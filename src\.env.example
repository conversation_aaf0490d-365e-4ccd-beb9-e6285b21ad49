# Environment variables for Bytspot

# Analytics Services
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
VITE_MIXPANEL_TOKEN=your_mixpanel_token_here
VITE_AMPLITUDE_API_KEY=your_amplitude_api_key_here
VITE_ANALYTICS_ENDPOINT=https://api.bytspot.com/analytics

# Error Tracking
VITE_SENTRY_DSN=https://<EMAIL>/project_id
VITE_ENVIRONMENT=development

# Push Notifications
VITE_VAPID_PUBLIC_KEY=your_vapid_public_key_here
VITE_PUSH_SUBSCRIPTION_ENDPOINT=https://api.bytspot.com/push/subscribe

# Social Media APIs (Optional - for enhanced sharing)
VITE_INSTAGRAM_CLIENT_ID=your_instagram_client_id
VITE_TWITTER_CLIENT_ID=your_twitter_client_id
VITE_TIKTOK_CLIENT_KEY=your_tiktok_client_key

# Google Services
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
VITE_GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# Firebase (Optional - for additional analytics and push)
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# App Configuration
VITE_APP_URL=https://app.bytspot.com
VITE_API_BASE_URL=https://api.bytspot.com
VITE_CDN_URL=https://cdn.bytspot.com

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_SOCIAL_SHARING=true
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_DEBUG_MODE=false