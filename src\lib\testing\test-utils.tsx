import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../auth/AuthProvider';
import { User, UserRole } from '../auth/types';

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  user?: Partial<User>;
}

// Create a test user factory
export const createTestUser = (overrides: Partial<User> = {}): User => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'consumer' as UserRole,
  permissions: [],
  preferences: {
    permissions: {
      gps: true,
      wifi: true,
      bluetooth: true,
      camera: true,
      notifications: true,
      imu: true,
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

// Custom render function that includes providers
function customRender(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { initialEntries = ['/'], user, ...renderOptions } = options;

  // Create a new QueryClient for each test
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <AuthProvider>
            {children}
          </AuthProvider>
        </BrowserRouter>
      </QueryClientProvider>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock auth service responses
export const mockAuthService = {
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  getCurrentUser: vi.fn(),
  refreshToken: vi.fn(),
  isAuthenticated: vi.fn(() => true),
  hasPermission: vi.fn(() => true),
  hasRole: vi.fn(() => true),
  getAccessToken: vi.fn(() => 'mock-token'),
};

// Mock API responses
export const mockApiResponse = <T>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
};

export const mockApiError = (message: string, status = 400) => {
  return Promise.reject(new Error(message));
};

// Mock fetch responses
export const mockFetchResponse = (data: any, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  } as Response);
};

// Mock location data
export const mockLocation = {
  latitude: 37.7749,
  longitude: -122.4194,
  accuracy: 10,
};

// Mock match data
export const createMockMatch = (overrides = {}) => ({
  id: 'mock-match-1',
  type: 'venue',
  title: 'Mock Venue',
  subtitle: 'Test Location',
  distance: '5 min walk',
  availability: 'Open now',
  rating: 4.5,
  image: 'https://example.com/image.jpg',
  details: {
    description: 'Mock venue description',
    features: ['Feature 1', 'Feature 2'],
    footprint: 100,
    vibe: 'Energetic',
  },
  ...overrides,
});

// Mock venue data
export const createMockVenue = (overrides = {}) => ({
  id: 'mock-venue-1',
  name: 'Mock Venue',
  address: '123 Test St, San Francisco, CA',
  rating: 4.5,
  priceLevel: 3,
  isOpen: true,
  currentCrowd: 50,
  maxCapacity: 100,
  ...overrides,
});

// Mock notification data
export const createMockNotification = (overrides = {}) => ({
  id: 'mock-notification-1',
  type: 'friend_at_venue',
  title: 'Mock Notification',
  message: 'This is a test notification',
  timestamp: new Date().toISOString(),
  ...overrides,
});

// Wait for loading states to complete
export const waitForLoadingToFinish = () => {
  return new Promise((resolve) => setTimeout(resolve, 0));
};

// Simulate user interactions
export const simulateSwipe = (element: HTMLElement, direction: 'left' | 'right') => {
  const startX = direction === 'left' ? 200 : 50;
  const endX = direction === 'left' ? 50 : 200;

  fireEvent.touchStart(element, {
    touches: [{ clientX: startX, clientY: 100 }],
  });

  fireEvent.touchMove(element, {
    touches: [{ clientX: endX, clientY: 100 }],
  });

  fireEvent.touchEnd(element, {
    changedTouches: [{ clientX: endX, clientY: 100 }],
  });
};

// Mock camera stream
export const mockCameraStream = {
  getTracks: () => [
    {
      stop: vi.fn(),
      getSettings: () => ({ width: 1280, height: 720 }),
    },
  ],
};

// Mock media recorder
export const mockMediaRecorder = {
  start: vi.fn(),
  stop: vi.fn(),
  ondataavailable: null,
  onstop: null,
  state: 'inactive',
};

// Test data generators
export const generateTestUsers = (count: number): User[] => {
  return Array.from({ length: count }, (_, index) =>
    createTestUser({
      id: `test-user-${index}`,
      email: `user${index}@example.com`,
      name: `Test User ${index}`,
    })
  );
};

export const generateTestMatches = (count: number) => {
  return Array.from({ length: count }, (_, index) =>
    createMockMatch({
      id: `mock-match-${index}`,
      title: `Mock Venue ${index}`,
    })
  );
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { customRender as render };

// Additional assertions
export const expectToBeVisible = (element: HTMLElement) => {
  expect(element).toBeInTheDocument();
  expect(element).toBeVisible();
};

export const expectToHaveLoadingState = (container: HTMLElement) => {
  expect(container.querySelector('[data-testid="loading"]')).toBeInTheDocument();
};

export const expectToHaveErrorState = (container: HTMLElement, message?: string) => {
  const errorElement = container.querySelector('[data-testid="error"]');
  expect(errorElement).toBeInTheDocument();
  if (message) {
    expect(errorElement).toHaveTextContent(message);
  }
};