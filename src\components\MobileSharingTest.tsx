import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { socialMediaService, type ShareData } from '../lib/services/socialMediaService';
import { mobileDetectionService } from '../lib/services/mobileDetectionService';
import { notificationService } from '../lib/services/notificationService';
import { 
  Smartphone, 
  Share, 
  Instagram, 
  Music, 
  Twitter, 
  Bell, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Vibrate,
  Clipboard,
  ExternalLink
} from 'lucide-react';

export function MobileSharingTest() {
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [sharingCapabilities, setSharingCapabilities] = useState<any>(null);
  const [testResults, setTestResults] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Get device information
    const info = mobileDetectionService.getDeviceInfo();
    setDeviceInfo(info);

    // Test sharing capabilities
    testCapabilities();
  }, []);

  const testCapabilities = async () => {
    setIsLoading(true);
    try {
      const capabilities = await socialMediaService.testSharingCapabilities();
      setSharingCapabilities(capabilities);
    } catch (error) {
      console.error('Failed to test capabilities:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testShare = async (platform: string) => {
    setIsLoading(true);
    
    const mockShareData: ShareData = {
      title: 'Testing Bytspot Sharing',
      text: 'Testing the amazing social sharing features of Bytspot! 🔥',
      venue: 'Test Venue',
      vibeScore: 9,
      hashtags: ['BytspotTest', 'SocialSharing', 'TestMode'],
      url: window.location.href
    };

    try {
      const success = await socialMediaService.share(platform, mockShareData);
      setTestResults(prev => ({
        ...prev,
        [platform]: {
          success,
          timestamp: new Date(),
          platform
        }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [platform]: {
          success: false,
          error: error.message,
          timestamp: new Date(),
          platform
        }
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const testNotification = async () => {
    try {
      await notificationService.requestPermission();
      await notificationService.simulateFriendActivity();
    } catch (error) {
      console.error('Notification test failed:', error);
    }
  };

  const testVibration = () => {
    const success = mobileDetectionService.vibrate([100, 50, 100, 50, 200]);
    setTestResults(prev => ({
      ...prev,
      vibration: {
        success,
        timestamp: new Date()
      }
    }));
  };

  const StatusIcon = ({ success }: { success?: boolean }) => {
    if (success === undefined) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    return success ? <CheckCircle className="w-4 h-4 text-green-500" /> : <XCircle className="w-4 h-4 text-red-500" />;
  };

  const platforms = socialMediaService.getAvailablePlatforms();

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="w-5 h-5" />
            Mobile Device Detection
          </CardTitle>
          <CardDescription>
            Device capabilities and platform detection results
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {deviceInfo && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Device Type</h4>
                <div className="space-y-1">
                  <Badge variant={deviceInfo.isMobile ? "default" : "secondary"}>
                    Mobile: {deviceInfo.isMobile ? "Yes" : "No"}
                  </Badge>
                  <Badge variant={deviceInfo.isTablet ? "default" : "secondary"}>
                    Tablet: {deviceInfo.isTablet ? "Yes" : "No"}
                  </Badge>
                  <Badge variant={deviceInfo.isDesktop ? "default" : "secondary"}>
                    Desktop: {deviceInfo.isDesktop ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Operating System</h4>
                <div className="space-y-1">
                  <Badge variant={deviceInfo.isIOS ? "default" : "secondary"}>
                    iOS: {deviceInfo.isIOS ? "Yes" : "No"}
                  </Badge>
                  <Badge variant={deviceInfo.isAndroid ? "default" : "secondary"}>
                    Android: {deviceInfo.isAndroid ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Browser Context</h4>
                <div className="space-y-1">
                  <Badge variant={deviceInfo.isInAppBrowser ? "destructive" : "default"}>
                    In-App: {deviceInfo.isInAppBrowser ? "Yes" : "No"}
                  </Badge>
                  <Badge variant={deviceInfo.isInstagramBrowser ? "default" : "secondary"}>
                    Instagram: {deviceInfo.isInstagramBrowser ? "Yes" : "No"}
                  </Badge>
                  <Badge variant={deviceInfo.isTikTokBrowser ? "default" : "secondary"}>
                    TikTok: {deviceInfo.isTikTokBrowser ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Capabilities</h4>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <StatusIcon success={deviceInfo.supportsWebShare} />
                    <span className="text-sm">Native Share</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <StatusIcon success={deviceInfo.supportsClipboard} />
                    <span className="text-sm">Clipboard</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <StatusIcon success={deviceInfo.supportsVibration} />
                    <span className="text-sm">Vibration</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share className="w-5 h-5" />
            Social Media Sharing Tests
          </CardTitle>
          <CardDescription>
            Test sharing capabilities across different platforms
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {platforms.map((platform) => (
              <Card key={platform.id} className="border-2">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {platform.id === 'instagram_story' || platform.id === 'instagram_post' ? (
                        <Instagram className="w-4 h-4" />
                      ) : platform.id === 'tiktok' ? (
                        <Music className="w-4 h-4" />
                      ) : platform.id === 'twitter' ? (
                        <Twitter className="w-4 h-4" />
                      ) : (
                        <Share className="w-4 h-4" />
                      )}
                      <span className="font-medium text-sm">{platform.name}</span>
                    </div>
                    {testResults[platform.id] && (
                      <StatusIcon success={testResults[platform.id].success} />
                    )}
                  </div>
                  
                  <Button
                    onClick={() => testShare(platform.id)}
                    disabled={isLoading}
                    size="sm"
                    className="w-full"
                  >
                    Test Share
                  </Button>
                  
                  {testResults[platform.id] && (
                    <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                      <div>Status: {testResults[platform.id].success ? 'Success' : 'Failed'}</div>
                      <div>Time: {testResults[platform.id].timestamp.toLocaleTimeString()}</div>
                      {testResults[platform.id].error && (
                        <div className="text-red-600">Error: {testResults[platform.id].error}</div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          <Separator />

          <div className="space-y-3">
            <h4 className="font-medium">Additional Tests</h4>
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={testNotification}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Bell className="w-4 h-4" />
                Test Notifications
              </Button>
              
              <Button
                onClick={testVibration}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={!deviceInfo?.supportsVibration}
              >
                <Vibrate className="w-4 h-4" />
                Test Vibration
              </Button>
              
              <Button
                onClick={async () => {
                  try {
                    await navigator.clipboard.writeText('Bytspot clipboard test! 📱');
                    setTestResults(prev => ({
                      ...prev,
                      clipboard: { success: true, timestamp: new Date() }
                    }));
                  } catch (error) {
                    setTestResults(prev => ({
                      ...prev,
                      clipboard: { success: false, timestamp: new Date() }
                    }));
                  }
                }}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={!deviceInfo?.supportsClipboard}
              >
                <Clipboard className="w-4 h-4" />
                Test Clipboard
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {sharingCapabilities && (
        <Card>
          <CardHeader>
            <CardTitle>Sharing Capabilities Report</CardTitle>
            <CardDescription>
              Detailed analysis of device sharing capabilities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Native Features</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <StatusIcon success={sharingCapabilities.nativeShare} />
                      <span className="text-sm">Native Web Share API</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <StatusIcon success={sharingCapabilities.clipboard} />
                      <span className="text-sm">Clipboard API</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Deep Links</h4>
                  <div className="space-y-2">
                    {Object.entries(sharingCapabilities.deepLinks).map(([platform, supported]) => (
                      <div key={platform} className="flex items-center gap-2">
                        <StatusIcon success={supported as boolean} />
                        <span className="text-sm capitalize">{platform}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">Debug Information</h4>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-48">
                  {JSON.stringify(sharingCapabilities.deviceInfo, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <h4 className="font-medium">For Mobile Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>Open this page on your mobile device</li>
              <li>Test each sharing platform to see how it behaves</li>
              <li>Check if deep links work correctly</li>
              <li>Verify that clipboard copying works</li>
              <li>Test notification permissions and display</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Expected Behavior:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>Instagram/TikTok should attempt to open native apps on mobile</li>
              <li>Content should be copied to clipboard as fallback</li>
              <li>Native share should work on supported platforms</li>
              <li>Vibration should work on mobile devices</li>
              <li>Instructions should appear for manual sharing when needed</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}