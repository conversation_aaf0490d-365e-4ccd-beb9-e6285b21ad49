import { analyticsService } from '../monitoring/analyticsService';
import { errorService } from '../monitoring/errorService';
import { mobileDetectionService } from './mobileDetectionService';

export interface NotificationData {
  id: string;
  type: NotificationType;
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  tag?: string;
  data?: Record<string, any>;
  actions?: NotificationAction[];
  timestamp: Date;
  read: boolean;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export type NotificationType = 
  | 'friend_tagged'
  | 'friend_joined'
  | 'friend_vibe_score'
  | 'challenge_progress'
  | 'challenge_completed'
  | 'challenge_new'
  | 'venue_recommendation'
  | 'referral_success'
  | 'achievement_unlocked'
  | 'social_milestone';

export interface FriendActivity {
  friendId: string;
  friendName: string;
  activity: string;
  venue?: string;
  vibeScore?: number;
  timestamp: Date;
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  progress: number;
  maxProgress: number;
  reward: number;
  deadline: Date;
}

class NotificationService {
  private registrationPromise: Promise<ServiceWorkerRegistration> | null = null;
  private notificationQueue: NotificationData[] = [];
  private isInitialized = false;
  private permissionStatus: NotificationPermission = 'default';
  private pushSubscription: PushSubscription | null = null;

  constructor() {
    this.checkPermissionStatus();
  }

  // Initialize the notification service
  async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) return true;

      // Check if notifications are supported
      if (!mobileDetectionService.hasNotificationSupport()) {
        console.warn('This browser does not support notifications');
        return false;
      }

      // Check if service workers are supported
      if (!('serviceWorker' in navigator)) {
        console.warn('This browser does not support service workers');
        return false;
      }

      // Register service worker for background notifications
      await this.registerServiceWorker();
      
      // Initialize push notifications if supported
      if (mobileDetectionService.hasPushNotificationSupport()) {
        await this.initializePushNotifications();
      }
      
      this.isInitialized = true;
      
      // Safe analytics tracking
      try {
        if (analyticsService && typeof analyticsService.track === 'function') {
          analyticsService.track('notification_service_initialized', {
            has_push_support: mobileDetectionService.hasPushNotificationSupport(),
            device_type: mobileDetectionService.isMobile() ? 'mobile' : 'desktop',
            is_ios: mobileDetectionService.isIOS(),
            is_android: mobileDetectionService.isAndroid()
          });
        }
      } catch (error) {
        console.warn('Analytics tracking failed in notification service:', error);
      }
      
      return true;
    } catch (error) {
      errorService.captureException(error as Error, {
        context: 'notification_service_init'
      });
      return false;
    }
  }

  // Initialize push notifications
  private async initializePushNotifications(): Promise<void> {
    try {
      const registration = await this.registrationPromise;
      if (!registration) return;

      // Check for existing subscription
      this.pushSubscription = await registration.pushManager.getSubscription();
      
      if (this.pushSubscription) {
        console.log('Existing push subscription found');
        // Send subscription to server
        await this.sendSubscriptionToServer(this.pushSubscription);
      }
    } catch (error) {
      console.warn('Failed to initialize push notifications:', error);
    }
  }

  // Subscribe to push notifications
  async subscribeToPush(): Promise<PushSubscription | null> {
    try {
      const registration = await this.registrationPromise;
      if (!registration) {
        throw new Error('Service worker not registered');
      }

      // Request permission first
      const permission = await this.requestPermission();
      if (permission !== 'granted') {
        return null;
      }

      // Generate VAPID keys (in production, this would come from your server)
      const vapidPublicKey = import.meta.env.VITE_VAPID_PUBLIC_KEY || 'demo-key';
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey)
      });

      this.pushSubscription = subscription;
      
      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      
      // Track analytics
      try {
        if (analyticsService && typeof analyticsService.track === 'function') {
          analyticsService.track('push_notification_subscribed', {
            device_type: mobileDetectionService.isMobile() ? 'mobile' : 'desktop'
          });
        }
      } catch (error) {
        console.warn('Analytics tracking failed for push subscription:', error);
      }

      return subscription;
    } catch (error) {
      errorService.captureException(error as Error, {
        context: 'push_notification_subscribe'
      });
      return null;
    }
  }

  // Unsubscribe from push notifications
  async unsubscribeFromPush(): Promise<boolean> {
    try {
      if (!this.pushSubscription) {
        return true;
      }

      const success = await this.pushSubscription.unsubscribe();
      
      if (success) {
        this.pushSubscription = null;
        // Notify server of unsubscription
        await this.removeSubscriptionFromServer();
      }

      return success;
    } catch (error) {
      errorService.captureException(error as Error, {
        context: 'push_notification_unsubscribe'
      });
      return false;
    }
  }

  // Helper function to convert VAPID key
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Send subscription to server
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    try {
      const endpoint = import.meta.env.VITE_PUSH_SUBSCRIPTION_ENDPOINT;
      if (!endpoint) {
        console.warn('Push subscription endpoint not configured');
        return;
      }

      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription,
          userAgent: navigator.userAgent,
          deviceInfo: mobileDetectionService.getDeviceInfo()
        }),
      });
    } catch (error) {
      console.warn('Failed to send subscription to server:', error);
    }
  }

  // Remove subscription from server
  private async removeSubscriptionFromServer(): Promise<void> {
    try {
      const endpoint = import.meta.env.VITE_PUSH_SUBSCRIPTION_ENDPOINT;
      if (!endpoint) return;

      await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.warn('Failed to remove subscription from server:', error);
    }
  }

  // Register service worker for background notifications
  private async registerServiceWorker(): Promise<void> {
    if (this.registrationPromise) {
      await this.registrationPromise;
      return;
    }

    this.registrationPromise = navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    });

    const registration = await this.registrationPromise;
    
    // Handle service worker updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            console.log('New service worker available');
          }
        });
      }
    });
  }

  // Check current permission status
  private checkPermissionStatus(): void {
    if ('Notification' in window) {
      this.permissionStatus = Notification.permission;
    }
  }

  // Request notification permission
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      return 'denied';
    }

    if (this.permissionStatus === 'granted') {
      return 'granted';
    }

    try {
      const permission = await Notification.requestPermission();
      this.permissionStatus = permission;
      
      // Safe analytics tracking
      try {
        if (analyticsService && typeof analyticsService.track === 'function') {
          analyticsService.track('notification_permission_requested', {
            result: permission
          });
        }
      } catch (error) {
        console.warn('Analytics tracking failed for permission request:', error);
      }

      if (permission === 'granted') {
        // Send welcome notification
        this.showLocalNotification({
          id: 'welcome',
          type: 'social_milestone',
          title: '🎉 Notifications Enabled!',
          body: 'You\'ll now get updates about friend activity, challenges, and more!',
          timestamp: new Date(),
          read: false
        });
      }

      return permission;
    } catch (error) {
      errorService.captureException(error as Error, {
        context: 'notification_permission_request'
      });
      return 'denied';
    }
  }

  // Show local notification
  async showLocalNotification(notification: NotificationData): Promise<void> {
    try {
      if (this.permissionStatus !== 'granted') {
        // Store in queue for when permission is granted
        this.notificationQueue.push(notification);
        return;
      }

      const options: NotificationOptions = {
        body: notification.body,
        icon: notification.icon || '/icons/bytspot-icon-192.png',
        badge: notification.badge || '/icons/bytspot-badge-72.png',
        image: notification.image,
        tag: notification.tag || notification.id,
        data: {
          ...notification.data,
          id: notification.id,
          type: notification.type,
          timestamp: notification.timestamp.toISOString()
        },
        actions: notification.actions,
        requireInteraction: notification.type === 'friend_tagged' || notification.type === 'challenge_completed',
        silent: false
      };

      // Use service worker for persistent notifications
      const registration = await this.registrationPromise;
      if (registration) {
        await registration.showNotification(notification.title, options);
      } else {
        // Fallback to direct notification
        new Notification(notification.title, options);
      }

      // Safe analytics tracking
      try {
        if (analyticsService && typeof analyticsService.track === 'function') {
          analyticsService.track('notification_shown', {
            type: notification.type,
            notification_id: notification.id
          });
        }
      } catch (error) {
        console.warn('Analytics tracking failed for notification shown:', error);
      }

      // Store notification for in-app display
      this.storeNotification(notification);

    } catch (error) {
      errorService.captureException(error as Error, {
        context: 'show_notification',
        notification_type: notification.type
      });
    }
  }

  // Store notification for in-app display
  private storeNotification(notification: NotificationData): void {
    const stored = this.getStoredNotifications();
    stored.unshift(notification);
    
    // Keep only last 50 notifications
    const trimmed = stored.slice(0, 50);
    localStorage.setItem('bytspot_notifications', JSON.stringify(trimmed));
    
    // Dispatch custom event for UI updates
    window.dispatchEvent(new CustomEvent('bytspot:notification', {
      detail: notification
    }));
  }

  // Get stored notifications
  getStoredNotifications(): NotificationData[] {
    try {
      const stored = localStorage.getItem('bytspot_notifications');
      if (!stored) return [];
      
      return JSON.parse(stored).map((n: any) => ({
        ...n,
        timestamp: new Date(n.timestamp)
      }));
    } catch {
      return [];
    }
  }

  // Mark notification as read
  markAsRead(notificationId: string): void {
    const notifications = this.getStoredNotifications();
    const updated = notifications.map(n => 
      n.id === notificationId ? { ...n, read: true } : n
    );
    localStorage.setItem('bytspot_notifications', JSON.stringify(updated));
    
    window.dispatchEvent(new CustomEvent('bytspot:notification_read', {
      detail: { notificationId }
    }));
  }

  // Get unread notification count
  getUnreadCount(): number {
    return this.getStoredNotifications().filter(n => !n.read).length;
  }

  // Clear all notifications
  clearAllNotifications(): void {
    localStorage.removeItem('bytspot_notifications');
    window.dispatchEvent(new CustomEvent('bytspot:notifications_cleared'));
  }

  // Friend activity notifications
  async notifyFriendTagged(friendName: string, venue: string, taggedBy: string): Promise<void> {
    await this.showLocalNotification({
      id: `friend_tagged_${Date.now()}`,
      type: 'friend_tagged',
      title: `📍 Tagged at ${venue}!`,
      body: `${taggedBy} tagged you at ${venue}. Check out their vibe!`,
      tag: 'friend_activity',
      data: { friendName, venue, taggedBy },
      actions: [
        { action: 'view_venue', title: 'View Venue' },
        { action: 'dismiss', title: 'Dismiss' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  async notifyFriendJoined(friendName: string, invitedBy?: string): Promise<void> {
    await this.showLocalNotification({
      id: `friend_joined_${Date.now()}`,
      type: 'friend_joined',
      title: `🎉 ${friendName} joined Bytspot!`,
      body: invitedBy 
        ? `${friendName} joined through your referral! You both earned bonus points.`
        : `${friendName} is now on Bytspot. Start discovering together!`,
      tag: 'friend_activity',
      data: { friendName, invitedBy },
      actions: [
        { action: 'view_profile', title: 'View Profile' },
        { action: 'send_message', title: 'Say Hi' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  async notifyFriendVibeScore(friendName: string, venue: string, vibeScore: number): Promise<void> {
    if (vibeScore < 8) return; // Only notify for high vibe scores

    await this.showLocalNotification({
      id: `friend_vibe_${Date.now()}`,
      type: 'friend_vibe_score',
      title: `⚡ ${friendName} found a ${vibeScore}/10 vibe!`,
      body: `${venue} is absolutely electric right now. Check it out!`,
      tag: 'friend_activity',
      data: { friendName, venue, vibeScore },
      actions: [
        { action: 'view_venue', title: 'View Venue' },
        { action: 'get_directions', title: 'Get Directions' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  // Challenge notifications
  async notifyChallengeProgress(challenge: Challenge): Promise<void> {
    const progressPercent = Math.round((challenge.progress / challenge.maxProgress) * 100);
    
    // Only notify at specific milestones
    if (![25, 50, 75, 90].includes(progressPercent)) return;

    await this.showLocalNotification({
      id: `challenge_progress_${challenge.id}`,
      type: 'challenge_progress',
      title: `🎯 ${progressPercent}% Complete!`,
      body: `You're ${progressPercent}% done with "${challenge.title}". Keep going for ${challenge.reward} points!`,
      tag: 'challenge_updates',
      data: { challengeId: challenge.id, progress: progressPercent },
      actions: [
        { action: 'view_challenge', title: 'View Challenge' },
        { action: 'find_venues', title: 'Find Venues' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  async notifyChallengeCompleted(challenge: Challenge): Promise<void> {
    await this.showLocalNotification({
      id: `challenge_completed_${challenge.id}`,
      type: 'challenge_completed',
      title: `🏆 Challenge Complete!`,
      body: `Amazing! You completed "${challenge.title}" and earned ${challenge.reward} points!`,
      tag: 'challenge_updates',
      data: { challengeId: challenge.id, reward: challenge.reward },
      actions: [
        { action: 'collect_reward', title: 'Collect Reward' },
        { action: 'share_achievement', title: 'Share' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  async notifyNewChallenge(challenge: Challenge): Promise<void> {
    await this.showLocalNotification({
      id: `new_challenge_${challenge.id}`,
      type: 'challenge_new',
      title: `🚀 New Challenge Available!`,
      body: `"${challenge.title}" - Earn ${challenge.reward} points by ${challenge.deadline.toLocaleDateString()}`,
      tag: 'challenge_updates',
      data: { challengeId: challenge.id },
      actions: [
        { action: 'start_challenge', title: 'Start Challenge' },
        { action: 'learn_more', title: 'Learn More' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  // Venue and social notifications
  async notifyVenueRecommendation(venue: string, reason: string): Promise<void> {
    await this.showLocalNotification({
      id: `venue_rec_${Date.now()}`,
      type: 'venue_recommendation',
      title: `✨ Perfect Match Found!`,
      body: `${venue} - ${reason}`,
      tag: 'recommendations',
      data: { venue, reason },
      actions: [
        { action: 'view_venue', title: 'Check It Out' },
        { action: 'get_directions', title: 'Directions' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  async notifyAchievementUnlocked(achievement: string, description: string): Promise<void> {
    await this.showLocalNotification({
      id: `achievement_${Date.now()}`,
      type: 'achievement_unlocked',
      title: `🏅 Achievement Unlocked!`,
      body: `"${achievement}" - ${description}`,
      tag: 'achievements',
      data: { achievement, description },
      actions: [
        { action: 'view_achievements', title: 'View All' },
        { action: 'share_achievement', title: 'Share' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  async notifyReferralSuccess(friendName: string, pointsEarned: number): Promise<void> {
    await this.showLocalNotification({
      id: `referral_${Date.now()}`,
      type: 'referral_success',
      title: `💰 Referral Bonus Earned!`,
      body: `${friendName} joined and you both earned ${pointsEarned} points!`,
      tag: 'referrals',
      data: { friendName, pointsEarned },
      actions: [
        { action: 'invite_more', title: 'Invite More' },
        { action: 'view_points', title: 'View Points' }
      ],
      timestamp: new Date(),
      read: false
    });
  }

  // Batch process queued notifications
  async processQueue(): Promise<void> {
    if (this.permissionStatus !== 'granted' || this.notificationQueue.length === 0) {
      return;
    }

    const queue = [...this.notificationQueue];
    this.notificationQueue = [];

    for (const notification of queue) {
      await this.showLocalNotification(notification);
      // Small delay to prevent overwhelming the user
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  // Check if notifications are supported and enabled
  isSupported(): boolean {
    return 'Notification' in window && 'serviceWorker' in navigator;
  }

  isEnabled(): boolean {
    return this.permissionStatus === 'granted';
  }

  // Get permission status
  getPermissionStatus(): NotificationPermission {
    return this.permissionStatus;
  }

  // Simulate friend activity (for development/demo)
  async simulateFriendActivity(): Promise<void> {
    const activities = [
      () => this.notifyFriendTagged('Sarah Martinez', 'Rooftop Lounge', 'Mike Chen'),
      () => this.notifyFriendVibeScore('Alex Rodriguez', 'Downtown Club', 9),
      () => this.notifyFriendJoined('Emma Wilson', 'your referral'),
      () => this.notifyVenueRecommendation('Sky Bar', 'Perfect for your vibe preferences'),
      () => this.notifyAchievementUnlocked('Social Butterfly', 'Tagged friends at 5 different venues')
    ];

    const randomActivity = activities[Math.floor(Math.random() * activities.length)];
    await randomActivity();
  }
}

export const notificationService = new NotificationService();
export default notificationService;