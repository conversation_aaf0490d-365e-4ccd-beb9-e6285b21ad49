import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';

interface ErrorContext {
  userId?: string;
  feature?: string;
  action?: string;
  metadata?: Record<string, any>;
}

class ErrorService {
  private static instance: ErrorService;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService();
    }
    return ErrorService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    // Prevent multiple concurrent initializations
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._performInitialization();
    return this.initializationPromise;
  }

  private async _performInitialization(): Promise<void> {
    try {
      const dsn = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_SENTRY_DSN) || null;
      const environment = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_ENVIRONMENT) || 'development';

      if (!dsn) {
        console.warn('Sentry DSN not provided. Error tracking disabled.');
        this.isInitialized = true;
        return;
      }

      Sentry.init({
        dsn,
        environment,
        integrations: [
          new BrowserTracing({
            // Performance monitoring
            tracePropagationTargets: [
              'localhost',
              /^https:\/\/api\.bytspot\.com/,
            ],
          }),
          new Sentry.Replay({
            // Session replay for debugging
            maskAllText: true,
            blockAllMedia: true,
          }),
        ],
        
        // Performance monitoring
        tracesSampleRate: environment === 'production' ? 0.1 : 1.0,
        
        // Session replay
        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0,
        
        // Error filtering
        beforeSend(event, hint) {
          // Filter out known non-critical errors
          const error = hint.originalException;
          
          if (error instanceof Error) {
            // Skip network errors that are not actionable
            if (error.message.includes('Network Error') && 
                error.message.includes('fetch')) {
              return null;
            }
            
            // Skip browser extension errors
            if (error.stack?.includes('extension://')) {
              return null;
            }
          }
          
          return event;
        },
        
        // Additional configuration
        maxBreadcrumbs: 50,
        debug: environment === 'development',
      });

      // Set initial user context
      this.setUserContext();
      
      this.isInitialized = true;
      console.log('Error tracking initialized');
    } catch (error) {
      console.error('Failed to initialize error service:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  captureError(error: Error, context?: ErrorContext): void {
    if (!this.isInitialized) {
      console.error('Error service not initialized:', error);
      return;
    }

    try {
      Sentry.withScope((scope) => {
        if (context) {
          scope.setContext('error_context', context);
          
          if (context.userId) {
            scope.setUser({ id: context.userId });
          }
          
          if (context.feature) {
            scope.setTag('feature', context.feature);
          }
          
          if (context.action) {
            scope.setTag('action', context.action);
          }
          
          if (context.metadata) {
            Object.entries(context.metadata).forEach(([key, value]) => {
              scope.setExtra(key, value);
            });
          }
        }
        
        Sentry.captureException(error);
      });
    } catch (captureError) {
      console.error('Failed to capture error:', captureError);
    }
    
    // Also log to console in development
    if ((typeof import.meta !== 'undefined' && import.meta.env?.DEV) || process.env.NODE_ENV === 'development') {
      console.error('Captured error:', error, context);
    }
  }

  // Alias for captureError to match expected interface
  captureException(error: Error, context?: ErrorContext): void {
    this.captureError(error, context);
  }

  captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context?: ErrorContext): void {
    if (!this.isInitialized) {
      console.log('Error service not initialized, logging message:', message);
      return;
    }

    Sentry.withScope((scope) => {
      scope.setLevel(level);
      
      if (context) {
        scope.setContext('message_context', context);
        
        if (context.feature) {
          scope.setTag('feature', context.feature);
        }
        
        if (context.action) {
          scope.setTag('action', context.action);
        }
      }
      
      Sentry.captureMessage(message);
    });
  }

  addBreadcrumb(message: string, category?: string, data?: Record<string, any>): void {
    if (!this.isInitialized) return;

    Sentry.addBreadcrumb({
      message,
      category: category || 'user-action',
      data,
      timestamp: Date.now() / 1000,
    });
  }

  setUserContext(userId?: string, email?: string, role?: string): void {
    if (!this.isInitialized) return;

    Sentry.setUser({
      id: userId,
      email,
      role,
    });
  }

  clearUserContext(): void {
    if (!this.isInitialized) return;

    Sentry.setUser(null);
  }

  setExtraContext(key: string, value: any): void {
    if (!this.isInitialized) return;

    Sentry.setExtra(key, value);
  }

  startTransaction(name: string, operation: string = 'navigation'): any {
    if (!this.isInitialized) return null;

    return Sentry.startTransaction({ name, op: operation });
  }

  // Utility method for wrapping async operations
  async wrapAsync<T>(
    operation: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      this.captureError(error as Error, context);
      throw error;
    }
  }

  // Utility method for wrapping sync operations
  wrapSync<T>(
    operation: () => T,
    context?: ErrorContext
  ): T {
    try {
      return operation();
    } catch (error) {
      this.captureError(error as Error, context);
      throw error;
    }
  }
}

export const errorService = ErrorService.getInstance();
export default errorService;

// React Error Boundary component
export const ErrorBoundary = Sentry.withErrorBoundary;

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorFallback?: React.ComponentType<any>
) {
  return Sentry.withErrorBoundary(Component, {
    fallback: errorFallback || (({ error, resetError }) => (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Something went wrong
          </h2>
          <p className="text-gray-600 mb-4">
            We're sorry, but something unexpected happened.
          </p>
          <button
            onClick={resetError}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md"
          >
            Try again
          </button>
        </div>
      </div>
    )),
    beforeCapture: (scope, error, errorInfo) => {
      scope.setTag('errorBoundary', true);
      scope.setContext('errorInfo', errorInfo);
    },
  });
}