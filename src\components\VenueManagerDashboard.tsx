import { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Activity,
  MapPin,
  Calendar,
  Zap,
  Eye,
  Star
} from 'lucide-react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Progress } from './ui/progress';

interface DashboardProps {
  venueName: string;
}

export function VenueManagerDashboard({ venueName = "Rooftop Lounge" }: DashboardProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [realTimeData, setRealTimeData] = useState({
    currentOccupancy: 127,
    maxCapacity: 200,
    waitTime: 15,
    avgSpendPerCustomer: 45,
    todayRevenue: 8650,
    weekRevenue: 52300,
    satisfaction: 4.8,
    activeRequests: 12
  });

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      // Simulate real-time data updates
      setRealTimeData(prev => ({
        ...prev,
        currentOccupancy: Math.max(50, Math.min(200, prev.currentOccupancy + Math.floor(Math.random() * 10 - 5))),
        activeRequests: Math.max(0, prev.activeRequests + Math.floor(Math.random() * 3 - 1))
      }));
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const occupancyPercentage = Math.round((realTimeData.currentOccupancy / realTimeData.maxCapacity) * 100);
  const getOccupancyStatus = () => {
    if (occupancyPercentage < 40) return { color: 'text-green-600', bg: 'bg-green-100', label: 'Low' };
    if (occupancyPercentage < 70) return { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Moderate' };
    if (occupancyPercentage < 90) return { color: 'text-orange-600', bg: 'bg-orange-100', label: 'High' };
    return { color: 'text-red-600', bg: 'bg-red-100', label: 'At Capacity' };
  };

  const status = getOccupancyStatus();

  const alerts = [
    { id: 1, type: 'warning', message: 'High demand detected - consider opening rooftop section', time: '2 min ago' },
    { id: 2, type: 'info', message: 'New Bytspot booking request from Premium user', time: '5 min ago' },
    { id: 3, type: 'success', message: 'Staff member completed VIP table setup', time: '12 min ago' }
  ];

  const recentActivity = [
    { id: 1, action: 'Table reservation', customer: 'Sarah M.', time: '2:30 PM', status: 'confirmed' },
    { id: 2, action: 'Valet request', customer: 'Mike R.', time: '2:25 PM', status: 'in-progress' },
    { id: 3, action: 'VIP upgrade', customer: 'Emma L.', time: '2:20 PM', status: 'completed' },
    { id: 4, action: 'Event booking', customer: 'David K.', time: '2:15 PM', status: 'pending' }
  ];

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">{venueName}</h1>
            <p className="text-gray-600">Venue Manager Dashboard</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-gray-800">
              {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </p>
            <p className="text-gray-600">
              {currentTime.toLocaleDateString([], { weekday: 'long', month: 'short', day: 'numeric' })}
            </p>
          </div>
        </div>

        {/* Key Metrics Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Current Occupancy */}
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Users className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-gray-700">Live Occupancy</span>
              </div>
              <Badge className={`${status.bg} ${status.color} border-0`}>
                {status.label}
              </Badge>
            </div>
            <div className="space-y-3">
              <div>
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl font-bold text-gray-800">{realTimeData.currentOccupancy}</span>
                  <span className="text-gray-600">/ {realTimeData.maxCapacity}</span>
                </div>
                <p className="text-sm text-gray-600">people inside</p>
              </div>
              <Progress value={occupancyPercentage} className="h-2" />
              <p className="text-sm text-gray-600">{occupancyPercentage}% capacity</p>
            </div>
          </Card>

          {/* Revenue Today */}
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-2 mb-4">
              <DollarSign className="w-5 h-5 text-green-600" />
              <span className="font-medium text-gray-700">Today's Revenue</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-gray-800">${realTimeData.todayRevenue.toLocaleString()}</span>
                <TrendingUp className="w-4 h-4 text-green-500" />
              </div>
              <p className="text-sm text-green-600">+15% vs yesterday</p>
              <p className="text-xs text-gray-600">Avg: ${realTimeData.avgSpendPerCustomer} per customer</p>
            </div>
          </Card>

          {/* Active Requests */}
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Activity className="w-5 h-5 text-purple-600" />
              <span className="font-medium text-gray-700">Active Requests</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-gray-800">{realTimeData.activeRequests}</span>
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ repeat: Infinity, duration: 2 }}
                  className="w-2 h-2 bg-red-500 rounded-full"
                />
              </div>
              <p className="text-sm text-orange-600">3 urgent</p>
              <p className="text-xs text-gray-600">Avg response: {realTimeData.waitTime} min</p>
            </div>
          </Card>

          {/* Customer Satisfaction */}
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Star className="w-5 h-5 text-yellow-600" />
              <span className="font-medium text-gray-700">Satisfaction</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-gray-800">{realTimeData.satisfaction}</span>
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(realTimeData.satisfaction) 
                          ? 'text-yellow-400 fill-current' 
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
              <p className="text-sm text-green-600">+0.2 this week</p>
              <p className="text-xs text-gray-600">Based on 156 reviews</p>
            </div>
          </Card>
        </div>

        {/* Alerts & Activity Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Alerts */}
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Recent Alerts</h3>
              <Badge variant="outline" className="bg-white/20 border-white/30">
                {alerts.length} active
              </Badge>
            </div>
            <div className="space-y-4">
              {alerts.map((alert) => (
                <div key={alert.id} className="flex items-start gap-3 p-3 rounded-lg bg-white/30">
                  <div className={`mt-0.5 ${
                    alert.type === 'warning' ? 'text-orange-500' : 
                    alert.type === 'info' ? 'text-blue-500' : 'text-green-500'
                  }`}>
                    {alert.type === 'warning' ? <AlertTriangle className="w-4 h-4" /> : 
                     alert.type === 'info' ? <Activity className="w-4 h-4" /> : 
                     <CheckCircle className="w-4 h-4" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-700">{alert.message}</p>
                    <p className="text-xs text-gray-500 mt-1">{alert.time}</p>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4 bg-white/20 border-white/30 hover:bg-white/30">
              View All Alerts
            </Button>
          </Card>

          {/* Recent Activity */}
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Recent Activity</h3>
              <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                <Eye className="w-4 h-4 mr-1" />
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 rounded-lg bg-white/30">
                  <div className="flex-1">
                    <p className="font-medium text-gray-800">{activity.action}</p>
                    <p className="text-sm text-gray-600">{activity.customer} • {activity.time}</p>
                  </div>
                  <Badge
                    variant="outline"
                    className={`${
                      activity.status === 'completed' ? 'bg-green-100 text-green-700 border-green-300' :
                      activity.status === 'in-progress' ? 'bg-blue-100 text-blue-700 border-blue-300' :
                      activity.status === 'confirmed' ? 'bg-purple-100 text-purple-700 border-purple-300' :
                      'bg-yellow-100 text-yellow-700 border-yellow-300'
                    }`}
                  >
                    {activity.status}
                  </Badge>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
              <Users className="w-4 h-4 mr-2" />
              Manage Staff
            </Button>
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <Calendar className="w-4 h-4 mr-2" />
              View Bookings
            </Button>
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <Zap className="w-4 h-4 mr-2" />
              Boost Visibility
            </Button>
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <MapPin className="w-4 h-4 mr-2" />
              Update Capacity
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}