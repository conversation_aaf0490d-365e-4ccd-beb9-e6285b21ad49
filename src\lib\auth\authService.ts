import { 
  User, 
  LoginCredentials, 
  RegisterCredentials, 
  AuthTokens, 
  AuthResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  MFASetupResponse,
  MFAVerification
} from './types';

class AuthService {
  private static instance: AuthService;
  private tokens: AuthTokens | null = null;

  private get baseURL(): string {
    return (typeof import.meta !== 'undefined' && import.meta.env?.VITE_API_URL) || 'https://api.bytspot.com';
  }

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  constructor() {
    console.log('AuthService: Constructor called');
    this.loadTokensFromStorage();
    this.setupTokenRefresh();
  }

  private loadTokensFromStorage(): void {
    try {
      const tokens = localStorage.getItem('bytspot_tokens');
      if (tokens) {
        this.tokens = JSON.parse(tokens);
        
        // Check if tokens are expired
        if (this.tokens && this.tokens.expiresAt <= Date.now()) {
          this.clearTokens();
        }
      }
    } catch (error) {
      console.error('Failed to load tokens from storage:', error);
      this.clearTokens();
    }
  }

  private saveTokensToStorage(tokens: AuthTokens): void {
    try {
      localStorage.setItem('bytspot_tokens', JSON.stringify(tokens));
      this.tokens = tokens;
    } catch (error) {
      console.error('Failed to save tokens to storage:', error);
    }
  }

  private clearTokens(): void {
    localStorage.removeItem('bytspot_tokens');
    this.tokens = null;
  }

  private setupTokenRefresh(): void {
    // Refresh token 5 minutes before expiry
    setInterval(() => {
      if (this.tokens && this.tokens.expiresAt - Date.now() < 5 * 60 * 1000) {
        this.refreshToken().catch(console.error);
      }
    }, 60 * 1000); // Check every minute
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // For development/demo mode, return mock authentication
    const isDev = (typeof import.meta !== 'undefined' && import.meta.env?.DEV) || 
                  (typeof import.meta !== 'undefined' && !import.meta.env?.VITE_API_URL);
    
    if (isDev) {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTokens: AuthTokens = {
        accessToken: 'demo-access-token-' + Date.now(),
        refreshToken: 'demo-refresh-token-' + Date.now(),
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };
      
      const mockUser: User = {
        id: 'demo-user-123',
        email: credentials.email,
        name: 'Demo User',
        role: 'user',
        isEmailVerified: true,
        isMFAEnabled: false,
        permissions: [
          {
            resource: 'venues',
            actions: ['view', 'search']
          },
          {
            resource: 'social',
            actions: ['view', 'share', 'invite']
          }
        ],
        profile: {
          preferences: {
            vibeTypes: ['energetic', 'trendy'],
            musicGenres: ['electronic', 'pop'],
            priceRange: 'mid'
          }
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: new Date().toISOString()
      };
      
      this.saveTokensToStorage(mockTokens);
      return { user: mockUser, tokens: mockTokens };
    }

    try {
      const response = await fetch(`${this.baseURL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Login failed');
      }

      const authResponse: AuthResponse = await response.json();
      this.saveTokensToStorage(authResponse.tokens);
      
      // Track login event
      this.trackEvent('user_login', {
        userId: authResponse.user.id,
        role: authResponse.user.role,
        timestamp: new Date().toISOString()
      });

      return authResponse;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    try {
      const response = await fetch(`${this.baseURL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Registration failed');
      }

      const authResponse: AuthResponse = await response.json();
      this.saveTokensToStorage(authResponse.tokens);
      
      // Track registration event
      this.trackEvent('user_register', {
        userId: authResponse.user.id,
        role: authResponse.user.role,
        timestamp: new Date().toISOString()
      });

      return authResponse;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.tokens) {
        await fetch(`${this.baseURL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.tokens.accessToken}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearTokens();
      // Track logout event
      this.trackEvent('user_logout', {
        timestamp: new Date().toISOString()
      });
    }
  }

  async refreshToken(): Promise<AuthTokens> {
    if (!this.tokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken: this.tokens.refreshToken
        }),
      });

      if (!response.ok) {
        this.clearTokens();
        throw new Error('Token refresh failed');
      }

      const tokens: AuthTokens = await response.json();
      this.saveTokensToStorage(tokens);
      return tokens;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.clearTokens();
      throw error;
    }
  }

  async getCurrentUser(): Promise<User | null> {
    console.log('getCurrentUser called, tokens:', !!this.tokens);
    
    if (!this.tokens) {
      console.log('No tokens available');
      return null;
    }

    // For development/demo mode, return a mock user if we have tokens
    const isDev = (typeof import.meta !== 'undefined' && import.meta.env?.DEV) || 
                  (typeof import.meta !== 'undefined' && !import.meta.env?.VITE_API_URL);
    
    if (isDev) {
      console.log('Development mode - returning mock user');
      // Return a mock user for development
      return {
        id: 'demo-user-123',
        email: '<EMAIL>',
        name: 'Demo User',
        role: 'user',
        isEmailVerified: true,
        isMFAEnabled: false,
        permissions: [
          {
            resource: 'venues',
            actions: ['view', 'search']
          },
          {
            resource: 'social',
            actions: ['view', 'share', 'invite']
          }
        ],
        profile: {
          preferences: {
            vibeTypes: ['energetic', 'trendy'],
            musicGenres: ['electronic', 'pop'],
            priceRange: 'mid'
          }
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: new Date().toISOString()
      };
    }

    try {
      const response = await fetch(`${this.baseURL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${this.tokens.accessToken}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          this.clearTokens();
        }
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  async requestPasswordReset(request: PasswordResetRequest): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/auth/password-reset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Password reset request failed');
      }
    } catch (error) {
      console.error('Password reset request error:', error);
      throw error;
    }
  }

  async confirmPasswordReset(confirm: PasswordResetConfirm): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/auth/password-reset/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(confirm),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Password reset confirmation failed');
      }
    } catch (error) {
      console.error('Password reset confirmation error:', error);
      throw error;
    }
  }

  async setupMFA(): Promise<MFASetupResponse> {
    if (!this.tokens) {
      throw new Error('Authentication required');
    }

    try {
      const response = await fetch(`${this.baseURL}/auth/mfa/setup`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.tokens.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'MFA setup failed');
      }

      return await response.json();
    } catch (error) {
      console.error('MFA setup error:', error);
      throw error;
    }
  }

  async verifyMFA(verification: MFAVerification): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/auth/mfa/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(verification),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'MFA verification failed');
      }
    } catch (error) {
      console.error('MFA verification error:', error);
      throw error;
    }
  }

  getAccessToken(): string | null {
    return this.tokens?.accessToken || null;
  }

  isAuthenticated(): boolean {
    const isAuth = !!(this.tokens && this.tokens.expiresAt > Date.now());
    console.log('AuthService: isAuthenticated check:', isAuth, 'tokens:', !!this.tokens);
    return isAuth;
  }

  hasPermission(resource: string, action: string): boolean {
    // This would check against user permissions
    // For now, return true for authenticated users
    return this.isAuthenticated();
  }

  hasRole(role: string): boolean {
    // This would check user role
    // Implementation depends on current user context
    return true;
  }

  private trackEvent(event: string, properties: Record<string, any>): void {
    // Integration with analytics service
    if (typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.track(event, properties);
    }
  }
}

export const authService = AuthService.getInstance();
export default authService;