export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserR<PERSON>;
  permissions: Permission[];
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  parkingType?: string;
  venueStyle?: string;
  budget?: string;
  distance?: string;
  vibePreferences?: {
    atmosphere: string[];
    musicStyle: string[];
    crowdEnergy: number;
    isPremium: boolean;
  };
  permissions: {
    gps: boolean;
    wifi: boolean;
    bluetooth: boolean;
    camera: boolean;
    notifications: boolean;
    imu: boolean;
  };
}

export type UserRole = 'consumer' | 'host' | 'admin' | 'super_admin' | 'venue_manager';

export interface Permission {
  resource: string;
  actions: string[];
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  name: string;
  role: User<PERSON>ole;
  address?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
}

export interface MFASetupResponse {
  qrCode: string;
  secret: string;
  backupCodes: string[];
}

export interface MFAVerification {
  token: string;
  code: string;
}