// Service Initializer - Registers all services with the service manager
import { registerService, initializeAllServices } from './serviceManager';
import { analyticsService } from '../monitoring/analyticsService';
import { errorService } from '../monitoring/errorService';
import { setServiceCallbacks } from '../store/safeStore';

export async function initializeBytspotServices(): Promise<void> {
  console.log('🎯 Registering Bytspot services...');

  // Register services in dependency order
  registerService({
    name: 'error',
    initialize: () => errorService.initialize(),
    dependencies: [], // No dependencies
  });

  registerService({
    name: 'analytics', 
    initialize: () => analyticsService.initialize(),
    dependencies: ['error'], // Depends on error service for error tracking
  });

  registerService({
    name: 'store',
    initialize: () => {
      // Set up store callbacks after services are initialized
      setServiceCallbacks(analyticsService, errorService);
    },
    dependencies: ['error', 'analytics'], // Depends on both services
  });

  // Initialize all services
  await initializeAllServices();
  
  console.log('✨ Bytspot services initialization complete');
}