import { motion } from 'motion/react';
import { 
  <PERSON><PERSON>hart<PERSON>, 
  <PERSON>, 
  ClipboardList, 
  Settings,
  LogOut,
  ToggleLeft,
  Bell,
  HelpCircle,
  Calendar,
  CreditCard,
  Sliders,
  Zap
} from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';

type B2BView = 'dashboard' | 'analytics' | 'requests' | 'management' | 'scheduling' | 'payments' | 'sponsored' | 'settings';

interface B2BNavigationProps {
  currentView: B2BView;
  onViewChange: (view: B2BView) => void;
  onToggleMode: () => void;
  notificationCount?: number;
}

export function B2BNavigation({ 
  currentView, 
  onViewChange, 
  onToggleMode,
  notificationCount = 0 
}: B2BNavigationProps) {
  const navItems = [
    {
      id: 'dashboard' as B2BView,
      label: 'Dashboard',
      icon: BarChart3,
      color: 'text-blue-600',
      activeColor: 'text-blue-600'
    },
    {
      id: 'analytics' as B2BView,
      label: 'Analytics',
      icon: BarChart3,
      color: 'text-purple-600',
      activeColor: 'text-purple-600'
    },
    {
      id: 'requests' as B2BView,
      label: 'Requests',
      icon: ClipboardList,
      color: 'text-green-600',
      activeColor: 'text-green-600',
      badge: notificationCount > 0 ? notificationCount : undefined
    },
    {
      id: 'sponsored' as B2BView,
      label: 'Sponsored',
      icon: Zap,
      color: 'text-yellow-600',
      activeColor: 'text-yellow-600'
    },
    {
      id: 'scheduling' as B2BView,
      label: 'Schedule',
      icon: Calendar,
      color: 'text-indigo-600',
      activeColor: 'text-indigo-600'
    },
    {
      id: 'payments' as B2BView,
      label: 'Payments',
      icon: CreditCard,
      color: 'text-emerald-600',
      activeColor: 'text-emerald-600'
    }
  ];

  return (
    <>
      {/* Top Header Bar */}
      <div className="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-white/30 border-b border-white/40">
        <div className="flex items-center justify-between px-6 py-3">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-bold">B</span>
              </div>
              <span className="font-semibold text-gray-800">Bytspot Manager</span>
            </div>
            <Badge className="bg-blue-100 text-blue-800 border-0">
              B2B Portal
            </Badge>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={onToggleMode}
              className="bg-white/20 border-white/30 hover:bg-white/40 flex items-center gap-2"
            >
              <ToggleLeft className="w-4 h-4" />
              Switch to Consumer
            </Button>
            
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-4 h-4" />
              {notificationCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {notificationCount > 9 ? '9+' : notificationCount}
                </span>
              )}
            </Button>
            
            <Button variant="ghost" size="sm">
              <HelpCircle className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50 backdrop-blur-xl bg-white/30 border-t border-white/40">
        <div className="flex items-center justify-around px-4 py-3">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;
            
            return (
              <Button
                key={item.id}
                variant="ghost"
                className={`relative flex flex-col items-center gap-1 h-auto py-2 px-3 rounded-xl transition-all duration-200 ${
                  isActive 
                    ? 'bg-white/40 shadow-sm' 
                    : 'hover:bg-white/20'
                }`}
                onClick={() => onViewChange(item.id)}
              >
                <div className="relative">
                  <Icon 
                    className={`w-5 h-5 transition-colors ${
                      isActive ? item.activeColor : 'text-gray-600'
                    }`} 
                  />
                  {item.badge && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
                    >
                      {item.badge > 9 ? '9+' : item.badge}
                    </motion.div>
                  )}
                </div>
                <span className={`text-xs transition-colors ${
                  isActive ? 'text-gray-800 font-medium' : 'text-gray-600'
                }`}>
                  {item.label}
                </span>
                
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-gradient-to-r from-purple-600 to-blue-600"
                  />
                )}
              </Button>
            );
          })}
        </div>
      </div>
    </>
  );
}