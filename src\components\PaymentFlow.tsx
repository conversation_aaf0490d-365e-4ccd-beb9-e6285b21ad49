import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  CreditCard, 
  Star, 
  Shield, 
  CheckCircle, 
  ArrowLeft,
  Wallet,
  Gift,
  Zap,
  Clock,
  MapPin,
  User,
  Plus,
  Minus,
  Info,
  Lock,
  Smartphone
} from 'lucide-react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Separator } from './ui/separator';
import { Switch } from './ui/switch';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';
import { Label } from './ui/label';
import { Progress } from './ui/progress';

interface PaymentFlowProps {
  service: {
    id: string;
    type: 'parking' | 'venue' | 'valet';
    title: string;
    subtitle: string;
    basePrice: number;
    location: string;
    features: string[];
  };
  onBack: () => void;
  onComplete: (paymentDetails: any) => void;
}

interface UserPoints {
  balance: number;
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  earned: number;
  toNextTier: number;
}

interface PaymentMethod {
  id: string;
  type: 'card' | 'digital_wallet';
  name: string;
  last4?: string;
  brand?: string;
  icon: string;
}

export function PaymentFlow({ service, onBack, onComplete }: PaymentFlowProps) {
  const [step, setStep] = useState<'summary' | 'payment' | 'processing' | 'success'>('summary');
  const [paymentType, setPaymentType] = useState<'card' | 'points' | 'mixed'>('card');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [pointsToUse, setPointsToUse] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [addTip, setAddTip] = useState(false);
  const [tipAmount, setTipAmount] = useState(0);
  const [promoCode, setPromoCode] = useState('');
  const [promoApplied, setPromoApplied] = useState(false);

  // Mock user data
  const userPoints: UserPoints = {
    balance: 2450,
    tier: 'Gold',
    earned: 15240,
    toNextTier: 760
  };

  const paymentMethods: PaymentMethod[] = [
    {
      id: '1',
      type: 'card',
      name: 'Chase Sapphire',
      last4: '4242',
      brand: 'Visa',
      icon: '💳'
    },
    {
      id: '2',
      type: 'card',
      name: 'Amex Gold',
      last4: '8431',
      brand: 'Amex',
      icon: '💳'
    },
    {
      id: '3',
      type: 'digital_wallet',
      name: 'Apple Pay',
      icon: '📱'
    },
    {
      id: '4',
      type: 'digital_wallet',
      name: 'Google Pay',
      icon: '📱'
    }
  ];

  const baseTotal = service.basePrice * quantity;
  const tipTotal = addTip ? tipAmount : 0;
  const promoDiscount = promoApplied ? baseTotal * 0.15 : 0;
  const subtotal = baseTotal + tipTotal - promoDiscount;
  const pointsValue = pointsToUse * 0.01; // 1 point = $0.01
  const finalTotal = Math.max(0, subtotal - pointsValue);
  const pointsEarned = Math.floor(finalTotal * 10); // 10 points per dollar

  const maxPointsUsable = Math.min(userPoints.balance, Math.floor(subtotal * 100));

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'bg-gradient-to-r from-amber-600 to-amber-500';
      case 'Silver': return 'bg-gradient-to-r from-gray-400 to-gray-500';
      case 'Gold': return 'bg-gradient-to-r from-yellow-400 to-yellow-500';
      case 'Platinum': return 'bg-gradient-to-r from-purple-400 to-purple-500';
      default: return 'bg-gradient-to-r from-gray-400 to-gray-500';
    }
  };

  const handlePayment = async () => {
    setStep('processing');
    
    // Simulate payment processing
    setTimeout(() => {
      setStep('success');
      setTimeout(() => {
        onComplete({
          paymentType,
          amount: finalTotal,
          pointsUsed: pointsToUse,
          pointsEarned,
          method: selectedPaymentMethod
        });
      }, 2000);
    }, 2000);
  };

  const renderSummary = () => (
    <div className="space-y-6">
      {/* Service Details */}
      <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
        <div className="flex items-start gap-4">
          <div className="p-3 bg-white/30 rounded-lg">
            {service.type === 'parking' && <CreditCard className="w-6 h-6 text-blue-600" />}
            {service.type === 'venue' && <MapPin className="w-6 h-6 text-purple-600" />}
            {service.type === 'valet' && <User className="w-6 h-6 text-green-600" />}
          </div>
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-800">{service.title}</h2>
            <p className="text-gray-600">{service.subtitle}</p>
            <p className="text-sm text-gray-500 mt-1">{service.location}</p>
            
            <div className="flex flex-wrap gap-2 mt-3">
              {service.features.slice(0, 3).map((feature, index) => (
                <Badge key={index} variant="outline" className="bg-white/30 border-white/40">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-gray-800">${service.basePrice}</p>
            <p className="text-sm text-gray-600">per {service.type === 'parking' ? 'hour' : 'service'}</p>
          </div>
        </div>
      </Card>

      {/* Quantity & Options */}
      {service.type === 'parking' && (
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <h3 className="font-semibold text-gray-800 mb-4">Duration</h3>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="bg-white/20 border-white/30 hover:bg-white/30"
              >
                <Minus className="w-4 h-4" />
              </Button>
              <span className="text-lg font-medium text-gray-800 min-w-[3rem] text-center">
                {quantity} hour{quantity > 1 ? 's' : ''}
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setQuantity(quantity + 1)}
                className="bg-white/20 border-white/30 hover:bg-white/30"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <div className="text-right">
              <p className="text-lg font-semibold text-gray-800">${baseTotal}</p>
            </div>
          </div>
        </Card>
      )}

      {/* Tip Option for Valet */}
      {service.type === 'valet' && (
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-800">Add Tip</h3>
            <Switch checked={addTip} onCheckedChange={setAddTip} />
          </div>
          {addTip && (
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-2">
                {[15, 18, 20].map((percent) => (
                  <Button
                    key={percent}
                    size="sm"
                    variant="outline"
                    onClick={() => setTipAmount(Math.round(baseTotal * (percent / 100) * 100) / 100)}
                    className={`${tipAmount === Math.round(baseTotal * (percent / 100) * 100) / 100
                      ? 'bg-purple-100 border-purple-300 text-purple-800'
                      : 'bg-white/20 border-white/30 hover:bg-white/30'}`}
                  >
                    {percent}%
                  </Button>
                ))}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-700">$</span>
                <Input
                  type="number"
                  value={tipAmount}
                  onChange={(e) => setTipAmount(parseFloat(e.target.value) || 0)}
                  className="bg-white/50 border-white/60"
                  placeholder="Custom amount"
                />
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Promo Code */}
      <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
        <h3 className="font-semibold text-gray-800 mb-4">Promo Code</h3>
        <div className="flex gap-2">
          <Input
            value={promoCode}
            onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
            placeholder="Enter promo code"
            className="bg-white/50 border-white/60"
          />
          <Button
            variant="outline"
            onClick={() => {
              if (promoCode === 'WELCOME15') {
                setPromoApplied(true);
              }
            }}
            className="bg-white/20 border-white/30 hover:bg-white/30"
          >
            Apply
          </Button>
        </div>
        {promoApplied && (
          <div className="mt-2 flex items-center gap-2 text-green-700">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm">WELCOME15 applied - 15% off!</span>
          </div>
        )}
      </Card>

      {/* Price Summary */}
      <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
        <h3 className="font-semibold text-gray-800 mb-4">Price Summary</h3>
        <div className="space-y-2">
          <div className="flex justify-between text-gray-700">
            <span>{service.type === 'parking' ? `${quantity} hour${quantity > 1 ? 's' : ''}` : 'Service'}</span>
            <span>${baseTotal.toFixed(2)}</span>
          </div>
          {tipTotal > 0 && (
            <div className="flex justify-between text-gray-700">
              <span>Tip</span>
              <span>${tipTotal.toFixed(2)}</span>
            </div>
          )}
          {promoApplied && (
            <div className="flex justify-between text-green-700">
              <span>Promo Discount</span>
              <span>-${promoDiscount.toFixed(2)}</span>
            </div>
          )}
          <Separator />
          <div className="flex justify-between font-semibold text-gray-800">
            <span>Total</span>
            <span>${subtotal.toFixed(2)}</span>
          </div>
        </div>
      </Card>

      <Button
        onClick={() => setStep('payment')}
        className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-6"
      >
        Continue to Payment
      </Button>
    </div>
  );

  const renderPayment = () => (
    <div className="space-y-6">
      {/* Bytspot Points */}
      <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${getTierColor(userPoints.tier)} text-white`}>
              <Star className="w-5 h-5" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">Bytspot Points</h3>
              <p className="text-sm text-gray-600">{userPoints.tier} Member</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-lg font-bold text-gray-800">{userPoints.balance.toLocaleString()}</p>
            <p className="text-xs text-gray-600">points available</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <Switch
              checked={paymentType === 'points' || paymentType === 'mixed'}
              onCheckedChange={(checked) => {
                if (checked) {
                  setPaymentType('points');
                  setPointsToUse(maxPointsUsable);
                } else {
                  setPaymentType('card');
                  setPointsToUse(0);
                }
              }}
            />
            <span className="text-sm text-gray-700">Use Bytspot Points</span>
          </div>

          {(paymentType === 'points' || paymentType === 'mixed') && (
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600">Points to use</span>
                  <span className="text-gray-800">{pointsToUse.toLocaleString()} = ${pointsValue.toFixed(2)}</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max={maxPointsUsable}
                  step="100"
                  value={pointsToUse}
                  onChange={(e) => {
                    const points = parseInt(e.target.value);
                    setPointsToUse(points);
                    setPaymentType(points === maxPointsUsable && maxPointsUsable >= subtotal * 100 ? 'points' : 'mixed');
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
              
              {pointsToUse < maxPointsUsable && (
                <div className="p-3 bg-blue-50/60 rounded-lg border border-blue-200/60">
                  <div className="flex items-center gap-2 text-blue-800">
                    <Info className="w-4 h-4" />
                    <span className="text-sm">
                      Remaining ${(subtotal - pointsValue).toFixed(2)} will be charged to your selected payment method
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>

      {/* Payment Methods */}
      {(paymentType === 'card' || paymentType === 'mixed') && (
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <h3 className="font-semibold text-gray-800 mb-4">Payment Method</h3>
          <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <div key={method.id} className="flex items-center space-x-3 p-3 rounded-lg border border-white/30 hover:bg-white/20">
                  <RadioGroupItem value={method.id} id={method.id} />
                  <Label htmlFor={method.id} className="flex items-center gap-3 flex-1 cursor-pointer">
                    <span className="text-lg">{method.icon}</span>
                    <div>
                      <p className="font-medium text-gray-800">{method.name}</p>
                      {method.last4 && (
                        <p className="text-sm text-gray-600">•••• •••• •••• {method.last4}</p>
                      )}
                    </div>
                  </Label>
                  {method.brand && (
                    <Badge variant="outline" className="bg-white/30 border-white/40">
                      {method.brand}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </RadioGroup>
        </Card>
      )}

      {/* Final Total */}
      <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
        <div className="space-y-3">
          <div className="flex justify-between text-gray-700">
            <span>Subtotal</span>
            <span>${subtotal.toFixed(2)}</span>
          </div>
          {pointsToUse > 0 && (
            <div className="flex justify-between text-purple-700">
              <span>Points Used ({pointsToUse.toLocaleString()})</span>
              <span>-${pointsValue.toFixed(2)}</span>
            </div>
          )}
          <Separator />
          <div className="flex justify-between text-lg font-semibold text-gray-800">
            <span>Total</span>
            <span>${finalTotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-sm text-green-700">
            <span>Points Earned</span>
            <span>+{pointsEarned} points</span>
          </div>
        </div>
      </Card>

      {/* Security Info */}
      <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
        <Lock className="w-4 h-4" />
        <span>Secured with 256-bit SSL encryption</span>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={() => setStep('summary')}
          className="flex-1 bg-white/20 border-white/30 hover:bg-white/30"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handlePayment}
          disabled={!selectedPaymentMethod && (paymentType === 'card' || paymentType === 'mixed')}
          className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
        >
          <Shield className="w-4 h-4 mr-2" />
          Pay ${finalTotal.toFixed(2)}
        </Button>
      </div>
    </div>
  );

  const renderProcessing = () => (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-6">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <Zap className="w-8 h-8 text-white" />
        </motion.div>
      </div>
      <h2 className="text-xl font-semibold text-gray-800 mb-2">Processing Payment</h2>
      <p className="text-gray-600 text-center mb-6">
        Please wait while we securely process your payment...
      </p>
      <div className="w-full max-w-xs">
        <Progress value={66} className="h-2" />
      </div>
    </div>
  );

  const renderSuccess = () => (
    <div className="flex flex-col items-center justify-center py-12">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, type: "spring" }}
        className="w-16 h-16 bg-gradient-to-r from-green-600 to-emerald-600 rounded-full flex items-center justify-center mb-6"
      >
        <CheckCircle className="w-8 h-8 text-white" />
      </motion.div>
      <h2 className="text-xl font-semibold text-gray-800 mb-2">Payment Successful!</h2>
      <p className="text-gray-600 text-center mb-6">
        Your {service.type} has been confirmed. You'll receive a confirmation shortly.
      </p>
      <div className="w-full max-w-sm space-y-4">
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
          <div className="text-center space-y-2">
            <p className="text-2xl font-bold text-gray-800">${finalTotal.toFixed(2)}</p>
            <p className="text-sm text-gray-600">Total Paid</p>
            {pointsToUse > 0 && (
              <p className="text-sm text-purple-700">
                {pointsToUse.toLocaleString()} points used
              </p>
            )}
            <p className="text-sm text-green-700">
              +{pointsEarned} points earned
            </p>
          </div>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          {step !== 'success' && (
            <Button
              variant="ghost"
              onClick={step === 'summary' ? onBack : () => setStep('summary')}
              className="hover:bg-white/30"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              {step === 'summary' ? 'Back' : 'Back to Summary'}
            </Button>
          )}
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800">
              {step === 'summary' && 'Order Summary'}
              {step === 'payment' && 'Payment'}
              {step === 'processing' && 'Processing'}
              {step === 'success' && 'Complete'}
            </h1>
          </div>
          <div className="w-16"></div> {/* Spacer for center alignment */}
        </div>

        {/* Progress Steps */}
        {step !== 'success' && (
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center gap-4">
              {['summary', 'payment', 'processing'].map((s, index) => (
                <div key={s} className="flex items-center gap-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                    s === step || (['payment', 'processing'].includes(step) && s === 'summary') || (step === 'processing' && s === 'payment')
                      ? 'bg-purple-600 text-white' 
                      : 'bg-white/30 text-gray-600'
                  }`}>
                    {index + 1}
                  </div>
                  {index < 2 && (
                    <div className={`w-12 h-1 transition-colors ${
                      (['payment', 'processing'].includes(step) && index === 0) || (step === 'processing' && index === 1)
                        ? 'bg-purple-600' 
                        : 'bg-white/30'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={step}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {step === 'summary' && renderSummary()}
            {step === 'payment' && renderPayment()}
            {step === 'processing' && renderProcessing()}
            {step === 'success' && renderSuccess()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}