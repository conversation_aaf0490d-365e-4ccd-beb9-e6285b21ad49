// Safe store implementation that doesn't cause circular dependencies
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { User } from '../auth/types';

interface Match {
  id: string;
  type: 'parking' | 'venue' | 'valet';
  title: string;
  subtitle: string;
  distance: string;
  availability: string;
  rating?: number;
  price?: string;
  image: string;
  details: {
    description: string;
    features: string[];
    footprint?: number;
    vibe?: string;
  };
}

interface Notification {
  id: string;
  type: 'friend_at_venue' | 'collaborative_invite' | 'reservation_confirmed';
  title: string;
  message: string;
  timestamp: string;
  actionLabel?: string;
  venueId?: string;
  friendId?: string;
}

interface AppStore {
  // Authentication state
  user: User | null;
  isAuthenticated: boolean;
  
  // App state
  currentView: string;
  currentMode: 'consumer' | 'business';
  
  // Data
  matches: Match[];
  notifications: Notification[];
  friends: any[];
  
  // Loading states
  isLoadingMatches: boolean;
  isLoadingUser: boolean;
  
  // Error states
  matchesError: string | null;
  userError: string | null;
  
  // Actions
  setUser: (user: User | null) => void;
  setCurrentView: (view: string) => void;
  setCurrentMode: (mode: 'consumer' | 'business') => void;
  setMatches: (matches: Match[]) => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
  
  // Async actions
  fetchMatches: (userId: string) => Promise<void>;
  fetchUser: () => Promise<void>;
  
  // Reset state
  reset: () => void;
}

const initialState = {
  user: null,
  isAuthenticated: false,
  currentView: 'matches',
  currentMode: 'consumer' as const,
  matches: [],
  notifications: [],
  friends: [],
  isLoadingMatches: false,
  isLoadingUser: false,
  matchesError: null,
  userError: null,
};

// Service integration callbacks - these will be set after services are initialized
let analyticsServiceCallback: any = null;
let errorServiceCallback: any = null;

export function setServiceCallbacks(analytics: any, error: any) {
  analyticsServiceCallback = analytics;
  errorServiceCallback = error;
}

export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setUser: (user) => {
          set({ user, isAuthenticated: !!user }, false, 'setUser');
          
          if (user && analyticsServiceCallback) {
            analyticsServiceCallback.identify(user.id, {
              userId: user.id,
              email: user.email,
              name: user.name,
              role: user.role,
              signupDate: user.createdAt,
            });
            
            if (errorServiceCallback) {
              errorServiceCallback.setUserContext(user.id, user.email, user.role);
            }
          } else if (analyticsServiceCallback) {
            analyticsServiceCallback.reset();
            if (errorServiceCallback) {
              errorServiceCallback.clearUserContext();
            }
          }
        },

        setCurrentView: (view) => {
          set({ currentView: view }, false, 'setCurrentView');
          if (analyticsServiceCallback) {
            analyticsServiceCallback.page(view, { 
              previous_view: get().currentView,
              user_mode: get().currentMode,
            });
          }
        },

        setCurrentMode: (mode) => {
          set({ currentMode: mode }, false, 'setCurrentMode');
          if (analyticsServiceCallback) {
            analyticsServiceCallback.track('mode_switch', {
              new_mode: mode,
              previous_mode: get().currentMode,
            });
          }
        },

        setMatches: (matches) => {
          set({ matches, matchesError: null }, false, 'setMatches');
        },

        addNotification: (notification) => {
          set(
            (state) => ({
              notifications: [notification, ...state.notifications],
            }),
            false,
            'addNotification'
          );
          
          if (analyticsServiceCallback) {
            analyticsServiceCallback.track('notification_received', {
              notification_type: notification.type,
              notification_id: notification.id,
            });
          }
        },

        removeNotification: (id) => {
          set(
            (state) => ({
              notifications: state.notifications.filter(n => n.id !== id),
            }),
            false,
            'removeNotification'
          );
          
          if (analyticsServiceCallback) {
            analyticsServiceCallback.track('notification_dismissed', {
              notification_id: id,
            });
          }
        },

        fetchMatches: async (userId) => {
          set({ isLoadingMatches: true, matchesError: null }, false, 'fetchMatches/start');
          
          try {
            const response = await fetch(`/api/matches?userId=${userId}`, {
              headers: {
                'Authorization': `Bearer ${get().user?.id}`, // This would be the actual token
              },
            });

            if (!response.ok) {
              throw new Error(`Failed to fetch matches: ${response.statusText}`);
            }

            const matches = await response.json();
            set({ 
              matches, 
              isLoadingMatches: false, 
              matchesError: null 
            }, false, 'fetchMatches/success');
            
            if (analyticsServiceCallback) {
              analyticsServiceCallback.track('matches_fetched', {
                count: matches.length,
                user_id: userId,
              });
            }

          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to fetch matches';
            
            set({ 
              isLoadingMatches: false, 
              matchesError: errorMessage 
            }, false, 'fetchMatches/error');
            
            if (errorServiceCallback) {
              errorServiceCallback.captureError(error as Error, {
                userId,
                feature: 'matches',
                action: 'fetch',
                metadata: { userId },
              });
            }
          }
        },

        fetchUser: async () => {
          set({ isLoadingUser: true, userError: null }, false, 'fetchUser/start');
          
          try {
            const response = await fetch('/api/user/me');
            
            if (!response.ok) {
              throw new Error(`Failed to fetch user: ${response.statusText}`);
            }

            const user = await response.json();
            set({ 
              user, 
              isAuthenticated: true,
              isLoadingUser: false, 
              userError: null 
            }, false, 'fetchUser/success');

          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user';
            
            set({ 
              isLoadingUser: false, 
              userError: errorMessage 
            }, false, 'fetchUser/error');
            
            if (errorServiceCallback) {
              errorServiceCallback.captureError(error as Error, {
                feature: 'user',
                action: 'fetch',
              });
            }
          }
        },

        reset: () => {
          set(initialState, false, 'reset');
          if (analyticsServiceCallback) {
            analyticsServiceCallback.reset();
          }
          if (errorServiceCallback) {
            errorServiceCallback.clearUserContext();
          }
        },
      }),
      {
        name: 'bytspot-store',
        partialize: (state) => ({
          user: state.user,
          currentView: state.currentView,
          currentMode: state.currentMode,
        }),
      }
    ),
    { name: 'BytspotStore' }
  )
);

// Selectors for better performance
export const selectUser = (state: AppStore) => state.user;
export const selectIsAuthenticated = (state: AppStore) => state.isAuthenticated;
export const selectMatches = (state: AppStore) => state.matches;
export const selectNotifications = (state: AppStore) => state.notifications;
export const selectCurrentView = (state: AppStore) => state.currentView;
export const selectCurrentMode = (state: AppStore) => state.currentMode;

// Loading selectors
export const selectIsLoadingMatches = (state: AppStore) => state.isLoadingMatches;
export const selectIsLoadingUser = (state: AppStore) => state.isLoadingUser;

// Error selectors
export const selectMatchesError = (state: AppStore) => state.matchesError;
export const selectUserError = (state: AppStore) => state.userError;