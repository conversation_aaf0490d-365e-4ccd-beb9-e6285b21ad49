import { useCallback } from 'react';
import { useAuth } from '../auth/AuthProvider';
import { analyticsService } from '../monitoring/analyticsService';

export function useAnalytics() {
  const { user } = useAuth();

  const track = useCallback((event: string, properties?: Record<string, any>) => {
    analyticsService.track(event, {
      ...properties,
      user_id: user?.id,
      user_role: user?.role,
      timestamp: Date.now(),
    });
  }, [user]);

  const page = useCallback((pageName: string, properties?: Record<string, any>) => {
    analyticsService.page(pageName, {
      ...properties,
      user_id: user?.id,
      user_role: user?.role,
    });
  }, [user]);

  const identify = useCallback((userId: string, traits?: Record<string, any>) => {
    analyticsService.identify(userId, {
      userId,
      ...traits,
      ...(user && {
        email: user.email,
        name: user.name,
        role: user.role,
        signupDate: user.createdAt,
      }),
    });
  }, [user]);

  // Common event tracking functions
  const trackMatchView = useCallback((matchId: string, matchType: string) => {
    track('match_viewed', {
      match_id: matchId,
      match_type: matchType,
    });
  }, [track]);

  const trackMatchSwipe = useCallback((matchId: string, direction: 'left' | 'right', matchType: string) => {
    track('match_swiped', {
      match_id: matchId,
      direction,
      match_type: matchType,
    });
  }, [track]);

  const trackReservation = useCallback((venueId: string, success: boolean) => {
    track('reservation_attempt', {
      venue_id: venueId,
      success,
    });
  }, [track]);

  const trackFeatureUsage = useCallback((feature: string, action: string, metadata?: Record<string, any>) => {
    track('feature_used', {
      feature,
      action,
      ...metadata,
    });
  }, [track]);

  const trackPerformance = useCallback((metric: string, value: number, unit: string = 'ms') => {
    track('performance_metric', {
      metric,
      value,
      unit,
    });
  }, [track]);

  const trackError = useCallback((error: string, context?: Record<string, any>) => {
    track('error_occurred', {
      error,
      ...context,
    });
  }, [track]);

  return {
    track,
    page,
    identify,
    trackMatchView,
    trackMatchSwipe,
    trackReservation,
    trackFeatureUsage,
    trackPerformance,
    trackError,
  };
}