import React from 'react';
import { Button } from './ui/button';

export function DebugPanel() {
  const clearStorage = () => {
    localStorage.clear();
    sessionStorage.clear();
    window.location.reload();
  };

  const showStorageInfo = () => {
    const tokens = localStorage.getItem('bytspot_tokens');
    console.log('LocalStorage tokens:', tokens);
    console.log('All localStorage keys:', Object.keys(localStorage));
  };

  // Only show in development - safely check environment
  const isDev = typeof import.meta !== 'undefined' && 
                import.meta.env && 
                (import.meta.env.DEV || import.meta.env.MODE === 'development');
                
  if (!isDev) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white p-4 rounded-lg shadow-lg border">
      <h3 className="text-sm font-medium mb-2">Debug Panel</h3>
      <div className="space-y-2">
        <Button 
          onClick={clearStorage}
          variant="destructive"
          size="sm"
          className="w-full"
        >
          Clear Storage & Reload
        </Button>
        <Button 
          onClick={showStorageInfo}
          variant="outline"
          size="sm"
          className="w-full"
        >
          Log Storage Info
        </Button>
      </div>
    </div>
  );
}