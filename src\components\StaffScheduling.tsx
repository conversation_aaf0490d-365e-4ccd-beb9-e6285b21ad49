import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  User, 
  Plus, 
  Edit, 
  X,
  Check,
  AlertCircle,
  Coffee,
  MapPin,
  Users,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  UserPlus,
  Filter
} from 'lucide-react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface ShiftSlot {
  id: string;
  startTime: string;
  endTime: string;
  staffId?: string;
  staffName?: string;
  role: string;
  status: 'open' | 'filled' | 'requested-off' | 'conflicted';
  isBreak?: boolean;
  location?: string;
}

interface StaffMember {
  id: string;
  name: string;
  avatar?: string;
  role: string;
  skills: string[];
  availability: {
    [day: string]: { start: string; end: string; available: boolean };
  };
  totalHours: number;
  hourlyRate: number;
}

interface TimeOffRequest {
  id: string;
  staffId: string;
  staffName: string;
  date: string;
  reason: string;
  status: 'pending' | 'approved' | 'denied';
  type: 'sick' | 'vacation' | 'personal';
}

export function StaffScheduling() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedWeek, setSelectedWeek] = useState(0);
  const [viewMode, setViewMode] = useState<'week' | 'day' | 'month'>('week');
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [selectedShift, setSelectedShift] = useState<ShiftSlot | null>(null);

  // Mock data
  const staff: StaffMember[] = [
    {
      id: '1',
      name: 'James Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      role: 'Valet Manager',
      skills: ['Valet', 'Customer Service', 'Management'],
      availability: {
        monday: { start: '18:00', end: '02:00', available: true },
        tuesday: { start: '18:00', end: '02:00', available: true },
        wednesday: { start: '18:00', end: '02:00', available: false },
        thursday: { start: '18:00', end: '02:00', available: true },
        friday: { start: '18:00', end: '02:00', available: true },
        saturday: { start: '17:00', end: '03:00', available: true },
        sunday: { start: '17:00', end: '01:00', available: true }
      },
      totalHours: 42,
      hourlyRate: 28
    },
    {
      id: '2',
      name: 'Lisa Park',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      role: 'Floor Manager',
      skills: ['Management', 'Customer Service', 'Problem Solving'],
      availability: {
        monday: { start: '17:00', end: '01:00', available: true },
        tuesday: { start: '17:00', end: '01:00', available: true },
        wednesday: { start: '17:00', end: '01:00', available: true },
        thursday: { start: '17:00', end: '01:00', available: true },
        friday: { start: '17:00', end: '01:00', available: true },
        saturday: { start: '16:00', end: '02:00', available: true },
        sunday: { start: '16:00', end: '12:00', available: false }
      },
      totalHours: 48,
      hourlyRate: 32
    },
    {
      id: '3',
      name: 'Carlos Martinez',
      role: 'Server',
      skills: ['Service', 'Bartending', 'Food Safety'],
      availability: {
        monday: { start: '16:00', end: '00:00', available: true },
        tuesday: { start: '16:00', end: '00:00', available: true },
        wednesday: { start: '16:00', end: '00:00', available: true },
        thursday: { start: '16:00', end: '00:00', available: true },
        friday: { start: '16:00', end: '00:00', available: true },
        saturday: { start: '15:00', end: '01:00', available: true },
        sunday: { start: '15:00', end: '23:00', available: true }
      },
      totalHours: 50,
      hourlyRate: 22
    },
    {
      id: '4',
      name: 'Sophie Anderson',
      avatar: 'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=150&h=150&fit=crop&crop=face',
      role: 'Hostess',
      skills: ['Customer Service', 'Reservations', 'Communication'],
      availability: {
        monday: { start: '18:00', end: '02:00', available: true },
        tuesday: { start: '18:00', end: '02:00', available: true },
        wednesday: { start: '18:00', end: '02:00', available: true },
        thursday: { start: '18:00', end: '02:00', available: true },
        friday: { start: '18:00', end: '02:00', available: true },
        saturday: { start: '17:00', end: '03:00', available: true },
        sunday: { start: '17:00', end: '01:00', available: true }
      },
      totalHours: 35,
      hourlyRate: 18
    }
  ];

  const weeklySchedule: { [day: string]: ShiftSlot[] } = {
    monday: [
      { id: '1', startTime: '17:00', endTime: '01:00', staffId: '2', staffName: 'Lisa Park', role: 'Floor Manager', status: 'filled', location: 'Main Floor' },
      { id: '2', startTime: '18:00', endTime: '02:00', staffId: '1', staffName: 'James Rodriguez', role: 'Valet Manager', status: 'filled', location: 'Valet Stand' },
      { id: '3', startTime: '16:00', endTime: '00:00', role: 'Server', status: 'open', location: 'Dining Area' },
      { id: '4', startTime: '18:00', endTime: '02:00', staffId: '4', staffName: 'Sophie Anderson', role: 'Hostess', status: 'filled', location: 'Reception' }
    ],
    tuesday: [
      { id: '5', startTime: '17:00', endTime: '01:00', staffId: '2', staffName: 'Lisa Park', role: 'Floor Manager', status: 'filled', location: 'Main Floor' },
      { id: '6', startTime: '18:00', endTime: '02:00', staffId: '1', staffName: 'James Rodriguez', role: 'Valet Manager', status: 'filled', location: 'Valet Stand' },
      { id: '7', startTime: '16:00', endTime: '00:00', staffId: '3', staffName: 'Carlos Martinez', role: 'Server', status: 'filled', location: 'Dining Area' },
      { id: '8', startTime: '18:00', endTime: '02:00', role: 'Hostess', status: 'open', location: 'Reception' }
    ],
    wednesday: [
      { id: '9', startTime: '17:00', endTime: '01:00', role: 'Floor Manager', status: 'requested-off', location: 'Main Floor' },
      { id: '10', startTime: '18:00', endTime: '02:00', role: 'Valet Manager', status: 'requested-off', location: 'Valet Stand' },
      { id: '11', startTime: '16:00', endTime: '00:00', staffId: '3', staffName: 'Carlos Martinez', role: 'Server', status: 'filled', location: 'Dining Area' },
      { id: '12', startTime: '18:00', endTime: '02:00', staffId: '4', staffName: 'Sophie Anderson', role: 'Hostess', status: 'filled', location: 'Reception' }
    ],
    thursday: [
      { id: '13', startTime: '17:00', endTime: '01:00', staffId: '2', staffName: 'Lisa Park', role: 'Floor Manager', status: 'filled', location: 'Main Floor' },
      { id: '14', startTime: '18:00', endTime: '02:00', staffId: '1', staffName: 'James Rodriguez', role: 'Valet Manager', status: 'filled', location: 'Valet Stand' },
      { id: '15', startTime: '16:00', endTime: '00:00', role: 'Server', status: 'conflicted', location: 'Dining Area' },
      { id: '16', startTime: '18:00', endTime: '02:00', staffId: '4', staffName: 'Sophie Anderson', role: 'Hostess', status: 'filled', location: 'Reception' }
    ],
    friday: [
      { id: '17', startTime: '17:00', endTime: '01:00', staffId: '2', staffName: 'Lisa Park', role: 'Floor Manager', status: 'filled', location: 'Main Floor' },
      { id: '18', startTime: '18:00', endTime: '02:00', staffId: '1', staffName: 'James Rodriguez', role: 'Valet Manager', status: 'filled', location: 'Valet Stand' },
      { id: '19', startTime: '16:00', endTime: '00:00', staffId: '3', staffName: 'Carlos Martinez', role: 'Server', status: 'filled', location: 'Dining Area' },
      { id: '20', startTime: '18:00', endTime: '02:00', staffId: '4', staffName: 'Sophie Anderson', role: 'Hostess', status: 'filled', location: 'Reception' },
      { id: '21', startTime: '15:00', endTime: '23:00', role: 'Server', status: 'open', location: 'Bar Area' }
    ],
    saturday: [
      { id: '22', startTime: '16:00', endTime: '02:00', staffId: '2', staffName: 'Lisa Park', role: 'Floor Manager', status: 'filled', location: 'Main Floor' },
      { id: '23', startTime: '17:00', endTime: '03:00', staffId: '1', staffName: 'James Rodriguez', role: 'Valet Manager', status: 'filled', location: 'Valet Stand' },
      { id: '24', startTime: '15:00', endTime: '01:00', staffId: '3', staffName: 'Carlos Martinez', role: 'Server', status: 'filled', location: 'Dining Area' },
      { id: '25', startTime: '17:00', endTime: '03:00', staffId: '4', staffName: 'Sophie Anderson', role: 'Hostess', status: 'filled', location: 'Reception' },
      { id: '26', startTime: '14:00', endTime: '22:00', role: 'Server', status: 'open', location: 'Bar Area' },
      { id: '27', startTime: '16:00', endTime: '00:00', role: 'Security', status: 'open', location: 'Front Door' }
    ],
    sunday: [
      { id: '28', startTime: '16:00', endTime: '12:00', role: 'Floor Manager', status: 'requested-off', location: 'Main Floor' },
      { id: '29', startTime: '17:00', endTime: '01:00', staffId: '1', staffName: 'James Rodriguez', role: 'Valet Manager', status: 'filled', location: 'Valet Stand' },
      { id: '30', startTime: '15:00', endTime: '23:00', staffId: '3', staffName: 'Carlos Martinez', role: 'Server', status: 'filled', location: 'Dining Area' },
      { id: '31', startTime: '17:00', endTime: '01:00', staffId: '4', staffName: 'Sophie Anderson', role: 'Hostess', status: 'filled', location: 'Reception' }
    ]
  };

  const timeOffRequests: TimeOffRequest[] = [
    {
      id: '1',
      staffId: '2',
      staffName: 'Lisa Park',
      date: 'Wednesday',
      reason: 'Family event',
      status: 'pending',
      type: 'personal'
    },
    {
      id: '2',
      staffId: '1',
      staffName: 'James Rodriguez',
      date: 'Wednesday',
      reason: 'Doctor appointment',
      status: 'pending',
      type: 'personal'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'filled': return 'bg-green-100 text-green-800 border-green-300';
      case 'open': return 'bg-orange-100 text-orange-800 border-orange-300';
      case 'requested-off': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'conflicted': return 'bg-red-100 text-red-800 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Floor Manager': return 'bg-purple-500';
      case 'Valet Manager': return 'bg-blue-500';
      case 'Server': return 'bg-green-500';
      case 'Hostess': return 'bg-pink-500';
      case 'Security': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  const weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const dayLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  const totalStaffCost = staff.reduce((total, member) => total + (member.totalHours * member.hourlyRate), 0);
  const averageHoursPerStaff = staff.reduce((total, member) => total + member.totalHours, 0) / staff.length;

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Staff Scheduling</h1>
            <p className="text-gray-600">Manage shifts, availability, and time-off requests</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Dialog open={showScheduleDialog} onOpenChange={setShowScheduleDialog}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Shift
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Shift</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Start Time</label>
                      <Input type="time" />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">End Time</label>
                      <Input type="time" />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Role</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="server">Server</SelectItem>
                        <SelectItem value="hostess">Hostess</SelectItem>
                        <SelectItem value="valet">Valet</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Location</label>
                    <Input placeholder="e.g., Main Floor, Bar Area" />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setShowScheduleDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={() => setShowScheduleDialog(false)}>
                      Create Shift
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{staff.length}</p>
                <p className="text-sm text-gray-600">Total Staff</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Clock className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{averageHoursPerStaff.toFixed(0)}</p>
                <p className="text-sm text-gray-600">Avg Hours/Week</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertCircle className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{timeOffRequests.length}</p>
                <p className="text-sm text-gray-600">Pending Requests</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <CalendarIcon className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">${totalStaffCost.toLocaleString()}</p>
                <p className="text-sm text-gray-600">Weekly Labor Cost</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="schedule" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-white/20 backdrop-blur-sm">
            <TabsTrigger value="schedule">Weekly Schedule</TabsTrigger>
            <TabsTrigger value="staff">Staff Overview</TabsTrigger>
            <TabsTrigger value="requests">Time-Off Requests</TabsTrigger>
          </TabsList>

          <TabsContent value="schedule" className="space-y-6">
            {/* Week Navigation */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" className="bg-white/20 border-white/30 hover:bg-white/30">
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <h3 className="text-lg font-semibold text-gray-800">
                  Week of January 15-21, 2024
                </h3>
                <Button variant="outline" size="sm" className="bg-white/20 border-white/30 hover:bg-white/30">
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  variant={viewMode === 'week' ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setViewMode('week')}
                  className={viewMode === 'week' ? "bg-indigo-600 text-white" : "bg-white/20 border-white/30 hover:bg-white/30"}
                >
                  Week
                </Button>
                <Button 
                  variant={viewMode === 'day' ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setViewMode('day')}
                  className={viewMode === 'day' ? "bg-indigo-600 text-white" : "bg-white/20 border-white/30 hover:bg-white/30"}
                >
                  Day
                </Button>
              </div>
            </div>

            {/* Schedule Grid */}
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <div className="overflow-x-auto">
                <div className="grid grid-cols-8 gap-4 min-w-[800px]">
                  {/* Header Row */}
                  <div className="font-semibold text-gray-700 text-center py-2">Time</div>
                  {dayLabels.map((day, index) => (
                    <div key={day} className="font-semibold text-gray-700 text-center py-2">
                      {day}
                      <div className="text-xs text-gray-500 mt-1">
                        Jan {15 + index}
                      </div>
                    </div>
                  ))}

                  {/* Schedule Rows */}
                  <div className="col-span-8 space-y-4">
                    {weekDays.map((day, dayIndex) => (
                      <div key={day} className="grid grid-cols-8 gap-4 items-start">
                        <div className="text-sm text-gray-600 py-2 font-medium">
                          {dayLabels[dayIndex]}
                        </div>
                        <div className="col-span-7">
                          <div className="space-y-2">
                            {weeklySchedule[day]?.map((shift) => (
                              <motion.div
                                key={shift.id}
                                className={`p-3 rounded-lg border transition-all duration-200 hover:shadow-md cursor-pointer ${
                                  shift.status === 'filled' ? 'bg-white/60 border-white/60' :
                                  shift.status === 'open' ? 'bg-orange-50/60 border-orange-200/60' :
                                  shift.status === 'requested-off' ? 'bg-yellow-50/60 border-yellow-200/60' :
                                  'bg-red-50/60 border-red-200/60'
                                }`}
                                onClick={() => setSelectedShift(shift)}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className={`w-3 h-3 rounded-full ${getRoleColor(shift.role)}`} />
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium text-sm text-gray-800">
                                          {shift.startTime} - {shift.endTime}
                                        </span>
                                        <Badge variant="outline" className={getStatusColor(shift.status)}>
                                          {shift.status.replace('-', ' ')}
                                        </Badge>
                                      </div>
                                      <div className="text-xs text-gray-600 mt-1">
                                        {shift.role} • {shift.location}
                                      </div>
                                      {shift.staffName && (
                                        <div className="text-xs text-gray-700 font-medium mt-1">
                                          {shift.staffName}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="staff" className="space-y-6">
            {/* Staff Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {staff.map((member) => (
                <Card key={member.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold text-gray-800">{member.name}</h3>
                        <p className="text-sm text-gray-600">{member.role}</p>
                        <div className="flex items-center gap-1 mt-1">
                          {member.skills.slice(0, 2).map((skill) => (
                            <Badge key={skill} variant="outline" className="text-xs bg-white/30 border-white/40">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Weekly Hours</p>
                        <p className="font-semibold text-gray-800">{member.totalHours}h</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Hourly Rate</p>
                        <p className="font-semibold text-gray-800">${member.hourlyRate}</p>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-gray-600 text-sm mb-2">Availability This Week</p>
                      <div className="flex gap-1">
                        {weekDays.map((day) => {
                          const availability = member.availability[day];
                          return (
                            <div
                              key={day}
                              className={`w-8 h-8 rounded flex items-center justify-center text-xs font-medium ${
                                availability.available 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-400'
                              }`}
                            >
                              {day.charAt(0).toUpperCase()}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between pt-2 border-t border-white/30">
                      <span className="text-sm font-medium text-gray-700">
                        Weekly Cost: ${(member.totalHours * member.hourlyRate).toLocaleString()}
                      </span>
                      <Button size="sm" variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
                        View Schedule
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
              
              {/* Add Staff Card */}
              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6 border-dashed border-2 hover:bg-white/30 transition-colors cursor-pointer">
                <div className="flex flex-col items-center justify-center h-full text-center py-8">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
                    <UserPlus className="w-6 h-6 text-gray-600" />
                  </div>
                  <h3 className="font-semibold text-gray-800 mb-1">Add New Staff Member</h3>
                  <p className="text-sm text-gray-600 mb-3">Expand your team</p>
                  <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700 text-white">
                    Add Staff
                  </Button>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="requests" className="space-y-6">
            {/* Time-Off Requests */}
            <div className="space-y-4">
              {timeOffRequests.map((request) => (
                <Card key={request.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-2 bg-yellow-100 rounded-lg">
                        <CalendarIcon className="w-5 h-5 text-yellow-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800">{request.staffName}</h3>
                        <p className="text-sm text-gray-600">
                          Requesting {request.date} off • {request.reason}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                            {request.type}
                          </Badge>
                          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-300">
                            {request.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline" className="border-red-300 text-red-600 hover:bg-red-50">
                        <X className="w-4 h-4 mr-1" />
                        Deny
                      </Button>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                        <Check className="w-4 h-4 mr-1" />
                        Approve
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}