import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'motion/react';
import { ArrowLeft, Star, MapPin, Clock, Users, Wifi, Music, Camera, Heart, Share, Calendar, Phone, Video } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Separator } from './ui/separator';
import { PaymentFlow } from './PaymentFlow';
import { LiveVibeCamera } from './LiveVibeCamera';
import { VibeScorePrompt } from './VibeScorePrompt';
import { useAnalytics } from '../lib/hooks/useAnalytics';

interface VenueDetailProps {
  match?: {
    id: string;
    title: string;
    subtitle: string;
    distance: string;
    availability: string;
    rating?: number;
    price?: string;
    image: string;
    details: {
      description: string;
      features: string[];
      footprint?: number;
      vibe?: string;
    };
  };
  onBack?: () => void;
  onVibeScoreEarned?: (venueData: any) => void;
}

export function VenueDetail({ match: propMatch, onBack, onVibeScoreEarned }: VenueDetailProps) {
  const { venueId } = useParams();
  const { track } = useAnalytics();
  const navigate = useNavigate();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [showVibeCamera, setShowVibeCamera] = useState(false);
  const [showVibePrompt, setShowVibePrompt] = useState(false);
  const [userVisitTime, setUserVisitTime] = useState<Date>(new Date());

  // Default onBack function if not provided
  const handleBack = onBack || (() => navigate(-1));

  // Mock venue data - in real app, this would come from API based on venueId
  const defaultVenue = {
    id: venueId || '1',
    title: 'Rooftop Terrace',
    subtitle: 'Downtown Skyline Bar',
    distance: '2 min walk',
    availability: 'Open now',
    rating: 4.8,
    price: '$$$',
    image: 'https://images.unsplash.com/photo-1566417713940-fe7c737a9ef2?w=800&h=600&fit=crop',
    details: {
      description: 'Premium rooftop experience with panoramic city views and craft cocktails.',
      features: ['Rooftop Views', 'Craft Cocktails', 'Live Music', 'VIP Sections', 'Outdoor Seating', 'Premium Sound'],
      footprint: 87,
      vibe: 'Upscale & Energetic'
    }
  };

  const match = propMatch || defaultVenue;

  const vibeScore = match.details.footprint ? Math.min(Math.round((match.details.footprint / 150) * 100), 100) : 85;
  const crowdLevel = match.details.footprint ? (
    match.details.footprint < 50 ? 'Chill' :
    match.details.footprint < 100 ? 'Lively' : 'Packed'
  ) : 'Moderate';

  const mockReviews = [
    { name: "Sarah M.", rating: 5, comment: "Amazing atmosphere and great cocktails!" },
    { name: "Mike R.", rating: 4, comment: "Perfect for a night out with friends" },
    { name: "Emma L.", rating: 5, comment: "The rooftop view is incredible" }
  ];

  const mockEvents = [
    { time: "9:00 PM", event: "Live DJ Set", type: "Music" },
    { time: "10:30 PM", event: "Happy Hour Extended", type: "Special" },
    { time: "11:00 PM", event: "Rooftop Opens", type: "Access" }
  ];

  const handlePaymentComplete = (paymentDetails: any) => {
    console.log('Payment completed:', paymentDetails);
    // Handle successful payment
    setShowPayment(false);
    
    // Simulate venue visit completion after payment
    // In real app, this would be triggered by actual venue check-out
    setTimeout(() => {
      const visitDuration = Math.floor((Date.now() - userVisitTime.getTime()) / (1000 * 60)); // minutes
      const vibeScore = Math.floor(Math.random() * 3) + 8; // Random score 8-10 for demo
      
      const venueData = {
        id: match.id,
        name: match.title,
        vibeScore,
        visitDuration,
        userRating: vibeScore
      };
      
      setShowVibePrompt(true);
      onVibeScoreEarned?.(venueData);
    }, 3000); // Show prompt 3 seconds after payment (simulate checkout)
    
    handleBack(); // Return to main app
  };

  const handleVibeShare = () => {
    track('venue_vibe_share_initiated', {
      venue_id: match.id,
      venue_name: match.title,
    });
    setShowVibeCamera(true);
  };

  const handleVibeUpload = (videoData: any) => {
    track('venue_vibe_shared', {
      venue_id: match.id,
      venue_name: match.title,
      video_duration: videoData.duration,
    });
    setShowVibeCamera(false);
    // Handle successful video upload
  };

  const handleShareVibeScore = (platform: string, vibeData: any) => {
    track('vibe_score_shared_from_venue', {
      venue_id: match.id,
      venue_name: match.title,
      platform,
      vibe_score: vibeData.score
    });
    
    // In real app, integrate with actual social media APIs
    console.log('Sharing to', platform, vibeData);
    
    // Simulate successful share
    alert(`Successfully shared your ${vibeData.score}/10 vibe score to ${platform}!`);
  };

  const handleTakeVibePhoto = () => {
    // Open the camera with venue context for photo/story creation
    setShowVibeCamera(true);
    setShowVibePrompt(false);
  };

  if (showPayment) {
    return (
      <PaymentFlow
        service={{
          id: match.id,
          type: 'venue',
          title: match.title,
          subtitle: match.subtitle,
          basePrice: 85, // VIP table reservation price
          location: match.distance,
          features: match.details.features
        }}
        onBack={() => setShowPayment(false)}
        onComplete={handlePaymentComplete}
      />
    );
  }

  if (showVibeCamera) {
    return (
      <LiveVibeCamera
        isOpen={showVibeCamera}
        onClose={() => setShowVibeCamera(false)}
        currentLocation={{
          name: match.title,
          type: 'venue',
          address: match.subtitle,
        }}
      />
    );
  }

  return (
    <>
      <div className="h-full bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="h-full overflow-auto"
        >
          {/* Header Image */}
          <div className="relative h-64 bg-cover bg-center" style={{ backgroundImage: `url(${match.image})` }}>
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            
            {/* Header Controls */}
            <div className="absolute top-4 left-4 right-4 flex justify-between items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="backdrop-blur-sm bg-white/20 text-white border-white/30 hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsBookmarked(!isBookmarked)}
                  className="backdrop-blur-sm bg-white/20 text-white border-white/30 hover:bg-white/30"
                >
                  <Heart className={`w-4 h-4 ${isBookmarked ? 'fill-current text-red-400' : ''}`} />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="backdrop-blur-sm bg-white/20 text-white border-white/30 hover:bg-white/30"
                >
                  <Share className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Title Overlay */}
            <div className="absolute bottom-4 left-4 right-4">
              <h1 className="text-3xl font-bold text-white mb-1">{match.title}</h1>
              <p className="text-white/80">{match.subtitle}</p>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Prominent Share the Vibe Button */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              <Button
                onClick={handleVibeShare}
                className="w-full h-16 bg-gradient-to-r from-purple-600 via-pink-500 to-purple-600 hover:from-purple-700 hover:via-pink-600 hover:to-purple-700 text-white shadow-xl hover:shadow-2xl transform hover:scale-[1.02] transition-all duration-200 rounded-2xl border-2 border-white/20"
              >
                <div className="flex items-center justify-center gap-3">
                  <div className="relative">
                    <Video className="w-6 h-6" />
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full"
                    />
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-lg">Share the Vibe</div>
                    <div className="text-white/90 text-sm">Show what's happening at {match.title}</div>
                  </div>
                </div>
              </Button>
            </motion.div>

            {/* Venue Info & Address */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="w-4 h-4 text-blue-500" />
                    <span className="text-sm text-gray-600">{match.distance} • {match.availability}</span>
                    <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                      Open
                    </Badge>
                  </div>
                  <p className="text-gray-700">{match.details.description}</p>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
                <div className="flex items-center justify-center mb-2">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                </div>
                <p className="text-lg font-bold">{match.rating}</p>
                <p className="text-xs text-gray-600">Rating</p>
              </div>
              
              <div className="text-center backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
                <div className="flex items-center justify-center mb-2">
                  <MapPin className="w-5 h-5 text-blue-500" />
                </div>
                <p className="text-lg font-bold">{match.distance}</p>
                <p className="text-xs text-gray-600">Distance</p>
              </div>
              
              <div className="text-center backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
                <div className="flex items-center justify-center mb-2">
                  <Users className="w-5 h-5 text-green-500" />
                </div>
                <p className="text-lg font-bold">{crowdLevel}</p>
                <p className="text-xs text-gray-600">Crowd</p>
              </div>
            </div>

            {/* Vibe Score */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Live Vibe Score</h3>
                <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                  {vibeScore}/100
                </Badge>
              </div>
              
              <Progress value={vibeScore} className="mb-4" />
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-blue-500" />
                  <span>{match.details.footprint || 0} people inside</span>
                </div>
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4 text-purple-500" />
                  <span>Vibe: {match.details.vibe}</span>
                </div>
              </div>
            </div>

            {/* Tonight's Events */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <h3 className="text-lg font-semibold mb-4">Tonight's Events</h3>
              <div className="space-y-3">
                {mockEvents.map((event, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <div>
                        <p className="font-medium">{event.event}</p>
                        <p className="text-sm text-gray-600">{event.time}</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {event.type}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            {/* Features */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <h3 className="text-lg font-semibold mb-4">Amenities</h3>
              <div className="grid grid-cols-2 gap-3">
                {match.details.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Reviews */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <h3 className="text-lg font-semibold mb-4">Recent Reviews</h3>
              <div className="space-y-4">
                {mockReviews.map((review, index) => (
                  <div key={index}>
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">{review.name}</span>
                      <div className="flex items-center gap-1">
                        {[...Array(review.rating)].map((_, i) => (
                          <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{review.comment}</p>
                    {index < mockReviews.length - 1 && <Separator className="mt-4" />}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="sticky bottom-0 p-6 bg-gradient-to-t from-white via-white/95 to-transparent backdrop-blur-sm">
            <div className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {/* Handle call */}}
              >
                <Phone className="w-4 h-4 mr-2" />
                Call
              </Button>
              
              <Button
                className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                onClick={() => setShowPayment(true)}
              >
                <Calendar className="w-4 h-4 mr-2" />
                Make Reservation
              </Button>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Vibe Score Prompt */}
      <VibeScorePrompt
        isOpen={showVibePrompt}
        onClose={() => setShowVibePrompt(false)}
        venueData={{
          id: match.id,
          name: match.title,
          vibeScore: 9, // This would come from actual visit data
          visitDuration: 45, // This would come from actual visit tracking
        }}
        onShareVibe={handleShareVibeScore}
        onTakePhoto={handleTakeVibePhoto}
      />
    </>
  );
}