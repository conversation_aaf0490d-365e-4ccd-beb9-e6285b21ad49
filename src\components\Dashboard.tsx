import { motion } from 'motion/react';
import { 
  MapPin, 
  Target, 
  ShoppingCart, 
  Plus, 
  TrendingUp, 
  Users, 
  Clock,
  Menu,
  Home,
  MessageCircle,
  Settings,
  User
} from 'lucide-react';
import { Button } from './ui/button';

interface DashboardProps {
  onNavigate: (view: 'matches' | 'map' | 'insider' | 'concierge') => void;
  userName?: string;
}

export function Dashboard({ onNavigate, userName = "myndixapp" }: DashboardProps) {
  const stats = [
    {
      icon: MapPin,
      value: "1,234",
      label: "Spots\nAvailable",
      color: "text-blue-600"
    },
    {
      icon: TrendingUp,
      value: "5.6K",
      label: "Active Users",
      color: "text-purple-600"
    },
    {
      icon: Clock,
      value: "15 min",
      label: "Avg. Save\nTime",
      color: "text-green-600"
    }
  ];

  const navigationCards = [
    {
      icon: MapPin,
      title: "Find Nearby Venues",
      subtitle: "AI-powered discovery",
      color: "from-purple-500 to-purple-600",
      action: () => onNavigate('matches')
    },
    {
      icon: Target,
      title: "Discovery\nPreferences",
      subtitle: "Personalize your\nexperience",
      color: "from-blue-500 to-blue-600",
      action: () => onNavigate('concierge')
    },
    {
      icon: ShoppingCart,
      title: "Marketplace",
      subtitle: "Browse all spots",
      color: "from-green-500 to-green-600",
      action: () => onNavigate('map')
    },
    {
      icon: Plus,
      title: "List a Spot",
      subtitle: "Earn by hosting",
      color: "from-orange-500 to-red-500",
      action: () => onNavigate('insider')
    }
  ];

  return (
    <div className="h-full bg-gray-50 overflow-y-auto">
      {/* Status Bar */}
      <div className="flex justify-between items-center p-4 pt-12 bg-white">
        <span className="text-lg font-semibold">12:23</span>
        <span className="text-sm font-medium">Gmail</span>
        <div className="flex items-center gap-1">
          <div className="flex gap-1">
            <div className="w-1 h-3 bg-gray-800 rounded-full"></div>
            <div className="w-1 h-3 bg-gray-800 rounded-full"></div>
            <div className="w-1 h-3 bg-gray-800 rounded-full"></div>
            <div className="w-1 h-2 bg-gray-400 rounded-full"></div>
          </div>
          <span className="text-sm ml-2">5G</span>
          <div className="w-6 h-3 bg-gray-300 rounded-sm ml-2 relative">
            <div className="absolute right-0 top-0 w-5 h-3 bg-gray-800 rounded-sm"></div>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-6 py-4 flex items-center justify-between border-b border-gray-100">
        <div className="flex items-center gap-4">
          <Menu className="w-6 h-6 text-gray-600" />
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold text-purple-600">Bytspot</span>
            <Home className="w-5 h-5 text-purple-600" />
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <MapPin className="w-5 h-5 text-gray-600" />
          <MessageCircle className="w-5 h-5 text-gray-600" />
          <Settings className="w-5 h-5 text-gray-600" />
          <Plus className="w-5 h-5 text-gray-600" />
          <Settings className="w-5 h-5 text-gray-600" />
          <User className="w-5 h-5 text-gray-600" />
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Welcome Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-2xl font-bold text-purple-600 mb-2">
            Welcome back, {userName}!
          </h1>
          <p className="text-gray-600">
            Find and book parking spots effortlessly
          </p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-3 gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="bg-white rounded-2xl p-4 text-center shadow-sm border border-gray-100"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}
            >
              <div className="flex flex-col items-center space-y-2">
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
                <div className="text-xl font-bold text-gray-800">{stat.value}</div>
                <div className="text-xs text-gray-500 leading-tight whitespace-pre-line">
                  {stat.label}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Navigation Cards */}
        <motion.div
          className="grid grid-cols-2 gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          {navigationCards.map((card, index) => (
            <motion.button
              key={card.title}
              onClick={card.action}
              className="bg-white rounded-2xl p-6 text-left shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 + index * 0.1, duration: 0.4 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="space-y-3">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${card.color} flex items-center justify-center shadow-lg`}>
                  <card.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 leading-tight whitespace-pre-line">
                    {card.title}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1 whitespace-pre-line">
                    {card.subtitle}
                  </p>
                </div>
              </div>
            </motion.button>
          ))}
        </motion.div>

        {/* Additional Navigation Dot */}
        <div className="flex justify-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}