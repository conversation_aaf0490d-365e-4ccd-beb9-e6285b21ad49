import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Brain, 
  MapPin, 
  Zap, 
  Target, 
  Sparkles, 
  Wifi, 
  Bluetooth,
  Car,
  Users,
  Wine
} from 'lucide-react';

interface UserData {
  address: string;
  permissions: {
    gps: boolean;
    wifi: boolean;
    bluetooth: boolean;
    camera: boolean;
    notifications: boolean;
  };
}

interface Match {
  id: string;
  type: 'parking' | 'venue' | 'valet';
  title: string;
  subtitle: string;
  distance: string;
  availability: string;
  rating?: number;
  price?: string;
  image: string;
  details: {
    description: string;
    features: string[];
    footprint?: number;
    vibe?: string;
  };
}

interface DiscoverySearchProps {
  userData: UserData;
  onComplete: (matches: Match[]) => void;
}

const discoverySteps = [
  {
    icon: MapPin,
    title: 'Analyzing Location',
    description: 'Processing your destination and nearby areas',
    color: 'from-blue-500 to-cyan-500',
  },
  {
    icon: Wifi,
    title: 'Scanning Networks',
    description: 'Detecting local WiFi signals for precision',
    color: 'from-green-500 to-emerald-500',
  },
  {
    icon: Bluetooth,
    title: 'Finding Beacons',
    description: 'Connecting to nearby venue beacons',
    color: 'from-purple-500 to-indigo-500',
  },
  {
    icon: Brain,
    title: 'AI Processing',
    description: 'Matching your preferences with available spots',
    color: 'from-pink-500 to-rose-500',
  },
  {
    icon: Target,
    title: 'Personalizing Results',
    description: 'Curating perfect matches for you',
    color: 'from-orange-500 to-red-500',
  },
];

const mockDiscoveredMatches: Match[] = [
  {
    id: '1',
    type: 'parking',
    title: 'Smart Garage Spot A3',
    subtitle: 'Premium Downtown Location',
    distance: '1 min walk',
    availability: 'Available now',
    price: '$8/hour',
    image: 'https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=800',
    details: {
      description: 'AI-detected optimal spot with real-time availability',
      features: ['Covered', 'Security', 'EV Charging', 'Level 3']
    }
  },
  {
    id: '2',
    type: 'venue',
    title: 'The Local Spot',
    subtitle: 'Trending • Perfect Vibe Match',
    distance: '3 min walk',
    availability: 'Open until 2 AM',
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800',
    details: {
      description: 'AI-matched venue based on your preferences',
      features: ['Live Music', 'Craft Beer', 'Outdoor Seating', 'Great Reviews'],
      footprint: 89,
      vibe: 'Energetic'
    }
  },
  {
    id: '3',
    type: 'valet',
    title: 'Elite Valet Express',
    subtitle: 'Premium Service Available',
    distance: 'Pickup in 2 min',
    availability: 'Available now',
    price: '$20',
    image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800',
    details: {
      description: 'Top-rated valet service in your area',
      features: ['Insured', '5-Star Rated', 'Quick Pickup', 'Professional']
    }
  },
];

export function DiscoverySearch({ userData, onComplete }: DiscoverySearchProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSearching, setIsSearching] = useState(true);
  const [foundMatches, setFoundMatches] = useState<number>(0);
  const [pulseIntensity, setPulseIntensity] = useState(1);

  useEffect(() => {
    const stepInterval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < discoverySteps.length - 1) {
          return prev + 1;
        } else {
          clearInterval(stepInterval);
          // Start finding matches animation
          setTimeout(() => {
            setIsSearching(false);
            const matchInterval = setInterval(() => {
              setFoundMatches(prev => {
                if (prev < mockDiscoveredMatches.length) {
                  return prev + 1;
                } else {
                  clearInterval(matchInterval);
                  setTimeout(() => {
                    onComplete(mockDiscoveredMatches);
                  }, 1000);
                  return prev;
                }
              });
            }, 800);
          }, 1000);
          return prev;
        }
      });
    }, 1500);

    // Pulse intensity animation
    const pulseInterval = setInterval(() => {
      setPulseIntensity(prev => prev === 1 ? 1.5 : 1);
    }, 1000);

    return () => {
      clearInterval(stepInterval);
      clearInterval(pulseInterval);
    };
  }, [onComplete]);

  const activeStep = discoverySteps[currentStep];

  return (
    <div className="h-full flex flex-col items-center justify-center p-6 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        {/* Radar-like scanning effect */}
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-purple-400/30"
          style={{
            width: '200%',
            height: '200%',
            left: '-50%',
            top: '-50%',
          }}
          animate={{
            rotate: [0, 360],
            scale: [0.5, 1.2, 0.5],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "linear",
          }}
        />
        
        <motion.div
          className="absolute inset-0 rounded-full border border-blue-400/20"
          style={{
            width: '150%',
            height: '150%',
            left: '-25%',
            top: '-25%',
          }}
          animate={{
            rotate: [360, 0],
            scale: [0.8, 1.5, 0.8],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "linear",
          }}
        />

        {/* Floating particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, Math.random() * 200 - 100],
              y: [0, Math.random() * 200 - 100],
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-md w-full">
        {/* AI Brain Center */}
        <motion.div
          className="mb-8 relative"
          style={{ scale: pulseIntensity }}
          transition={{ duration: 1, ease: "easeInOut" }}
        >
          <div className="relative mx-auto">
            {/* Outer ring */}
            <motion.div
              className="w-32 h-32 rounded-full border-4 border-purple-400/30 absolute inset-0"
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            />
            
            {/* Middle ring */}
            <motion.div
              className="w-24 h-24 rounded-full border-2 border-pink-400/40 absolute inset-4"
              animate={{ rotate: [360, 0] }}
              transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
            />
            
            {/* Core */}
            <div className={`
              w-16 h-16 rounded-full absolute inset-8 flex items-center justify-center
              bg-gradient-to-br ${activeStep.color} shadow-2xl
            `}>
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 180, 360]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <activeStep.icon className="w-8 h-8 text-white" />
              </motion.div>
            </div>

            {/* Signal waves */}
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute inset-0 rounded-full border border-white/20"
                style={{
                  width: `${140 + i * 20}%`,
                  height: `${140 + i * 20}%`,
                  left: `${-20 - i * 10}%`,
                  top: `${-20 - i * 10}%`,
                }}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0, 0.5],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.5,
                }}
              />
            ))}
          </div>
        </motion.div>

        {/* Status Text */}
        <AnimatePresence mode="wait">
          <motion.div
            key={isSearching ? currentStep : 'results'}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-8"
          >
            {isSearching ? (
              <>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  {activeStep.title}
                </h2>
                <p className="text-gray-600 mb-4">
                  {activeStep.description}
                </p>
                
                {/* Progress indicators */}
                <div className="flex justify-center gap-2 mb-4">
                  {discoverySteps.map((_, index) => (
                    <motion.div
                      key={index}
                      className={`w-2 h-2 rounded-full ${
                        index <= currentStep ? 'bg-purple-500' : 'bg-gray-300'
                      }`}
                      animate={index === currentStep ? {
                        scale: [1, 1.5, 1],
                        opacity: [0.5, 1, 0.5]
                      } : {}}
                      transition={{ duration: 1, repeat: Infinity }}
                    />
                  ))}
                </div>
              </>
            ) : (
              <>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  Matches Found!
                </h2>
                <p className="text-gray-600">
                  Discovered {foundMatches} perfect spots for you
                </p>
              </>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Found Matches Animation */}
        {!isSearching && (
          <motion.div 
            className="flex justify-center gap-4 mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {mockDiscoveredMatches.slice(0, foundMatches).map((match, index) => (
              <motion.div
                key={match.id}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: index * 0.3, type: "spring", bounce: 0.6 }}
                className="flex flex-col items-center"
              >
                <div className={`
                  w-12 h-12 rounded-xl flex items-center justify-center text-white shadow-lg
                  ${match.type === 'parking' ? 'bg-blue-500' : 
                    match.type === 'venue' ? 'bg-purple-500' : 'bg-green-500'}
                `}>
                  {match.type === 'parking' ? <Car className="w-6 h-6" /> :
                   match.type === 'venue' ? <Wine className="w-6 h-6" /> :
                   <Users className="w-6 h-6" />}
                </div>
                <span className="text-xs text-gray-600 mt-1 capitalize">
                  {match.type}
                </span>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Location Info */}
        <motion.div
          className="backdrop-blur-sm bg-white/20 rounded-2xl p-4 border border-white/30"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex items-center gap-3 text-gray-700">
            <MapPin className="w-5 h-5 text-purple-500" />
            <div className="text-left">
              <p className="font-medium">Searching near:</p>
              <p className="text-sm text-gray-600">{userData.address}</p>
            </div>
          </div>
          
          <div className="flex justify-center gap-3 mt-3 pt-3 border-t border-white/30">
            {Object.entries(userData.permissions).map(([key, enabled]) => {
              if (!enabled) return null;
              
              const icons = {
                gps: MapPin,
                wifi: Wifi,
                bluetooth: Bluetooth,
                camera: null,
                notifications: null,
              };
              
              const Icon = icons[key as keyof typeof icons];
              if (!Icon) return null;
              
              return (
                <motion.div
                  key={key}
                  className="flex items-center gap-1 text-green-600"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity, delay: Math.random() }}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-xs capitalize">{key}</span>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </div>
  );
}