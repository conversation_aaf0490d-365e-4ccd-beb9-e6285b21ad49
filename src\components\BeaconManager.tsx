import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import {
  Radio,
  Bluetooth,
  Wifi,
  MapPin,
  Settings,
  Plus,
  Edit,
  Trash2,
  Power,
  Battery,
  Signal,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  QrCode,
  Download,
  Upload,
  Save,
  X,
  Zap,
  Shield,
  Clock,
  Users,
  Activity,
  Network,
  Smartphone,
  Monitor,
  Server
} from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Switch } from './ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from './ui/tabs';
import { Progress } from './ui/progress';
import { Separator } from './ui/separator';

interface BeaconConfig {
  id: string;
  name: string;
  type: 'ibeacon' | 'eddystone' | 'bluetooth_le' | 'wifi_aware';
  uuid?: string;
  major?: number;
  minor?: number;
  transmissionPower: number;
  advertisingInterval: number;
  enabled: boolean;
  location: {
    x: number;
    y: number;
    floor: string;
    description: string;
  };
  services: string[];
  batteryLevel?: number;
  lastMaintenance: string;
  firmware: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
}

interface VenueLayout {
  id: string;
  name: string;
  floors: {
    id: string;
    name: string;
    map: string;
    beacons: string[];
  }[];
}

interface BeaconManagerProps {
  onBack: () => void;
  venueId: string;
}

export function BeaconManager({ onBack, venueId }: BeaconManagerProps) {
  const [activeTab, setActiveTab] = useState('beacons');
  const [beacons, setBeacons] = useState<BeaconConfig[]>([]);
  const [selectedBeacon, setSelectedBeacon] = useState<BeaconConfig | null>(null);
  const [isAddingBeacon, setIsAddingBeacon] = useState(false);
  const [isEditingBeacon, setIsEditingBeacon] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [venueLayout, setVenueLayout] = useState<VenueLayout | null>(null);

  // Mock beacon configurations
  const mockBeacons: BeaconConfig[] = [
    {
      id: 'beacon-001',
      name: 'Main Entrance',
      type: 'ibeacon',
      uuid: 'E2C56DB5-DFFB-48D2-B060-D0F5A71096E0',
      major: 1,
      minor: 1,
      transmissionPower: -20,
      advertisingInterval: 100,
      enabled: true,
      location: {
        x: 50,
        y: 20,
        floor: 'ground',
        description: 'Near main entrance doors'
      },
      services: ['welcome', 'check-in', 'navigation'],
      batteryLevel: 85,
      lastMaintenance: '2024-01-15',
      firmware: '2.1.4',
      status: 'online'
    },
    {
      id: 'beacon-002',
      name: 'Bar Area',
      type: 'eddystone',
      transmissionPower: -16,
      advertisingInterval: 200,
      enabled: true,
      location: {
        x: 150,
        y: 100,
        floor: 'ground',
        description: 'Main bar counter'
      },
      services: ['menu', 'ordering', 'payment', 'loyalty'],
      batteryLevel: 92,
      lastMaintenance: '2024-01-20',
      firmware: '1.8.2',
      status: 'online'
    },
    {
      id: 'beacon-003',
      name: 'VIP Section',
      type: 'ibeacon',
      uuid: 'E2C56DB5-DFFB-48D2-B060-D0F5A71096E0',
      major: 1,
      minor: 3,
      transmissionPower: -12,
      advertisingInterval: 150,
      enabled: true,
      location: {
        x: 250,
        y: 150,
        floor: 'ground',
        description: 'VIP seating area'
      },
      services: ['vip-services', 'concierge', 'premium-menu'],
      batteryLevel: 78,
      lastMaintenance: '2024-01-10',
      firmware: '2.1.4',
      status: 'online'
    },
    {
      id: 'beacon-004',
      name: 'Restroom Area',
      type: 'bluetooth_le',
      transmissionPower: -24,
      advertisingInterval: 500,
      enabled: false,
      location: {
        x: 80,
        y: 200,
        floor: 'ground',
        description: 'Restroom corridor'
      },
      services: ['navigation', 'facility-info'],
      batteryLevel: 45,
      lastMaintenance: '2023-12-15',
      firmware: '1.5.1',
      status: 'maintenance'
    },
    {
      id: 'beacon-005',
      name: 'Rooftop Terrace',
      type: 'wifi_aware',
      transmissionPower: -8,
      advertisingInterval: 300,
      enabled: true,
      location: {
        x: 200,
        y: 80,
        floor: 'rooftop',
        description: 'Outdoor terrace area'
      },
      services: ['ambiance', 'weather', 'events'],
      batteryLevel: 67,
      lastMaintenance: '2024-01-25',
      firmware: '3.0.1',
      status: 'online'
    }
  ];

  const mockVenueLayout: VenueLayout = {
    id: venueId,
    name: 'Rooftop Lounge',
    floors: [
      {
        id: 'ground',
        name: 'Ground Floor',
        map: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800',
        beacons: ['beacon-001', 'beacon-002', 'beacon-003', 'beacon-004']
      },
      {
        id: 'rooftop',
        name: 'Rooftop Terrace',
        map: 'https://images.unsplash.com/photo-1566417713940-fe7c737a9ef2?w=800',
        beacons: ['beacon-005']
      }
    ]
  };

  const [newBeacon, setNewBeacon] = useState<Partial<BeaconConfig>>({
    name: '',
    type: 'ibeacon',
    transmissionPower: -20,
    advertisingInterval: 100,
    enabled: true,
    location: {
      x: 0,
      y: 0,
      floor: 'ground',
      description: ''
    },
    services: []
  });

  useEffect(() => {
    setBeacons(mockBeacons);
    setVenueLayout(mockVenueLayout);
  }, []);

  const filteredBeacons = beacons.filter(beacon => {
    const matchesSearch = beacon.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         beacon.id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || beacon.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: BeaconConfig['status']) => {
    switch (status) {
      case 'online': return 'text-green-500';
      case 'offline': return 'text-red-500';
      case 'error': return 'text-red-500';
      case 'maintenance': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: BeaconConfig['status']) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'offline': return <X className="w-4 h-4 text-red-500" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'maintenance': return <Settings className="w-4 h-4 text-yellow-500" />;
      default: return <Radio className="w-4 h-4 text-gray-500" />;
    }
  };

  const getBeaconTypeIcon = (type: BeaconConfig['type']) => {
    switch (type) {
      case 'ibeacon': return <Radio className="w-4 h-4" />;
      case 'eddystone': return <Zap className="w-4 h-4" />;
      case 'bluetooth_le': return <Bluetooth className="w-4 h-4" />;
      case 'wifi_aware': return <Wifi className="w-4 h-4" />;
      default: return <Radio className="w-4 h-4" />;
    }
  };

  const getBatteryColor = (level: number) => {
    if (level > 60) return 'text-green-500';
    if (level > 30) return 'text-yellow-500';
    return 'text-red-500';
  };

  const handleAddBeacon = () => {
    if (newBeacon.name && newBeacon.type) {
      const beacon: BeaconConfig = {
        id: `beacon-${Date.now()}`,
        name: newBeacon.name,
        type: newBeacon.type,
        transmissionPower: newBeacon.transmissionPower || -20,
        advertisingInterval: newBeacon.advertisingInterval || 100,
        enabled: newBeacon.enabled || true,
        location: newBeacon.location || {
          x: 0,
          y: 0,
          floor: 'ground',
          description: ''
        },
        services: newBeacon.services || [],
        batteryLevel: 100,
        lastMaintenance: new Date().toISOString().split('T')[0],
        firmware: '2.1.4',
        status: 'online'
      };

      if (beacon.type === 'ibeacon') {
        beacon.uuid = 'E2C56DB5-DFFB-48D2-B060-D0F5A71096E0';
        beacon.major = 1;
        beacon.minor = Math.floor(Math.random() * 1000) + 1;
      }

      setBeacons(prev => [...prev, beacon]);
      setIsAddingBeacon(false);
      setNewBeacon({
        name: '',
        type: 'ibeacon',
        transmissionPower: -20,
        advertisingInterval: 100,
        enabled: true,
        location: {
          x: 0,
          y: 0,
          floor: 'ground',
          description: ''
        },
        services: []
      });
    }
  };

  const handleUpdateBeacon = (beacon: BeaconConfig) => {
    setBeacons(prev => prev.map(b => b.id === beacon.id ? beacon : b));
    setSelectedBeacon(beacon);
    setIsEditingBeacon(false);
  };

  const handleDeleteBeacon = (beaconId: string) => {
    setBeacons(prev => prev.filter(b => b.id !== beaconId));
    if (selectedBeacon?.id === beaconId) {
      setSelectedBeacon(null);
    }
  };

  const handleToggleBeacon = (beaconId: string) => {
    setBeacons(prev => prev.map(b => 
      b.id === beaconId ? { ...b, enabled: !b.enabled } : b
    ));
  };

  const renderBeaconList = () => (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search beacons..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="online">Online</SelectItem>
            <SelectItem value="offline">Offline</SelectItem>
            <SelectItem value="maintenance">Maintenance</SelectItem>
            <SelectItem value="error">Error</SelectItem>
          </SelectContent>
        </Select>
        <Dialog open={isAddingBeacon} onOpenChange={setIsAddingBeacon}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add Beacon
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Beacon</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Beacon Name</Label>
                <Input
                  id="name"
                  value={newBeacon.name || ''}
                  onChange={(e) => setNewBeacon(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Main Entrance"
                />
              </div>
              
              <div>
                <Label htmlFor="type">Beacon Type</Label>
                <Select
                  value={newBeacon.type}
                  onValueChange={(value) => setNewBeacon(prev => ({ ...prev, type: value as BeaconConfig['type'] }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ibeacon">iBeacon</SelectItem>
                    <SelectItem value="eddystone">Eddystone</SelectItem>
                    <SelectItem value="bluetooth_le">Bluetooth LE</SelectItem>
                    <SelectItem value="wifi_aware">WiFi Aware</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="description">Location Description</Label>
                <Textarea
                  id="description"
                  value={newBeacon.location?.description || ''}
                  onChange={(e) => setNewBeacon(prev => ({
                    ...prev,
                    location: { ...prev.location!, description: e.target.value }
                  }))}
                  placeholder="Describe the beacon location..."
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={handleAddBeacon} className="flex-1">
                  Add Beacon
                </Button>
                <Button variant="outline" onClick={() => setIsAddingBeacon(false)} className="flex-1">
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Beacon Cards */}
      <div className="grid gap-4">
        {filteredBeacons.map((beacon) => (
          <Card 
            key={beacon.id} 
            className={`p-4 cursor-pointer transition-colors ${
              selectedBeacon?.id === beacon.id ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedBeacon(beacon)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  {getBeaconTypeIcon(beacon.type)}
                </div>
                
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium">{beacon.name}</h4>
                    <Badge variant="outline" className="text-xs">
                      {beacon.type.toUpperCase()}
                    </Badge>
                    {getStatusIcon(beacon.status)}
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{beacon.id}</span>
                    <span>{beacon.location.description}</span>
                    {beacon.batteryLevel && (
                      <div className="flex items-center gap-1">
                        <Battery className="w-3 h-3" />
                        <span className={getBatteryColor(beacon.batteryLevel)}>
                          {beacon.batteryLevel}%
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Switch
                  checked={beacon.enabled}
                  onCheckedChange={() => handleToggleBeacon(beacon.id)}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedBeacon(beacon);
                    setIsEditingBeacon(true);
                  }}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteBeacon(beacon.id);
                  }}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderBeaconDetails = () => {
    if (!selectedBeacon) {
      return (
        <Card className="p-8 text-center">
          <Radio className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">Select a Beacon</h3>
          <p className="text-muted-foreground">
            Choose a beacon from the list to view its details and configuration
          </p>
        </Card>
      );
    }

    return (
      <div className="space-y-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                {getBeaconTypeIcon(selectedBeacon.type)}
              </div>
              <div>
                <h3 className="text-xl font-bold">{selectedBeacon.name}</h3>
                <p className="text-muted-foreground">{selectedBeacon.id}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {getStatusIcon(selectedBeacon.status)}
              <span className={`text-sm font-medium ${getStatusColor(selectedBeacon.status)}`}>
                {selectedBeacon.status.charAt(0).toUpperCase() + selectedBeacon.status.slice(1)}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Configuration</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="font-medium">{selectedBeacon.type.toUpperCase()}</span>
                </div>
                {selectedBeacon.uuid && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">UUID:</span>
                    <span className="font-mono text-xs">{selectedBeacon.uuid}</span>
                  </div>
                )}
                {selectedBeacon.major && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Major:</span>
                    <span className="font-medium">{selectedBeacon.major}</span>
                  </div>
                )}
                {selectedBeacon.minor && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Minor:</span>
                    <span className="font-medium">{selectedBeacon.minor}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-muted-foreground">TX Power:</span>
                  <span className="font-medium">{selectedBeacon.transmissionPower} dBm</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Interval:</span>
                  <span className="font-medium">{selectedBeacon.advertisingInterval} ms</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3">Status & Health</h4>
              <div className="space-y-2 text-sm">
                {selectedBeacon.batteryLevel && (
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Battery:</span>
                    <div className="flex items-center gap-2">
                      <Progress value={selectedBeacon.batteryLevel} className="w-16 h-2" />
                      <span className={`font-medium ${getBatteryColor(selectedBeacon.batteryLevel)}`}>
                        {selectedBeacon.batteryLevel}%
                      </span>
                    </div>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Firmware:</span>
                  <span className="font-medium">{selectedBeacon.firmware}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Last Maintenance:</span>
                  <span className="font-medium">{selectedBeacon.lastMaintenance}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Enabled:</span>
                  <Switch
                    checked={selectedBeacon.enabled}
                    onCheckedChange={() => handleToggleBeacon(selectedBeacon.id)}
                  />
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-medium mb-3">Location</h4>
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Floor:</span>
                <p className="font-medium">{selectedBeacon.location.floor}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Coordinates:</span>
                <p className="font-medium">
                  X: {selectedBeacon.location.x}, Y: {selectedBeacon.location.y}
                </p>
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Description:</span>
              <p className="mt-1">{selectedBeacon.location.description}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-medium mb-3">Services</h4>
          {selectedBeacon.services.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {selectedBeacon.services.map((service, index) => (
                <Badge key={index} variant="secondary">
                  {service}
                </Badge>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">No services configured</p>
          )}
        </Card>
      </div>
    );
  };

  const renderVenueMap = () => (
    <div className="space-y-6">
      {venueLayout?.floors.map((floor) => (
        <Card key={floor.id} className="p-6">
          <h3 className="text-lg font-semibold mb-4">{floor.name}</h3>
          
          <div className="relative bg-gray-100 rounded-lg overflow-hidden" style={{ height: '400px' }}>
            <img
              src={floor.map}
              alt={floor.name}
              className="w-full h-full object-cover opacity-50"
            />
            
            {/* Beacon overlays */}
            {beacons
              .filter(beacon => beacon.location.floor === floor.id)
              .map((beacon) => (
                <div
                  key={beacon.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                  style={{
                    left: `${(beacon.location.x / 300) * 100}%`,
                    top: `${(beacon.location.y / 250) * 100}%`
                  }}
                  onClick={() => setSelectedBeacon(beacon)}
                >
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    beacon.enabled 
                      ? beacon.status === 'online' 
                        ? 'bg-green-500' 
                        : 'bg-red-500'
                      : 'bg-gray-400'
                  }`}>
                    {getBeaconTypeIcon(beacon.type)}
                  </div>
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                    {beacon.name}
                  </div>
                </div>
              ))}
          </div>
          
          <div className="mt-4 flex flex-wrap gap-2">
            {floor.beacons.map((beaconId) => {
              const beacon = beacons.find(b => b.id === beaconId);
              return beacon ? (
                <Badge 
                  key={beaconId} 
                  variant={beacon.enabled ? "default" : "secondary"}
                  className="cursor-pointer"
                  onClick={() => setSelectedBeacon(beacon)}
                >
                  {beacon.name}
                </Badge>
              ) : null;
            })}
          </div>
        </Card>
      ))}
    </div>
  );

  const renderAnalytics = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-2xl font-bold">{beacons.filter(b => b.status === 'online').length}</p>
              <p className="text-sm text-muted-foreground">Online</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-red-100 flex items-center justify-center">
              <X className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-2xl font-bold">{beacons.filter(b => b.status === 'offline').length}</p>
              <p className="text-sm text-muted-foreground">Offline</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-yellow-100 flex items-center justify-center">
              <Settings className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-2xl font-bold">{beacons.filter(b => b.status === 'maintenance').length}</p>
              <p className="text-sm text-muted-foreground">Maintenance</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <Battery className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-2xl font-bold">
                {Math.round(beacons.reduce((acc, b) => acc + (b.batteryLevel || 0), 0) / beacons.length)}%
              </p>
              <p className="text-sm text-muted-foreground">Avg Battery</p>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Beacon Health Overview</h3>
        <div className="space-y-3">
          {beacons.map((beacon) => (
            <div key={beacon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
                  {getBeaconTypeIcon(beacon.type)}
                </div>
                <div>
                  <p className="font-medium">{beacon.name}</p>
                  <p className="text-sm text-muted-foreground">{beacon.id}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                {beacon.batteryLevel && (
                  <div className="flex items-center gap-2">
                    <Battery className="w-4 h-4" />
                    <Progress value={beacon.batteryLevel} className="w-16 h-2" />
                    <span className="text-sm font-medium w-10">
                      {beacon.batteryLevel}%
                    </span>
                  </div>
                )}
                {getStatusIcon(beacon.status)}
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  return (
    <div className="h-full bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_black_1px,_transparent_0)] bg-[size:40px_40px]" />
      </div>

      {/* Header */}
      <div className="relative z-10 bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={onBack}>
                ← Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <Settings className="w-6 h-6" />
                  Beacon Manager
                </h1>
                <p className="text-sm text-muted-foreground">
                  Configure and monitor venue beacons
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Badge variant="outline" className="flex items-center gap-1">
                <Activity className="w-3 h-3" />
                {beacons.filter(b => b.status === 'online').length}/{beacons.length} Online
              </Badge>
              
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Config
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="beacons">Beacons</TabsTrigger>
            <TabsTrigger value="map">Venue Map</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="beacons">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Beacon List</h3>
                  {renderBeaconList()}
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Beacon Details</h3>
                  {renderBeaconDetails()}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="map">
              {renderVenueMap()}
            </TabsContent>
            
            <TabsContent value="analytics">
              {renderAnalytics()}
            </TabsContent>
            
            <TabsContent value="settings">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Beacon Infrastructure Settings</h3>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Auto-Discovery</p>
                      <p className="text-sm text-muted-foreground">Automatically detect new beacons in range</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Health Monitoring</p>
                      <p className="text-sm text-muted-foreground">Monitor beacon status and battery levels</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Mesh Networking</p>
                      <p className="text-sm text-muted-foreground">Enable beacon-to-beacon communication</p>
                    </div>
                    <Switch />
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <p className="font-medium mb-2">Default Configuration</p>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="defaultPower">Default TX Power (dBm)</Label>
                        <Input id="defaultPower" type="number" defaultValue="-20" />
                      </div>
                      <div>
                        <Label htmlFor="defaultInterval">Default Interval (ms)</Label>
                        <Input id="defaultInterval" type="number" defaultValue="100" />
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}