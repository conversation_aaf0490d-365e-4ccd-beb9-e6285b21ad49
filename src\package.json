{"name": "bytspot", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:unit": "vitest run --reporter=verbose --coverage", "test:integration": "vitest run --config vitest.integration.config.ts", "test:component": "vitest run --config vitest.component.config.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:a11y": "pa11y-ci --sitemap https://localhost:3000/sitemap.xml", "test:watch": "vitest", "prepare": "husky install", "analyze": "npx vite-bundle-analyzer"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "@tanstack/react-query": "^4.28.0", "motion": "^10.18.0", "lucide-react": "^0.263.1", "clsx": "^1.2.1", "tailwind-merge": "^1.12.0", "zustand": "^4.3.7", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.4", "@radix-ui/react-select": "^1.2.1", "@radix-ui/react-checkbox": "^1.0.3", "@radix-ui/react-switch": "^1.0.2", "@radix-ui/react-tabs": "^1.0.3", "@radix-ui/react-toast": "^1.1.3", "@radix-ui/react-tooltip": "^1.0.5", "@radix-ui/react-alert-dialog": "^1.0.3", "@radix-ui/react-avatar": "^1.0.2", "@radix-ui/react-badge": "^1.0.1", "@radix-ui/react-card": "^1.0.1", "sonner": "^2.0.3", "@sentry/react": "^7.50.0", "@sentry/tracing": "^7.50.0", "web-vitals": "^3.3.1", "zod": "^3.21.4", "react-hook-form": "^7.43.9", "@hookform/resolvers": "^3.1.0", "date-fns": "^2.29.3", "recharts": "^2.6.2"}, "devDependencies": {"@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.2", "vite": "^4.3.2", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.23", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "eslint-plugin-jsx-a11y": "^6.7.1", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^13.2.1", "vitest": "^0.31.0", "@vitest/coverage-c8": "^0.31.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "jsdom": "^22.0.0", "msw": "^1.2.1", "@playwright/test": "^1.33.0", "pa11y-ci": "^3.0.1", "@lhci/cli": "^0.12.0", "vite-bundle-analyzer": "^0.5.1"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test:unit"}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}