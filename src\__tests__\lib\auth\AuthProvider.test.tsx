import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { AuthProvider, useAuth } from '../../../lib/auth/AuthProvider';
import { authService } from '../../../lib/auth/authService';
import type { User } from '../../../lib/auth/types';

// Mock the auth service
jest.mock('../../../lib/auth/authService', () => ({
  authService: {
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    getCurrentUser: jest.fn(),
    isAuthenticated: jest.fn(),
  },
}));

const mockAuthService = authService as jest.Mocked<typeof authService>;

// Test component to access auth context
const TestComponent = () => {
  const {
    user,
    isLoading,
    isAuthenticated,
    error,
    login,
    logout,
    register,
    hasRole,
    hasPermission,
  } = useAuth();

  return (
    <div>
      <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
      <div data-testid="authenticated">{isAuthenticated ? 'authenticated' : 'not-authenticated'}</div>
      <div data-testid="user">{user ? user.name : 'no-user'}</div>
      <div data-testid="error">{error || 'no-error'}</div>
      <div data-testid="has-consumer-role">{hasRole('consumer') ? 'yes' : 'no'}</div>
      <div data-testid="has-host-role">{hasRole('host') ? 'yes' : 'no'}</div>
      <div data-testid="has-venue-read">{hasPermission('venue', 'read') ? 'yes' : 'no'}</div>
      <button data-testid="login-btn" onClick={() => login({ email: '<EMAIL>', password: 'password' })}>
        Login
      </button>
      <button data-testid="logout-btn" onClick={() => logout()}>
        Logout
      </button>
      <button data-testid="register-btn" onClick={() => register({ email: '<EMAIL>', password: 'password', name: 'Test User' })}>
        Register
      </button>
    </div>
  );
};

const mockUser: User = {
  id: 'user-1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'consumer',
  permissions: [
    { resource: 'venue', actions: ['read'] },
  ],
  preferences: {
    permissions: {
      gps: true,
      wifi: true,
      bluetooth: true,
      camera: true,
      notifications: true,
      imu: true,
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const mockHostUser: User = {
  ...mockUser,
  role: 'host',
  permissions: [
    { resource: 'venue', actions: ['read', 'write', 'manage'] },
    { resource: 'analytics', actions: ['read'] },
  ],
};

describe('AuthProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console methods to reduce noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should start with loading state', () => {
      mockAuthService.isAuthenticated.mockReturnValue(false);
      
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('loading')).toHaveTextContent('loading');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('no-user');
    });

    it('should initialize with authenticated user if tokens exist', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('Test User');
    });

    it('should handle initialization error gracefully', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockRejectedValue(new Error('Network error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('no-user');
    });

    it('should timeout initialization after 3 seconds', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Fast-forward time to trigger timeout
      act(() => {
        jest.advanceTimersByTime(3100);
      });

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
    });
  });

  describe('Login', () => {
    it('should handle successful login', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false);
      mockAuthService.login.mockResolvedValue({ user: mockUser, tokens: {} as any });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      act(() => {
        screen.getByTestId('login-btn').click();
      });

      expect(screen.getByTestId('loading')).toHaveTextContent('loading');

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('Test User');
      expect(mockAuthService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
      });
    });

    it('should handle login error', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false);
      mockAuthService.login.mockRejectedValue(new Error('Invalid credentials'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      act(() => {
        screen.getByTestId('login-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Invalid credentials');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('no-user');
    });
  });

  describe('Registration', () => {
    it('should handle successful registration', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false);
      mockAuthService.register.mockResolvedValue({ user: mockUser, tokens: {} as any });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      act(() => {
        screen.getByTestId('register-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('Test User');
      expect(mockAuthService.register).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
        name: 'Test User',
      });
    });

    it('should handle registration error', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false);
      mockAuthService.register.mockRejectedValue(new Error('Email already exists'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      act(() => {
        screen.getByTestId('register-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Email already exists');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
    });
  });

  describe('Logout', () => {
    it('should handle successful logout', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser);
      mockAuthService.logout.mockResolvedValue();

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
      });

      act(() => {
        screen.getByTestId('logout-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
      });

      expect(screen.getByTestId('user')).toHaveTextContent('no-user');
      expect(mockAuthService.logout).toHaveBeenCalled();
    });

    it('should clear local state even if logout API fails', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser);
      mockAuthService.logout.mockRejectedValue(new Error('Network error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
      });

      act(() => {
        screen.getByTestId('logout-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
      });

      expect(screen.getByTestId('user')).toHaveTextContent('no-user');
    });
  });

  describe('Role Checking', () => {
    it('should correctly identify user roles', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('has-consumer-role')).toHaveTextContent('yes');
      });

      expect(screen.getByTestId('has-host-role')).toHaveTextContent('no');
    });

    it('should correctly identify host roles', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValue(mockHostUser);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('has-host-role')).toHaveTextContent('yes');
      });

      expect(screen.getByTestId('has-consumer-role')).toHaveTextContent('no');
    });
  });

  describe('Permission Checking', () => {
    it('should correctly check user permissions', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('has-venue-read')).toHaveTextContent('yes');
      });
    });

    it('should return false for permissions user does not have', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.getCurrentUser.mockResolvedValue({
        ...mockUser,
        permissions: [],
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('has-venue-read')).toHaveTextContent('no');
      });
    });

    it('should return false when user is not authenticated', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('has-venue-read')).toHaveTextContent('no');
      });
    });
  });

  describe('Error Handling', () => {
    it('should clear error after 5 seconds', async () => {
      jest.useFakeTimers();
      
      mockAuthService.isAuthenticated.mockReturnValue(false);
      mockAuthService.login.mockRejectedValue(new Error('Test error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      act(() => {
        screen.getByTestId('login-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Test error');
      });

      act(() => {
        jest.advanceTimersByTime(5000);
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('no-error');
      });

      jest.useRealTimers();
    });
  });

  describe('Context Usage', () => {
    it('should throw error when useAuth is called outside provider', () => {
      // Mock console.error to prevent error output in test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestComponent />);
      }).toThrow('useAuth must be used within an AuthProvider');
      
      consoleSpy.mockRestore();
    });
  });
});