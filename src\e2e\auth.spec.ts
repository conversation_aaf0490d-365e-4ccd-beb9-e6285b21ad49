import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display login form', async ({ page }) => {
    // Should redirect to login for unauthenticated users
    await expect(page).toHaveURL(/.*login/);
    
    // Check login form elements
    await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
  });

  test('should show validation errors for empty form', async ({ page }) => {
    await page.getByRole('button', { name: /sign in/i }).click();
    
    await expect(page.getByText('Email is required')).toBeVisible();
    await expect(page.getByText('Password is required')).toBeVisible();
  });

  test('should show error for invalid email format', async ({ page }) => {
    await page.getByLabel(/email/i).fill('invalid-email');
    await page.getByLabel(/password/i).fill('password123');
    await page.getByRole('button', { name: /sign in/i }).click();
    
    await expect(page.getByText('Please enter a valid email address')).toBeVisible();
  });

  test('should show error for short password', async ({ page }) => {
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('123');
    await page.getByRole('button', { name: /sign in/i }).click();
    
    await expect(page.getByText('Password must be at least 6 characters')).toBeVisible();
  });

  test('should toggle password visibility', async ({ page }) => {
    const passwordInput = page.getByLabel(/password/i);
    const toggleButton = page.getByRole('button', { name: '' }).nth(1); // Eye icon
    
    await passwordInput.fill('password123');
    
    // Initially password should be hidden
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Click toggle to show password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'text');
    
    // Click toggle to hide password again
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('should handle login attempt', async ({ page }) => {
    // Mock successful login response
    await page.route('**/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'consumer',
            permissions: [],
            preferences: {
              permissions: {
                gps: true,
                wifi: true,
                bluetooth: true,
                camera: true,
                notifications: true,
                imu: true,
              },
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          tokens: {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            expiresAt: Date.now() + 3600000,
          },
        }),
      });
    });

    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('password123');
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Should redirect to app after successful login
    await expect(page).toHaveURL(/.*app/);
  });

  test('should handle login error', async ({ page }) => {
    // Mock failed login response
    await page.route('**/auth/login', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Invalid credentials',
        }),
      });
    });

    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('wrongpassword');
    await page.getByRole('button', { name: /sign in/i }).click();
    
    await expect(page.getByText('Invalid credentials')).toBeVisible();
  });

  test('should navigate to register form', async ({ page }) => {
    await page.getByRole('link', { name: /sign up/i }).click();
    
    await expect(page).toHaveURL(/.*register/);
    await expect(page.getByRole('heading', { name: /create account/i })).toBeVisible();
  });

  test('should navigate to forgot password', async ({ page }) => {
    await page.getByRole('link', { name: /forgot password/i }).click();
    
    await expect(page).toHaveURL(/.*forgot-password/);
    await expect(page.getByRole('heading', { name: /reset password/i })).toBeVisible();
  });

  test('should check remember me functionality', async ({ page }) => {
    const rememberMeCheckbox = page.getByLabel(/remember me/i);
    
    // Initially unchecked
    await expect(rememberMeCheckbox).not.toBeChecked();
    
    // Check the box
    await rememberMeCheckbox.check();
    await expect(rememberMeCheckbox).toBeChecked();
    
    // Uncheck the box
    await rememberMeCheckbox.uncheck();
    await expect(rememberMeCheckbox).not.toBeChecked();
  });

  test('should show loading state during login', async ({ page }) => {
    // Mock slow login response
    await page.route('**/auth/login', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: { id: 'test' },
          tokens: { accessToken: 'token' },
        }),
      });
    });

    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('password123');
    
    const submitButton = page.getByRole('button', { name: /sign in/i });
    await submitButton.click();
    
    // Check loading state
    await expect(page.getByText('Signing in...')).toBeVisible();
    await expect(submitButton).toBeDisabled();
  });

  test('should be accessible', async ({ page }) => {
    // Check form has proper labels
    const emailInput = page.getByLabel(/email/i);
    const passwordInput = page.getByLabel(/password/i);
    
    await expect(emailInput).toHaveAttribute('type', 'email');
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Check form can be navigated with keyboard
    await emailInput.focus();
    await page.keyboard.press('Tab');
    await expect(passwordInput).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByLabel(/remember me/i)).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByRole('button', { name: /sign in/i })).toBeFocused();
  });
});