import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import {
  Wifi,
  Bluetooth,
  MapPin,
  Radio,
  Signal,
  Zap,
  Shield,
  Users,
  Clock,
  Battery,
  Settings,
  Scan,
  QrCode,
  Navigation as NavigationIcon,
  Smartphone,
  WifiOff,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronUp,
  Activity,
  Network
} from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Switch } from './ui/switch';
import { Separator } from './ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';

interface BeaconDevice {
  id: string;
  name: string;
  type: 'ibeacon' | 'eddystone' | 'bluetooth' | 'wifi';
  rssi: number;
  distance: number;
  battery?: number;
  lastSeen: string;
  status: 'active' | 'inactive' | 'error';
  services?: string[];
  venue?: {
    id: string;
    name: string;
    category: string;
  };
}

interface WiFiNetwork {
  ssid: string;
  signal: number;
  security: 'open' | 'wep' | 'wpa' | 'wpa2' | 'wpa3';
  connected: boolean;
  venue?: {
    id: string;
    name: string;
    category: string;
  };
}

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp: string;
  source: 'gps' | 'wifi' | 'bluetooth' | 'beacon' | 'hybrid';
}

interface VenueInfo {
  id: string;
  name: string;
  category: string;
  description: string;
  features: string[];
  hours: {
    open: string;
    close: string;
  };
  capacity: {
    current: number;
    maximum: number;
  };
  offers: string[];
  rating: number;
  image: string;
}

interface BeaconSignageProps {
  onBack: () => void;
  venueId?: string;
  mode?: 'kiosk' | 'mobile' | 'embedded';
}

export function BeaconSignage({ onBack, venueId, mode = 'kiosk' }: BeaconSignageProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isScanning, setIsScanning] = useState(false);
  const [discoveredBeacons, setDiscoveredBeacons] = useState<BeaconDevice[]>([]);
  const [availableWiFi, setAvailableWiFi] = useState<WiFiNetwork[]>([]);
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [currentVenue, setCurrentVenue] = useState<VenueInfo | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [systemStatus, setSystemStatus] = useState({
    bluetooth: true,
    wifi: true,
    location: true,
    beacons: true
  });

  const scanInterval = useRef<NodeJS.Timeout>();

  // Mock venue data
  const mockVenueInfo: VenueInfo = {
    id: 'rooftop-lounge',
    name: 'Rooftop Lounge',
    category: 'Bar & Restaurant',
    description: 'Premium rooftop experience with craft cocktails and city views',
    features: ['City Views', 'Live DJ', 'Craft Cocktails', 'Outdoor Seating', 'VIP Area'],
    hours: {
      open: '5:00 PM',
      close: '2:00 AM'
    },
    capacity: {
      current: 78,
      maximum: 120
    },
    offers: ['Happy Hour 5-7 PM', 'Live Music Tonight', 'VIP Table Available'],
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1566417713940-fe7c737a9ef2?w=800'
  };

  // Mock beacon data
  const mockBeacons: BeaconDevice[] = [
    {
      id: 'beacon-entrance',
      name: 'Rooftop Entrance',
      type: 'ibeacon',
      rssi: -45,
      distance: 2.5,
      battery: 85,
      lastSeen: '2 seconds ago',
      status: 'active',
      services: ['proximity', 'navigation'],
      venue: { id: 'rooftop-lounge', name: 'Rooftop Lounge', category: 'entrance' }
    },
    {
      id: 'beacon-bar',
      name: 'Main Bar Area',
      type: 'eddystone',
      rssi: -38,
      distance: 1.8,
      battery: 92,
      lastSeen: '1 second ago',
      status: 'active',
      services: ['ordering', 'menu', 'payment'],
      venue: { id: 'rooftop-lounge', name: 'Rooftop Lounge', category: 'bar' }
    },
    {
      id: 'beacon-vip',
      name: 'VIP Section',
      type: 'ibeacon',
      rssi: -62,
      distance: 5.2,
      battery: 78,
      lastSeen: '3 seconds ago',
      status: 'active',
      services: ['vip-services', 'concierge'],
      venue: { id: 'rooftop-lounge', name: 'Rooftop Lounge', category: 'vip' }
    },
    {
      id: 'beacon-restroom',
      name: 'Restroom Beacon',
      type: 'bluetooth',
      rssi: -55,
      distance: 4.1,
      battery: 45,
      lastSeen: '5 seconds ago',
      status: 'active',
      services: ['navigation'],
      venue: { id: 'rooftop-lounge', name: 'Rooftop Lounge', category: 'facility' }
    }
  ];

  // Mock WiFi networks
  const mockWiFiNetworks: WiFiNetwork[] = [
    {
      ssid: 'Rooftop_Guest',
      signal: 85,
      security: 'wpa2',
      connected: true,
      venue: { id: 'rooftop-lounge', name: 'Rooftop Lounge', category: 'guest' }
    },
    {
      ssid: 'Rooftop_Premium',
      signal: 92,
      security: 'wpa3',
      connected: false,
      venue: { id: 'rooftop-lounge', name: 'Rooftop Lounge', category: 'premium' }
    },
    {
      ssid: 'Bytspot_Mesh',
      signal: 78,
      security: 'wpa3',
      connected: false,
      venue: { id: 'network', name: 'Bytspot Network', category: 'mesh' }
    },
    {
      ssid: 'Downtown_Free',
      signal: 65,
      security: 'open',
      connected: false
    }
  ];

  const startBeaconScan = () => {
    setIsScanning(true);
    setDiscoveredBeacons(mockBeacons);
    setAvailableWiFi(mockWiFiNetworks);
    setCurrentVenue(mockVenueInfo);
    
    // Mock location data
    setCurrentLocation({
      latitude: 40.7589,
      longitude: -73.9851,
      accuracy: 3.2,
      altitude: 125,
      heading: 45,
      timestamp: new Date().toISOString(),
      source: 'hybrid'
    });

    // Simulate real-time updates
    scanInterval.current = setInterval(() => {
      setDiscoveredBeacons(prev => prev.map(beacon => ({
        ...beacon,
        rssi: beacon.rssi + (Math.random() - 0.5) * 4,
        distance: Math.max(0.5, beacon.distance + (Math.random() - 0.5) * 0.8),
        lastSeen: Math.floor(Math.random() * 5) + ' seconds ago'
      })));
    }, 2000);
  };

  const stopBeaconScan = () => {
    setIsScanning(false);
    if (scanInterval.current) {
      clearInterval(scanInterval.current);
    }
  };

  const getSignalStrength = (rssi: number): { strength: number; label: string; color: string } => {
    if (rssi > -40) return { strength: 100, label: 'Excellent', color: 'text-green-500' };
    if (rssi > -55) return { strength: 75, label: 'Good', color: 'text-blue-500' };
    if (rssi > -70) return { strength: 50, label: 'Fair', color: 'text-yellow-500' };
    return { strength: 25, label: 'Poor', color: 'text-red-500' };
  };

  const getBeaconIcon = (type: BeaconDevice['type']) => {
    switch (type) {
      case 'ibeacon':
        return <Radio className="w-4 h-4" />;
      case 'eddystone':
        return <Zap className="w-4 h-4" />;
      case 'bluetooth':
        return <Bluetooth className="w-4 h-4" />;
      case 'wifi':
        return <Wifi className="w-4 h-4" />;
      default:
        return <Signal className="w-4 h-4" />;
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Current Venue */}
      {currentVenue && (
        <Card className="p-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-200">
          <div className="flex items-start gap-4">
            <div className="w-16 h-16 rounded-xl overflow-hidden">
              <img 
                src={currentVenue.image} 
                alt={currentVenue.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-xl font-bold">{currentVenue.name}</h3>
                <Badge variant="secondary">{currentVenue.category}</Badge>
              </div>
              <p className="text-muted-foreground mb-3">{currentVenue.description}</p>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="text-sm font-medium">Hours</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {currentVenue.hours.open} - {currentVenue.hours.close}
                  </p>
                </div>
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <Users className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium">Capacity</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress 
                      value={(currentVenue.capacity.current / currentVenue.capacity.maximum) * 100} 
                      className="flex-1 h-2"
                    />
                    <span className="text-sm text-muted-foreground">
                      {currentVenue.capacity.current}/{currentVenue.capacity.maximum}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {currentVenue.offers.slice(0, 2).map((offer, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {offer}
                  </Badge>
                ))}
                {currentVenue.offers.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{currentVenue.offers.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* System Status */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Activity className="w-5 h-5 text-blue-500" />
          System Status
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { key: 'bluetooth', label: 'Bluetooth', icon: Bluetooth, color: 'blue' },
            { key: 'wifi', label: 'WiFi', icon: Wifi, color: 'green' },
            { key: 'location', label: 'Location', icon: MapPin, color: 'purple' },
            { key: 'beacons', label: 'Beacons', icon: Radio, color: 'orange' }
          ].map(({ key, label, icon: Icon, color }) => (
            <div key={key} className="text-center">
              <div className={`w-12 h-12 rounded-full bg-${color}-100 flex items-center justify-center mx-auto mb-2`}>
                <Icon className={`w-6 h-6 text-${color}-600`} />
              </div>
              <p className="text-sm font-medium">{label}</p>
              <div className="flex items-center justify-center gap-1 mt-1">
                {systemStatus[key as keyof typeof systemStatus] ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <AlertTriangle className="w-4 h-4 text-red-500" />
                )}
                <span className={`text-xs ${systemStatus[key as keyof typeof systemStatus] ? 'text-green-600' : 'text-red-600'}`}>
                  {systemStatus[key as keyof typeof systemStatus] ? 'Active' : 'Error'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <Radio className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-2xl font-bold">{discoveredBeacons.length}</p>
              <p className="text-sm text-muted-foreground">Active Beacons</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center">
              <Wifi className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-2xl font-bold">{availableWiFi.length}</p>
              <p className="text-sm text-muted-foreground">WiFi Networks</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center">
              <MapPin className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-2xl font-bold">
                {currentLocation?.accuracy.toFixed(1)}m
              </p>
              <p className="text-sm text-muted-foreground">Location Accuracy</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderBeacons = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Discovered Beacons</h3>
          <p className="text-sm text-muted-foreground">
            {discoveredBeacons.length} beacons detected in range
          </p>
        </div>
        <Button
          onClick={isScanning ? stopBeaconScan : startBeaconScan}
          variant={isScanning ? "destructive" : "default"}
          className="flex items-center gap-2"
        >
          {isScanning ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              Stop Scan
            </>
          ) : (
            <>
              <Scan className="w-4 h-4" />
              Start Scan
            </>
          )}
        </Button>
      </div>

      <div className="space-y-3">
        {discoveredBeacons.map((beacon) => {
          const signal = getSignalStrength(beacon.rssi);
          
          return (
            <Card key={beacon.id} className="p-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  {getBeaconIcon(beacon.type)}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium">{beacon.name}</h4>
                    <Badge variant="outline" className="text-xs">
                      {beacon.type.toUpperCase()}
                    </Badge>
                    {beacon.status === 'active' ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Distance</p>
                      <p className="font-medium">{beacon.distance.toFixed(1)}m</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Signal</p>
                      <p className={`font-medium ${signal.color}`}>
                        {signal.label} ({beacon.rssi}dBm)
                      </p>
                    </div>
                    {beacon.battery && (
                      <div>
                        <p className="text-muted-foreground">Battery</p>
                        <div className="flex items-center gap-2">
                          <Battery className="w-4 h-4" />
                          <span className="font-medium">{beacon.battery}%</span>
                        </div>
                      </div>
                    )}
                    <div>
                      <p className="text-muted-foreground">Last Seen</p>
                      <p className="font-medium">{beacon.lastSeen}</p>
                    </div>
                  </div>

                  {beacon.services && beacon.services.length > 0 && (
                    <div className="mt-3">
                      <p className="text-sm text-muted-foreground mb-2">Available Services:</p>
                      <div className="flex flex-wrap gap-1">
                        {beacon.services.map((service, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {service}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderWiFi = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">Available Networks</h3>
        <p className="text-sm text-muted-foreground">
          {availableWiFi.length} networks detected
        </p>
      </div>

      <div className="space-y-3">
        {availableWiFi.map((network, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                  {network.connected ? (
                    <Wifi className="w-6 h-6 text-green-600" />
                  ) : (
                    <WifiOff className="w-6 h-6 text-gray-400" />
                  )}
                </div>
                
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium">{network.ssid}</h4>
                    {network.connected && (
                      <Badge variant="default" className="text-xs">
                        Connected
                      </Badge>
                    )}
                    {network.venue && (
                      <Badge variant="outline" className="text-xs">
                        {network.venue.name}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Signal className="w-4 h-4" />
                      <span>{network.signal}%</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      <span className="capitalize">{network.security}</span>
                    </div>
                  </div>
                </div>
              </div>

              <Button
                variant={network.connected ? "secondary" : "default"}
                size="sm"
                disabled={network.connected}
              >
                {network.connected ? "Connected" : "Connect"}
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderLocation = () => (
    <div className="space-y-6">
      {currentLocation && (
        <>
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-purple-500" />
              Current Location
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Coordinates</p>
                <p className="font-mono text-sm">
                  {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Accuracy</p>
                <p className="font-medium">{currentLocation.accuracy.toFixed(1)} meters</p>
              </div>
              {currentLocation.altitude && (
                <div>
                  <p className="text-sm text-muted-foreground">Altitude</p>
                  <p className="font-medium">{currentLocation.altitude.toFixed(1)} meters</p>
                </div>
              )}
              <div>
                <p className="text-sm text-muted-foreground">Source</p>
                <Badge variant="outline" className="text-xs capitalize">
                  {currentLocation.source}
                </Badge>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Location Enhancement</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Beacon Triangulation</p>
                  <p className="text-sm text-muted-foreground">Use multiple beacons for precise indoor positioning</p>
                </div>
                <Switch checked={true} />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">WiFi Positioning</p>
                  <p className="text-sm text-muted-foreground">Enhance location accuracy using WiFi signals</p>
                </div>
                <Switch checked={true} />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Motion Sensors</p>
                  <p className="text-sm text-muted-foreground">Use accelerometer and gyroscope for movement tracking</p>
                </div>
                <Switch checked={false} />
              </div>
            </div>
          </Card>
        </>
      )}
    </div>
  );

  useEffect(() => {
    // Auto-start scanning when component mounts
    startBeaconScan();
    
    return () => {
      if (scanInterval.current) {
        clearInterval(scanInterval.current);
      }
    };
  }, []);

  return (
    <div className="h-full bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_black_1px,_transparent_0)] bg-[size:40px_40px]" />
      </div>

      {/* Header */}
      <div className="relative z-10 bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="lg:hidden"
              >
                ←
              </Button>
              <div>
                <h1 className="text-2xl font-bold flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                    <Radio className="w-5 h-5 text-white" />
                  </div>
                  Bytspot Beacon Hub
                </h1>
                <p className="text-sm text-muted-foreground">
                  Location Services & Venue Integration
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Badge variant={isScanning ? "default" : "secondary"} className="flex items-center gap-1">
                {isScanning ? (
                  <RefreshCw className="w-3 h-3 animate-spin" />
                ) : (
                  <Radio className="w-3 h-3" />
                )}
                {isScanning ? 'Scanning...' : 'Ready'}
              </Badge>
              
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="beacons" className="flex items-center gap-2">
              <Radio className="w-4 h-4" />
              Beacons
            </TabsTrigger>
            <TabsTrigger value="wifi" className="flex items-center gap-2">
              <Wifi className="w-4 h-4" />
              WiFi
            </TabsTrigger>
            <TabsTrigger value="location" className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Location
            </TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="overview">
              {renderOverview()}
            </TabsContent>
            
            <TabsContent value="beacons">
              {renderBeacons()}
            </TabsContent>
            
            <TabsContent value="wifi">
              {renderWiFi()}
            </TabsContent>
            
            <TabsContent value="location">
              {renderLocation()}
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}