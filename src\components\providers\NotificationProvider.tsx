import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Bell, X, Check, Users, Trophy, Star, Gift } from 'lucide-react';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { notificationService, type NotificationData, type NotificationType } from '../../lib/services/notificationService';
import { useAnalytics } from '../../lib/hooks/useAnalytics';

interface NotificationContextType {
  notifications: NotificationData[];
  unreadCount: number;
  isEnabled: boolean;
  permissionStatus: NotificationPermission;
  requestPermission: () => Promise<NotificationPermission>;
  markAsRead: (id: string) => void;
  clearAll: () => void;
  showInAppNotification: (notification: NotificationData) => void;
  simulateActivity: () => void;
}

const NotificationContext = createContext<NotificationContextType | null>(null);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  // const { track } = useAnalytics(); // Temporarily commented out for debugging
  const track = (event: string, properties?: any) => {
    console.log('Analytics track:', event, properties);
  };

  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isEnabled, setIsEnabled] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');
  const [inAppNotifications, setInAppNotifications] = useState<NotificationData[]>([]);
  const [showPermissionBanner, setShowPermissionBanner] = useState(false);

  // Initialize notification service
  useEffect(() => {
    const initializeNotifications = async () => {
      const supported = notificationService.isSupported();
      if (!supported) return;

      await notificationService.initialize();
      
      // Get initial state
      const storedNotifications = notificationService.getStoredNotifications();
      setNotifications(storedNotifications);
      setUnreadCount(notificationService.getUnreadCount());
      setPermissionStatus(notificationService.getPermissionStatus());
      setIsEnabled(notificationService.isEnabled());

      // Show permission banner if not yet requested and user has been active
      if (notificationService.getPermissionStatus() === 'default') {
        setTimeout(() => setShowPermissionBanner(true), 10000); // Show after 10 seconds
      }
    };

    initializeNotifications();
  }, []);

  // Listen for new notifications
  useEffect(() => {
    const handleNewNotification = (event: CustomEvent<NotificationData>) => {
      const notification = event.detail;
      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
      
      // Show in-app notification
      showInAppNotification(notification);
    };

    const handleNotificationRead = () => {
      setUnreadCount(notificationService.getUnreadCount());
    };

    const handleNotificationsCleared = () => {
      setNotifications([]);
      setUnreadCount(0);
    };

    window.addEventListener('bytspot:notification', handleNewNotification as EventListener);
    window.addEventListener('bytspot:notification_read', handleNotificationRead);
    window.addEventListener('bytspot:notifications_cleared', handleNotificationsCleared);

    return () => {
      window.removeEventListener('bytspot:notification', handleNewNotification as EventListener);
      window.removeEventListener('bytspot:notification_read', handleNotificationRead);
      window.removeEventListener('bytspot:notifications_cleared', handleNotificationsCleared);
    };
  }, []);

  const requestPermission = async (): Promise<NotificationPermission> => {
    const permission = await notificationService.requestPermission();
    setPermissionStatus(permission);
    setIsEnabled(permission === 'granted');
    setShowPermissionBanner(false);
    
    if (permission === 'granted') {
      // Process any queued notifications
      await notificationService.processQueue();
    }

    track('notification_permission_response', {
      permission,
      requested_from: 'banner'
    });

    return permission;
  };

  const markAsRead = (id: string) => {
    notificationService.markAsRead(id);
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const clearAll = () => {
    notificationService.clearAllNotifications();
    setNotifications([]);
    setUnreadCount(0);
  };

  const showInAppNotification = (notification: NotificationData) => {
    // Add to in-app notification queue
    setInAppNotifications(prev => [...prev, notification]);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      setInAppNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 5000);
  };

  const removeInAppNotification = (id: string) => {
    setInAppNotifications(prev => prev.filter(n => n.id !== id));
  };

  const simulateActivity = async () => {
    await notificationService.simulateFriendActivity();
  };

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'friend_tagged':
      case 'friend_joined':
      case 'friend_vibe_score':
        return Users;
      case 'challenge_progress':
      case 'challenge_completed':
      case 'challenge_new':
        return Trophy;
      case 'achievement_unlocked':
        return Star;
      case 'referral_success':
        return Gift;
      default:
        return Bell;
    }
  };

  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case 'friend_tagged':
      case 'friend_joined':
      case 'friend_vibe_score':
        return 'from-blue-500 to-cyan-600';
      case 'challenge_progress':
      case 'challenge_completed':
      case 'challenge_new':
        return 'from-purple-500 to-pink-600';
      case 'achievement_unlocked':
        return 'from-yellow-500 to-orange-600';
      case 'referral_success':
        return 'from-green-500 to-emerald-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        isEnabled,
        permissionStatus,
        requestPermission,
        markAsRead,
        clearAll,
        showInAppNotification,
        simulateActivity
      }}
    >
      {children}

      {/* Permission Banner */}
      <AnimatePresence>
        {showPermissionBanner && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            className="fixed top-4 left-4 right-4 z-50"
          >
            <Card className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-0 shadow-2xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Bell className="w-6 h-6" />
                  <div>
                    <h4 className="font-semibold">Stay in the Loop!</h4>
                    <p className="text-sm opacity-90">
                      Get notified about friend activity, challenges, and more
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                    onClick={requestPermission}
                  >
                    Enable
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-white hover:bg-white/20"
                    onClick={() => setShowPermissionBanner(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* In-App Notifications */}
      <div className="fixed top-20 right-4 z-50 space-y-2">
        <AnimatePresence>
          {inAppNotifications.map((notification) => {
            const Icon = getNotificationIcon(notification.type);
            const colorClass = getNotificationColor(notification.type);
            
            return (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, x: 100, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 100, scale: 0.8 }}
                className="max-w-sm"
              >
                <Card className={`bg-gradient-to-r ${colorClass} text-white border-0 shadow-xl`}>
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Icon className="w-5 h-5" />
                        <h4 className="font-semibold text-sm">{notification.title}</h4>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="w-6 h-6 p-0 text-white hover:bg-white/20"
                        onClick={() => removeInAppNotification(notification.id)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                    
                    <p className="text-sm opacity-90 mb-3">{notification.body}</p>
                    
                    {notification.actions && notification.actions.length > 0 && (
                      <div className="flex gap-2">
                        {notification.actions.slice(0, 2).map((action) => (
                          <Button
                            key={action.action}
                            size="sm"
                            variant="outline"
                            className="bg-white/20 border-white/30 text-white hover:bg-white/30 text-xs"
                            onClick={() => {
                              // Handle action click
                              track('in_app_notification_action', {
                                notification_id: notification.id,
                                action: action.action,
                                type: notification.type
                              });
                              removeInAppNotification(notification.id);
                            }}
                          >
                            {action.title}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Development: Simulate Activity Button */}
      {(typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') && (
        <Button
          className="fixed bottom-20 left-4 z-50 bg-yellow-500 hover:bg-yellow-600 text-black"
          onClick={simulateActivity}
        >
          🧪 Test Notification
        </Button>
      )}
    </NotificationContext.Provider>
  );
}