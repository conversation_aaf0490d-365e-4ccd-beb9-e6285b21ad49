import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { ArrowLeft, Heart, X, Users, Clock, Star, Crown, Zap } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar } from './ui/avatar';

interface Friend {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
}

interface Match {
  id: string;
  type: 'venue';
  title: string;
  subtitle: string;
  distance: string;
  availability: string;
  rating?: number;
  image: string;
  details: {
    description: string;
    features: string[];
    footprint?: number;
    vibe?: string;
  };
}

interface CollaborativeSession {
  id: string;
  name: string;
  friends: Friend[];
  sharedMatches: Match[];
  votes: Record<string, string[]>; // venueId -> friendIds who liked it
  status: 'active' | 'decided' | 'expired';
  createdAt: string;
}

interface CollaborativeDiscoveryProps {
  session: CollaborativeSession;
  currentUserId: string;
  onVote: (venueId: string, friendId: string, vote: 'like' | 'dislike') => void;
  onBack: () => void;
  onComplete: (chosenVenueId: string) => void;
}

export function CollaborativeDiscovery({ 
  session, 
  currentUserId, 
  onVote, 
  onBack, 
  onComplete 
}: CollaborativeDiscoveryProps) {
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0);
  const [showResults, setShowResults] = useState(false);

  const currentMatch = session.sharedMatches[currentMatchIndex];
  const userVotes = session.votes[currentMatch?.id] || [];
  const hasUserVoted = userVotes.includes(currentUserId);

  const handleVote = (vote: 'like' | 'dislike') => {
    if (hasUserVoted && vote === 'dislike') {
      // Remove vote if user wants to change from like to dislike
      onVote(currentMatch.id, currentUserId, 'dislike');
    } else if (!hasUserVoted && vote === 'like') {
      onVote(currentMatch.id, currentUserId, 'like');
    }

    // Move to next match after voting
    setTimeout(() => {
      if (currentMatchIndex < session.sharedMatches.length - 1) {
        setCurrentMatchIndex(prev => prev + 1);
      } else {
        setShowResults(true);
      }
    }, 500);
  };

  const getTopVotedVenues = () => {
    return session.sharedMatches
      .map(match => ({
        match,
        votes: session.votes[match.id]?.length || 0,
        voters: session.votes[match.id] || []
      }))
      .sort((a, b) => b.votes - a.votes)
      .slice(0, 3);
  };

  const renderVotingInterface = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="relative h-full"
    >
      {/* Progress Bar */}
      <div className="absolute top-6 left-6 right-6 z-20">
        <div className="bg-white/20 rounded-full h-2 overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${((currentMatchIndex + 1) / session.sharedMatches.length) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
        <p className="text-white/70 text-sm mt-2 text-center">
          {currentMatchIndex + 1} of {session.sharedMatches.length} venues
        </p>
      </div>

      {/* Friends Voting Status */}
      <div className="absolute top-20 left-6 right-6 z-20">
        <div className="flex items-center justify-center gap-2">
          {session.friends.map(friend => {
            const hasVoted = userVotes.includes(friend.id);
            return (
              <div key={friend.id} className="flex flex-col items-center">
                <div className={`relative w-10 h-10 rounded-full overflow-hidden border-2 ${
                  hasVoted ? 'border-green-400' : 'border-white/30'
                }`}>
                  <img src={friend.avatar} alt={friend.name} className="w-full h-full object-cover" />
                  {hasVoted && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute inset-0 bg-green-400/80 flex items-center justify-center"
                    >
                      <Heart className="w-4 h-4 text-white fill-current" />
                    </motion.div>
                  )}
                </div>
                <span className="text-white text-xs mt-1">{friend.name.split(' ')[0]}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Venue Card */}
      <div className="h-full flex items-center justify-center p-6 pt-32">
        <div className="w-full max-w-sm h-[70vh] relative">
          <div className="h-full rounded-3xl overflow-hidden relative backdrop-blur-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-white/30 shadow-2xl">
            {/* Background Image */}
            <div 
              className="absolute inset-0 bg-cover bg-center opacity-40"
              style={{ backgroundImage: `url(${currentMatch.image})` }}
            />
            
            {/* Glassmorphism Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20" />
            
            {/* Content */}
            <div className="relative h-full flex flex-col">
              {/* Header */}
              <div className="p-6 flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">🎉</span>
                  <Badge variant="secondary" className="backdrop-blur-sm bg-white/20">
                    VENUE
                  </Badge>
                </div>
                
                {currentMatch.rating && (
                  <div className="flex items-center gap-1 backdrop-blur-sm bg-white/20 px-2 py-1 rounded-full">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-white text-sm font-medium">{currentMatch.rating}</span>
                  </div>
                )}
              </div>

              {/* Main Content */}
              <div className="flex-1 flex flex-col justify-end p-6">
                <div className="space-y-4">
                  <div>
                    <h1 className="text-3xl font-bold text-white mb-2">{currentMatch.title}</h1>
                    <p className="text-white/80 text-lg">{currentMatch.subtitle}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2 text-white/90">
                      <Clock className="w-4 h-4" />
                      <span className="text-sm">{currentMatch.availability}</span>
                    </div>
                    <div className="flex items-center gap-2 text-white/90">
                      <Users className="w-4 h-4" />
                      <span className="text-sm">{userVotes.length} votes</span>
                    </div>
                  </div>

                  <p className="text-white/80 text-sm">{currentMatch.details.description}</p>

                  {/* Voting Buttons */}
                  <div className="flex gap-4 pt-4">
                    <Button
                      size="lg"
                      variant="outline"
                      className="flex-1 backdrop-blur-sm bg-white/10 border-white/30 text-white hover:bg-red-500/20"
                      onClick={() => handleVote('dislike')}
                      disabled={hasUserVoted}
                    >
                      <X className="w-5 h-5 mr-2" />
                      Pass
                    </Button>
                    
                    <Button
                      size="lg"
                      className={`flex-1 border-0 ${
                        hasUserVoted 
                          ? 'bg-green-500 hover:bg-green-600' 
                          : 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700'
                      }`}
                      onClick={() => handleVote('like')}
                      disabled={hasUserVoted}
                    >
                      <Heart className={`w-5 h-5 mr-2 ${hasUserVoted ? 'fill-current' : ''}`} />
                      {hasUserVoted ? 'Voted!' : 'Love It'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );

  const renderResults = () => {
    const topVenues = getTopVotedVenues();
    const winner = topVenues[0];

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-6 space-y-6"
      >
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", bounce: 0.5 }}
            className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <Crown className="w-8 h-8 text-white" />
          </motion.div>
          <h2 className="text-2xl font-bold text-white mb-2">Group Decision Made!</h2>
          <p className="text-white/70">Here's what your group chose</p>
        </div>

        {/* Winner */}
        {winner && (
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-400/30 rounded-2xl p-4"
          >
            <div className="flex items-center gap-3 mb-3">
              <Crown className="w-6 h-6 text-yellow-400" />
              <span className="text-yellow-300 font-semibold">Group Favorite</span>
              <Badge className="bg-yellow-400 text-black">
                {winner.votes} votes
              </Badge>
            </div>
            
            <div className="flex gap-4">
              <div className="w-20 h-20 rounded-xl overflow-hidden">
                <img src={winner.match.image} alt={winner.match.title} className="w-full h-full object-cover" />
              </div>
              <div className="flex-1">
                <h3 className="text-white font-bold text-lg">{winner.match.title}</h3>
                <p className="text-white/80 text-sm mb-2">{winner.match.subtitle}</p>
                <div className="flex items-center gap-2">
                  {winner.voters.slice(0, 3).map((voterId, index) => {
                    const voter = session.friends.find(f => f.id === voterId);
                    return voter ? (
                      <div key={voterId} className="w-6 h-6 rounded-full overflow-hidden border border-white/30">
                        <img src={voter.avatar} alt={voter.name} className="w-full h-full object-cover" />
                      </div>
                    ) : null;
                  })}
                  {winner.voters.length > 3 && (
                    <span className="text-white/60 text-xs">+{winner.voters.length - 3}</span>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Other Options */}
        {topVenues.slice(1).map((venue, index) => (
          <motion.div
            key={venue.match.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 + index * 0.1 }}
            className="bg-white/10 border border-white/20 rounded-xl p-4"
          >
            <div className="flex gap-3">
              <div className="w-16 h-16 rounded-lg overflow-hidden">
                <img src={venue.match.image} alt={venue.match.title} className="w-full h-full object-cover" />
              </div>
              <div className="flex-1">
                <h4 className="text-white font-semibold">{venue.match.title}</h4>
                <p className="text-white/70 text-sm">{venue.match.subtitle}</p>
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center gap-1">
                    {venue.voters.slice(0, 2).map((voterId) => {
                      const voter = session.friends.find(f => f.id === voterId);
                      return voter ? (
                        <div key={voterId} className="w-5 h-5 rounded-full overflow-hidden border border-white/30">
                          <img src={voter.avatar} alt={voter.name} className="w-full h-full object-cover" />
                        </div>
                      ) : null;
                    })}
                  </div>
                  <span className="text-white/60 text-sm">{venue.votes} votes</span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}

        <div className="flex gap-3">
          <Button
            variant="outline"
            className="flex-1 border-white/30 text-white hover:bg-white/10"
            onClick={() => setShowResults(false)}
          >
            Vote Again
          </Button>
          <Button
            className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600"
            onClick={() => onComplete(winner?.match.id || '')}
          >
            <Zap className="w-4 h-4 mr-2" />
            Let's Go!
          </Button>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="h-full bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_white_1px,_transparent_0)] bg-[size:20px_20px]" />
      </div>

      {/* Header */}
      <div className="relative z-10 p-6 pb-0">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/10 p-2"
            onClick={onBack}
          >
            <ArrowLeft className="w-6 h-6" />
          </Button>
          <div className="text-center">
            <h1 className="text-white font-bold">{session.name}</h1>
            <p className="text-white/60 text-sm">{session.friends.length + 1} friends deciding together</p>
          </div>
          <div className="w-10" /> {/* Spacer */}
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 h-full">
        <AnimatePresence mode="wait">
          {showResults ? renderResults() : renderVotingInterface()}
        </AnimatePresence>
      </div>
    </div>
  );
}