import { test, expect } from '@playwright/test';

test.describe('Protected Routes & Role-Based Access Control', () => {
  test.describe('Unauthenticated User Access', () => {
    test.beforeEach(async ({ page }) => {
      // Ensure no authentication tokens
      await page.addInitScript(() => {
        localStorage.clear();
        sessionStorage.clear();
      });
    });

    test('should redirect to login when accessing protected app routes', async ({ page }) => {
      await page.goto('/app');
      await expect(page).toHaveURL(/.*login/);
      
      await page.goto('/app/matches');
      await expect(page).toHaveURL(/.*login/);
      
      await page.goto('/app/map');
      await expect(page).toHaveURL(/.*login/);
      
      await page.goto('/app/profile');
      await expect(page).toHaveURL(/.*login/);
    });

    test('should redirect to login when accessing business routes', async ({ page }) => {
      await page.goto('/business');
      await expect(page).toHaveURL(/.*login/);
      
      await page.goto('/business/dashboard');
      await expect(page).toHaveURL(/.*login/);
      
      await page.goto('/business/analytics');
      await expect(page).toHaveURL(/.*login/);
    });

    test('should allow access to public routes', async ({ page }) => {
      await page.goto('/login');
      await expect(page).toHaveURL(/.*login/);
      
      await page.goto('/register');
      await expect(page).toHaveURL(/.*register/);
      
      await page.goto('/forgot-password');
      await expect(page).toHaveURL(/.*forgot-password/);
    });

    test('should preserve intended destination after login', async ({ page }) => {
      // Try to access protected route
      await page.goto('/app/profile');
      await expect(page).toHaveURL(/.*login/);
      
      // Mock successful login
      await page.route('**/auth/login', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            user: {
              id: 'test-user',
              email: '<EMAIL>',
              name: 'Test User',
              role: 'consumer',
              permissions: [],
              preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            tokens: {
              accessToken: 'mock-access-token',
              refreshToken: 'mock-refresh-token',
              expiresAt: Date.now() + 3600000,
            },
          }),
        });
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'consumer',
            permissions: [],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });
      
      // Login
      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('password123');
      await page.getByRole('button', { name: /sign in/i }).click();
      
      // Should redirect to intended destination
      await expect(page).toHaveURL(/.*app\/profile/);
    });
  });

  test.describe('Consumer User Access', () => {
    test.beforeEach(async ({ page }) => {
      // Mock consumer authentication
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-consumer-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'consumer-user',
            email: '<EMAIL>',
            name: 'Consumer User',
            role: 'consumer',
            permissions: [],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });
    });

    test('should access consumer app routes', async ({ page }) => {
      await page.goto('/app');
      await expect(page).toHaveURL(/.*app\/matches/);
      
      await page.goto('/app/matches');
      await expect(page).toHaveURL(/.*app\/matches/);
      
      await page.goto('/app/map');
      await expect(page).toHaveURL(/.*app\/map/);
      
      await page.goto('/app/profile');
      await expect(page).toHaveURL(/.*app\/profile/);
    });

    test('should be denied access to business routes', async ({ page }) => {
      await page.goto('/business');
      await expect(page).toHaveURL(/.*unauthorized/);
      
      await page.goto('/business/dashboard');
      await expect(page).toHaveURL(/.*unauthorized/);
      
      await page.goto('/business/analytics');
      await expect(page).toHaveURL(/.*unauthorized/);
    });

    test('should redirect authenticated user from login page', async ({ page }) => {
      await page.goto('/login');
      await expect(page).toHaveURL(/.*app/);
      
      await page.goto('/register');
      await expect(page).toHaveURL(/.*app/);
    });
  });

  test.describe('Host User Access', () => {
    test.beforeEach(async ({ page }) => {
      // Mock host authentication
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-host-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'host-user',
            email: '<EMAIL>',
            name: 'Host User',
            role: 'host',
            permissions: [
              { resource: 'venue', actions: ['read', 'write', 'manage'] },
              { resource: 'analytics', actions: ['read'] },
              { resource: 'reservations', actions: ['read', 'write'] },
            ],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });
    });

    test('should access both consumer and business routes', async ({ page }) => {
      // Consumer routes
      await page.goto('/app');
      await expect(page).toHaveURL(/.*app\/matches/);
      
      await page.goto('/app/profile');
      await expect(page).toHaveURL(/.*app\/profile/);
      
      // Business routes
      await page.goto('/business');
      await expect(page).toHaveURL(/.*business\/dashboard/);
      
      await page.goto('/business/dashboard');
      await expect(page).toHaveURL(/.*business\/dashboard/);
      
      await page.goto('/business/analytics');
      await expect(page).toHaveURL(/.*business\/analytics/);
    });

    test('should show business toggle in consumer mode', async ({ page }) => {
      await page.goto('/app');
      
      // Should show business mode toggle
      await expect(page.getByRole('button', { name: /business/i })).toBeVisible();
    });
  });

  test.describe('Admin User Access', () => {
    test.beforeEach(async ({ page }) => {
      // Mock admin authentication
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-admin-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'admin-user',
            email: '<EMAIL>',
            name: 'Admin User',
            role: 'admin',
            permissions: [
              { resource: '*', actions: ['*'] },
            ],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });
    });

    test('should access all routes', async ({ page }) => {
      // Consumer routes
      await page.goto('/app');
      await expect(page).toHaveURL(/.*app\/matches/);
      
      // Business routes
      await page.goto('/business');
      await expect(page).toHaveURL(/.*business\/dashboard/);
      
      // Admin-specific functionality would be tested here
      // when admin routes are implemented
    });
  });

  test.describe('Session Management', () => {
    test('should handle expired tokens', async ({ page }) => {
      // Mock expired token
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'expired-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() - 3600000, // Expired 1 hour ago
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Token expired' }),
        });
      });

      await page.goto('/app');
      
      // Should redirect to login
      await expect(page).toHaveURL(/.*login/);
    });

    test('should handle logout and clear session', async ({ page }) => {
      // Mock authenticated user
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'consumer',
            permissions: [],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      await page.route('**/auth/logout', async route => {
        await route.fulfill({ status: 200, body: 'OK' });
      });

      await page.goto('/app/profile');
      
      // Logout
      await page.getByRole('button', { name: /logout/i }).click();
      
      // Should redirect to login and clear tokens
      await expect(page).toHaveURL(/.*login/);
      
      // Try to access protected route again
      await page.goto('/app');
      await expect(page).toHaveURL(/.*login/);
    });
  });

  test.describe('Error Handling', () => {
    test('should handle auth service errors gracefully', async ({ page }) => {
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      // Mock auth service error
      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Internal server error' }),
        });
      });

      await page.goto('/app');
      
      // Should handle error and redirect to login
      await expect(page).toHaveURL(/.*login/);
    });

    test('should handle network errors during authentication', async ({ page, context }) => {
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      // Simulate network failure
      await context.setOffline(true);
      
      await page.goto('/app');
      
      // Should show loading state or error
      await expect(page.getByText(/loading/i)).toBeVisible();
      
      // Re-enable network
      await context.setOffline(false);
      
      // Mock successful auth check
      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'consumer',
            permissions: [],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });
      
      // Retry should work
      await page.reload();
      await expect(page).toHaveURL(/.*app\/matches/);
    });
  });

  test.describe('Accessibility', () => {
    test('should provide accessible error messages for unauthorized access', async ({ page }) => {
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-consumer-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'consumer-user',
            email: '<EMAIL>',
            name: 'Consumer User',
            role: 'consumer',
            permissions: [],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      await page.goto('/business');
      await expect(page).toHaveURL(/.*unauthorized/);
      
      // Check for accessible error content
      await expect(page.getByRole('heading', { name: /403/i })).toBeVisible();
      await expect(page.getByText(/permission/i)).toBeVisible();
      await expect(page.getByRole('button', { name: /go to app/i })).toBeVisible();
      
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      await expect(page.getByRole('button', { name: /go to app/i })).toBeFocused();
    });

    test('should provide accessible loading states', async ({ page }) => {
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'mock-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      // Mock slow auth response
      await page.route('**/auth/me', async route => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'consumer',
            permissions: [],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      await page.goto('/app');
      
      // Should show accessible loading state
      await expect(page.getByText(/loading bytspot/i)).toBeVisible();
      await expect(page.getByText(/preparing your personalized experience/i)).toBeVisible();
    });
  });
});