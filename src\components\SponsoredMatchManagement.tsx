import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import {
  Plus,
  Play,
  Pause,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  TrendingUp,
  Target,
  DollarSign,
  Users,
  Clock,
  MapPin,
  Calendar,
  Settings,
  BarChart3,
  Zap,
  Star,
  ThumbsUp,
  Heart,
  Share2,
  ArrowUp,
  ArrowDown,
  Filter,
  Search,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Switch } from './ui/switch';
import { Slider } from './ui/slider';
import { Progress } from './ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Separator } from './ui/separator';

interface SponsoredCampaign {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'draft' | 'ended';
  type: 'premium_placement' | 'featured_match' | 'priority_boost' | 'targeted_promotion';
  budget: {
    daily: number;
    total: number;
    spent: number;
    currency: string;
  };
  targeting: {
    demographics: {
      ageRange: [number, number];
      interests: string[];
      spendingTier: string[];
    };
    geographic: {
      radius: number;
      areas: string[];
    };
    behavioral: {
      visitFrequency: string;
      timePreferences: string[];
      groupSize: string[];
    };
  };
  schedule: {
    startDate: string;
    endDate: string;
    timeSlots: {
      days: string[];
      hours: [number, number];
    };
  };
  creative: {
    title: string;
    subtitle: string;
    description: string;
    images: string[];
    callToAction: string;
    specialOffer?: string;
  };
  performance: {
    impressions: number;
    clicks: number;
    conversions: number;
    revenue: number;
    ctr: number;
    cpm: number;
    roas: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface SponsoredMatchManagementProps {
  venueName: string;
}

export function SponsoredMatchManagement({ venueName }: SponsoredMatchManagementProps) {
  const [activeTab, setActiveTab] = useState('campaigns');
  const [campaigns, setCampaigns] = useState<SponsoredCampaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<SponsoredCampaign | null>(null);
  const [isCreatingCampaign, setIsCreatingCampaign] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Mock campaign data
  const mockCampaigns: SponsoredCampaign[] = [
    {
      id: 'camp-001',
      name: 'Weekend Party Promotion',
      status: 'active',
      type: 'featured_match',
      budget: {
        daily: 150,
        total: 3000,
        spent: 1250,
        currency: 'USD'
      },
      targeting: {
        demographics: {
          ageRange: [21, 35],
          interests: ['nightlife', 'electronic music', 'cocktails'],
          spendingTier: ['premium', 'luxury']
        },
        geographic: {
          radius: 5,
          areas: ['Downtown', 'Financial District', 'SoHo']
        },
        behavioral: {
          visitFrequency: 'frequent',
          timePreferences: ['evening', 'weekend'],
          groupSize: ['2-4', '5-8']
        }
      },
      schedule: {
        startDate: '2024-01-15',
        endDate: '2024-02-15',
        timeSlots: {
          days: ['Friday', 'Saturday', 'Sunday'],
          hours: [18, 2]
        }
      },
      creative: {
        title: 'Rooftop Lounge - Weekend Vibes',
        subtitle: '🔥 The Hottest Weekend Destination',
        description: 'Experience the ultimate weekend party with stunning city views, world-class DJs, and premium cocktails.',
        images: ['https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800'],
        callToAction: 'Book VIP Table',
        specialOffer: '20% off VIP tables this weekend'
      },
      performance: {
        impressions: 45230,
        clicks: 2847,
        conversions: 156,
        revenue: 18750,
        ctr: 6.3,
        cpm: 27.63,
        roas: 15
      },
      createdAt: '2024-01-10',
      updatedAt: '2024-01-20'
    },
    {
      id: 'camp-002',
      name: 'Happy Hour Drive',
      status: 'paused',
      type: 'priority_boost',
      budget: {
        daily: 75,
        total: 1500,
        spent: 580,
        currency: 'USD'
      },
      targeting: {
        demographics: {
          ageRange: [25, 45],
          interests: ['after-work drinks', 'networking', 'business'],
          spendingTier: ['mid-tier', 'premium']
        },
        geographic: {
          radius: 3,
          areas: ['Financial District', 'Midtown']
        },
        behavioral: {
          visitFrequency: 'regular',
          timePreferences: ['weekday_evening'],
          groupSize: ['2-4', 'solo']
        }
      },
      schedule: {
        startDate: '2024-01-08',
        endDate: '2024-01-31',
        timeSlots: {
          days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
          hours: [17, 20]
        }
      },
      creative: {
        title: 'Executive Happy Hour',
        subtitle: '🍸 Premium After-Work Experience',
        description: 'Unwind with colleagues and clients at our sophisticated rooftop setting.',
        images: ['https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800'],
        callToAction: 'Reserve Table',
        specialOffer: 'Buy 2 cocktails, get 1 free appetizer'
      },
      performance: {
        impressions: 28560,
        clicks: 1240,
        conversions: 89,
        revenue: 6700,
        ctr: 4.3,
        cpm: 20.31,
        roas: 11.6
      },
      createdAt: '2024-01-05',
      updatedAt: '2024-01-18'
    },
    {
      id: 'camp-003',
      name: 'Date Night Special',
      status: 'draft',
      type: 'targeted_promotion',
      budget: {
        daily: 100,
        total: 2000,
        spent: 0,
        currency: 'USD'
      },
      targeting: {
        demographics: {
          ageRange: [24, 40],
          interests: ['romantic dining', 'date nights', 'wine'],
          spendingTier: ['premium']
        },
        geographic: {
          radius: 8,
          areas: ['All Areas']
        },
        behavioral: {
          visitFrequency: 'occasional',
          timePreferences: ['evening', 'weekend'],
          groupSize: ['couples']
        }
      },
      schedule: {
        startDate: '2024-02-01',
        endDate: '2024-02-29',
        timeSlots: {
          days: ['Friday', 'Saturday'],
          hours: [19, 23]
        }
      },
      creative: {
        title: 'Romantic Evening Under Stars',
        subtitle: '💕 Perfect Date Night Destination',
        description: 'Create unforgettable memories with your special someone in our intimate rooftop setting.',
        images: ['https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800'],
        callToAction: 'Book Date Night',
        specialOffer: 'Complimentary champagne for couples'
      },
      performance: {
        impressions: 0,
        clicks: 0,
        conversions: 0,
        revenue: 0,
        ctr: 0,
        cpm: 0,
        roas: 0
      },
      createdAt: '2024-01-22',
      updatedAt: '2024-01-22'
    }
  ];

  const [newCampaign, setNewCampaign] = useState<Partial<SponsoredCampaign>>({
    name: '',
    type: 'featured_match',
    budget: {
      daily: 100,
      total: 2000,
      spent: 0,
      currency: 'USD'
    },
    targeting: {
      demographics: {
        ageRange: [21, 45],
        interests: [],
        spendingTier: ['mid-tier']
      },
      geographic: {
        radius: 5,
        areas: []
      },
      behavioral: {
        visitFrequency: 'any',
        timePreferences: [],
        groupSize: []
      }
    },
    creative: {
      title: '',
      subtitle: '',
      description: '',
      images: [],
      callToAction: 'Book Now'
    }
  });

  useEffect(() => {
    setCampaigns(mockCampaigns);
  }, []);

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         campaign.creative.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || campaign.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: SponsoredCampaign['status']) => {
    switch (status) {
      case 'active': return 'text-green-500';
      case 'paused': return 'text-yellow-500';
      case 'draft': return 'text-gray-500';
      case 'ended': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: SponsoredCampaign['status']) => {
    switch (status) {
      case 'active': return <Play className="w-4 h-4 text-green-500" />;
      case 'paused': return <Pause className="w-4 h-4 text-yellow-500" />;
      case 'draft': return <Edit className="w-4 h-4 text-gray-500" />;
      case 'ended': return <XCircle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTypeLabel = (type: SponsoredCampaign['type']) => {
    switch (type) {
      case 'premium_placement': return 'Premium Placement';
      case 'featured_match': return 'Featured Match';
      case 'priority_boost': return 'Priority Boost';
      case 'targeted_promotion': return 'Targeted Promotion';
      default: return type;
    }
  };

  const handleToggleCampaignStatus = (campaignId: string) => {
    setCampaigns(prev => prev.map(campaign => {
      if (campaign.id === campaignId) {
        const newStatus = campaign.status === 'active' ? 'paused' : 'active';
        return { ...campaign, status: newStatus };
      }
      return campaign;
    }));
  };

  const handleCreateCampaign = () => {
    if (newCampaign.name && newCampaign.creative?.title) {
      const campaign: SponsoredCampaign = {
        id: `camp-${Date.now()}`,
        name: newCampaign.name,
        status: 'draft',
        type: newCampaign.type || 'featured_match',
        budget: newCampaign.budget || {
          daily: 100,
          total: 2000,
          spent: 0,
          currency: 'USD'
        },
        targeting: newCampaign.targeting || {
          demographics: {
            ageRange: [21, 45],
            interests: [],
            spendingTier: ['mid-tier']
          },
          geographic: {
            radius: 5,
            areas: []
          },
          behavioral: {
            visitFrequency: 'any',
            timePreferences: [],
            groupSize: []
          }
        },
        schedule: newCampaign.schedule || {
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          timeSlots: {
            days: ['Friday', 'Saturday'],
            hours: [18, 2]
          }
        },
        creative: newCampaign.creative || {
          title: '',
          subtitle: '',
          description: '',
          images: [],
          callToAction: 'Book Now'
        },
        performance: {
          impressions: 0,
          clicks: 0,
          conversions: 0,
          revenue: 0,
          ctr: 0,
          cpm: 0,
          roas: 0
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      setCampaigns(prev => [...prev, campaign]);
      setIsCreatingCampaign(false);
      setNewCampaign({
        name: '',
        type: 'featured_match',
        budget: {
          daily: 100,
          total: 2000,
          spent: 0,
          currency: 'USD'
        },
        creative: {
          title: '',
          subtitle: '',
          description: '',
          images: [],
          callToAction: 'Book Now'
        }
      });
    }
  };

  const renderOverview = () => {
    const totalSpent = campaigns.reduce((sum, c) => sum + c.budget.spent, 0);
    const totalImpressions = campaigns.reduce((sum, c) => sum + c.performance.impressions, 0);
    const totalClicks = campaigns.reduce((sum, c) => sum + c.performance.clicks, 0);
    const totalRevenue = campaigns.reduce((sum, c) => sum + c.performance.revenue, 0);
    const avgCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
    const avgROAS = totalSpent > 0 ? totalRevenue / totalSpent : 0;

    return (
      <div className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">${totalSpent.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">Total Spent</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{totalImpressions.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">Impressions</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                <Target className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{avgCTR.toFixed(1)}%</p>
                <p className="text-sm text-muted-foreground">Avg CTR</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{avgROAS.toFixed(1)}x</p>
                <p className="text-sm text-muted-foreground">Avg ROAS</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Campaign Performance Chart */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Campaign Performance Overview</h3>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Performance chart visualization would go here</p>
            </div>
          </div>
        </Card>

        {/* Active Campaigns Summary */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Active Campaigns</h3>
            <Badge variant="outline">
              {campaigns.filter(c => c.status === 'active').length} Active
            </Badge>
          </div>
          
          <div className="space-y-3">
            {campaigns.filter(c => c.status === 'active').slice(0, 3).map((campaign) => (
              <div key={campaign.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center">
                    <Play className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">{campaign.name}</p>
                    <p className="text-sm text-muted-foreground">{getTypeLabel(campaign.type)}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className="font-medium">${campaign.budget.spent} / ${campaign.budget.total}</p>
                  <p className="text-sm text-muted-foreground">
                    {campaign.performance.ctr.toFixed(1)}% CTR
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    );
  };

  const renderCampaigns = () => (
    <div className="space-y-6">
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search campaigns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="paused">Paused</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="ended">Ended</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <Dialog open={isCreatingCampaign} onOpenChange={setIsCreatingCampaign}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Create Campaign
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Campaign</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-6">
              <div>
                <Label htmlFor="campaign-name">Campaign Name</Label>
                <Input
                  id="campaign-name"
                  value={newCampaign.name || ''}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Weekend Party Promotion"
                />
              </div>

              <div>
                <Label htmlFor="campaign-type">Campaign Type</Label>
                <Select
                  value={newCampaign.type}
                  onValueChange={(value) => setNewCampaign(prev => ({ ...prev, type: value as SponsoredCampaign['type'] }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="featured_match">Featured Match</SelectItem>
                    <SelectItem value="premium_placement">Premium Placement</SelectItem>
                    <SelectItem value="priority_boost">Priority Boost</SelectItem>
                    <SelectItem value="targeted_promotion">Targeted Promotion</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="daily-budget">Daily Budget ($)</Label>
                  <Input
                    id="daily-budget"
                    type="number"
                    value={newCampaign.budget?.daily || 100}
                    onChange={(e) => setNewCampaign(prev => ({
                      ...prev,
                      budget: { ...prev.budget!, daily: parseFloat(e.target.value) }
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="total-budget">Total Budget ($)</Label>
                  <Input
                    id="total-budget"
                    type="number"
                    value={newCampaign.budget?.total || 2000}
                    onChange={(e) => setNewCampaign(prev => ({
                      ...prev,
                      budget: { ...prev.budget!, total: parseFloat(e.target.value) }
                    }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="campaign-title">Ad Title</Label>
                <Input
                  id="campaign-title"
                  value={newCampaign.creative?.title || ''}
                  onChange={(e) => setNewCampaign(prev => ({
                    ...prev,
                    creative: { ...prev.creative!, title: e.target.value }
                  }))}
                  placeholder="e.g., Rooftop Lounge - Weekend Vibes"
                />
              </div>

              <div>
                <Label htmlFor="campaign-subtitle">Ad Subtitle</Label>
                <Input
                  id="campaign-subtitle"
                  value={newCampaign.creative?.subtitle || ''}
                  onChange={(e) => setNewCampaign(prev => ({
                    ...prev,
                    creative: { ...prev.creative!, subtitle: e.target.value }
                  }))}
                  placeholder="e.g., 🔥 The Hottest Weekend Destination"
                />
              </div>

              <div>
                <Label htmlFor="campaign-description">Description</Label>
                <Textarea
                  id="campaign-description"
                  value={newCampaign.creative?.description || ''}
                  onChange={(e) => setNewCampaign(prev => ({
                    ...prev,
                    creative: { ...prev.creative!, description: e.target.value }
                  }))}
                  placeholder="Describe your venue and what makes it special..."
                  rows={3}
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={handleCreateCampaign} className="flex-1">
                  Create Campaign
                </Button>
                <Button variant="outline" onClick={() => setIsCreatingCampaign(false)} className="flex-1">
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Campaign List */}
      <div className="grid gap-4">
        {filteredCampaigns.map((campaign) => (
          <Card key={campaign.id} className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  {getStatusIcon(campaign.status)}
                </div>
                
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold">{campaign.name}</h4>
                    <Badge variant="outline">{getTypeLabel(campaign.type)}</Badge>
                    <Badge variant={campaign.status === 'active' ? 'default' : 'secondary'}>
                      {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{campaign.creative.title}</p>
                  <p className="text-xs text-muted-foreground">
                    Created {new Date(campaign.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleToggleCampaignStatus(campaign.id)}
                  disabled={campaign.status === 'draft' || campaign.status === 'ended'}
                >
                  {campaign.status === 'active' ? (
                    <Pause className="w-4 h-4" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                </Button>
                <Button variant="ghost" size="sm">
                  <Edit className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div>
                <p className="text-sm text-muted-foreground">Budget</p>
                <p className="font-medium">${campaign.budget.spent} / ${campaign.budget.total}</p>
                <Progress 
                  value={(campaign.budget.spent / campaign.budget.total) * 100} 
                  className="mt-1 h-2"
                />
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground">Impressions</p>
                <p className="font-medium">{campaign.performance.impressions.toLocaleString()}</p>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground">CTR</p>
                <p className="font-medium">{campaign.performance.ctr.toFixed(1)}%</p>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground">ROAS</p>
                <p className="font-medium">{campaign.performance.roas.toFixed(1)}x</p>
              </div>
            </div>

            {campaign.creative.specialOffer && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <p className="text-sm font-medium text-yellow-800">Special Offer</p>
                </div>
                <p className="text-sm text-yellow-700 mt-1">{campaign.creative.specialOffer}</p>
              </div>
            )}
          </Card>
        ))}
      </div>
    </div>
  );

  const renderAnalytics = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Performance Analytics</h3>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Detailed Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="font-semibold mb-4">Click-Through Rate Trends</h4>
          <div className="h-48 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">CTR trend chart</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-semibold mb-4">Return on Ad Spend</h4>
          <div className="h-48 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <DollarSign className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">ROAS performance chart</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-semibold mb-4">Audience Demographics</h4>
          <div className="h-48 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Users className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Demographics breakdown</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-semibold mb-4">Time-based Performance</h4>
          <div className="h-48 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Hourly performance data</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Top Performing Campaigns */}
      <Card className="p-6">
        <h4 className="font-semibold mb-4">Top Performing Campaigns</h4>
        <div className="space-y-3">
          {campaigns
            .sort((a, b) => b.performance.roas - a.performance.roas)
            .slice(0, 5)
            .map((campaign, index) => (
              <div key={campaign.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{campaign.name}</p>
                    <p className="text-sm text-muted-foreground">{getTypeLabel(campaign.type)}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className="font-medium">{campaign.performance.roas.toFixed(1)}x ROAS</p>
                  <p className="text-sm text-muted-foreground">
                    {campaign.performance.ctr.toFixed(1)}% CTR
                  </p>
                </div>
              </div>
            ))}
        </div>
      </Card>
    </div>
  );

  return (
    <div className="h-full bg-gray-50 overflow-hidden">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Sponsored Match Management</h1>
            <p className="text-sm text-muted-foreground">
              Create and manage sponsored listings for {venueName}
            </p>
          </div>
          
          <Badge variant="outline" className="flex items-center gap-1">
            <TrendingUp className="w-3 h-3" />
            ${campaigns.reduce((sum, c) => sum + c.performance.revenue, 0).toLocaleString()} Revenue
          </Badge>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6 h-full overflow-y-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="overview">
              {renderOverview()}
            </TabsContent>
            
            <TabsContent value="campaigns">
              {renderCampaigns()}
            </TabsContent>
            
            <TabsContent value="analytics">
              {renderAnalytics()}
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}