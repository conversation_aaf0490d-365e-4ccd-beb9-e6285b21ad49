import { analyticsService } from '../monitoring/analyticsService';
import { errorService } from '../monitoring/errorService';
import { mobileDetectionService } from './mobileDetectionService';

export interface ShareData {
  title: string;
  text: string;
  url?: string;
  hashtags?: string[];
  venue?: string;
  vibeScore?: number;
  image?: string;
}

export interface SocialPlatform {
  id: string;
  name: string;
  requiresAuth: boolean;
  supportsNativeShare: boolean;
}

class SocialMediaService {
  private readonly platforms: Record<string, SocialPlatform> = {
    instagram_story: {
      id: 'instagram_story',
      name: 'Instagram Story',
      requiresAuth: true,
      supportsNativeShare: false
    },
    instagram_post: {
      id: 'instagram_post',
      name: 'Instagram Post',
      requiresAuth: true,
      supportsNativeShare: false
    },
    twitter: {
      id: 'twitter',
      name: 'Twitter/X',
      requiresAuth: false,
      supportsNativeShare: true
    },
    tiktok: {
      id: 'tiktok',
      name: 'TikT<PERSON>',
      requiresAuth: false,
      supportsNativeShare: true
    },
    native: {
      id: 'native',
      name: 'Share',
      requiresAuth: false,
      supportsNativeShare: true
    }
  };

  private accessTokens: Record<string, string> = {};

  // Check if Web Share API is available
  private isWebShareSupported(): boolean {
    return 'share' in navigator && 'canShare' in navigator;
  }

  // Check if the device is mobile
  private isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  // Generate sharing URLs for different platforms
  private generateShareUrl(platform: string, data: ShareData): string {
    const encodedText = encodeURIComponent(data.text);
    const encodedUrl = data.url ? encodeURIComponent(data.url) : '';
    
    switch (platform) {
      case 'twitter':
        const hashtags = data.hashtags ? data.hashtags.join(',') : 'Bytspot';
        return `https://twitter.com/intent/tweet?text=${encodedText}&hashtags=${hashtags}&url=${encodedUrl}`;
      
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedText}`;
      
      case 'linkedin':
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}&summary=${encodedText}`;
      
      case 'whatsapp':
        return `https://wa.me/?text=${encodedText}%20${encodedUrl}`;
      
      case 'telegram':
        return `https://t.me/share/url?url=${encodedUrl}&text=${encodedText}`;
      
      default:
        return `mailto:?subject=${encodeURIComponent(data.title)}&body=${encodedText}%20${encodedUrl}`;
    }
  }

  // Create shareable content with Bytspot branding
  private createShareContent(platform: string, data: ShareData): ShareData {
    const baseUrl = window.location.origin;
    const shareUrl = data.url || `${baseUrl}/app/venue/shared`;

    switch (platform) {
      case 'instagram_story':
        return {
          ...data,
          text: `🔥 ${data.vibeScore}/10 vibe at ${data.venue}! The energy is UNREAL! 📍 Found this gem on @bytspot ✨`,
          hashtags: ['Bytspot', 'VibeScore', 'NightlifeFinds', data.venue?.replace(/\s+/g, '') || ''],
          url: shareUrl
        };

      case 'instagram_post':
        return {
          ...data,
          text: `Perfect ${data.vibeScore}/10 vibe at ${data.venue}! 🎉\n\nFound this incredible spot through @bytspot and the atmosphere is absolutely electric! You need to check this place out! ⚡\n\n#Bytspot #PerfectVibe #${data.venue?.replace(/\s+/g, '') || 'NightlifeFinds'} #VibeScore`,
          url: shareUrl
        };

      case 'twitter':
        return {
          ...data,
          text: `🔥 Just hit a ${data.vibeScore}/10 vibe score at ${data.venue}! The energy is UNREAL! Thanks @bytspot for finding me the perfect spot! ⚡`,
          hashtags: ['VibeScore', 'Bytspot', 'NightlifeFinds'],
          url: shareUrl
        };

      case 'tiktok':
        return {
          ...data,
          text: `POV: You found the perfect spot with @bytspot and the vibe is a solid ${data.vibeScore}/10! ✨ #Bytspot #PerfectVibe #SpotFinder #VibeCheck`,
          url: shareUrl
        };

      default:
        return {
          ...data,
          text: `Check out this amazing ${data.vibeScore}/10 vibe spot I found on Bytspot: ${data.venue}! ${shareUrl}`,
          url: shareUrl
        };
    }
  }

  // Handle Instagram sharing (requires deep linking to Instagram app)
  private async shareToInstagram(platform: string, data: ShareData): Promise<boolean> {
    try {
      const content = this.createShareContent(platform, data);
      const deviceInfo = mobileDetectionService.getDeviceInfo();
      
      // Use mobile detection service for better platform detection
      if (deviceInfo.isMobile && !deviceInfo.isInAppBrowser) {
        // Try Instagram deep link with better detection
        const deepLink = mobileDetectionService.generateDeepLink('instagram', content);
        
        if (deepLink) {
          // Create a temporary link to trigger Instagram
          const link = document.createElement('a');
          link.href = deepLink;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          // Add haptic feedback if available
          mobileDetectionService.vibrate([100, 50, 100]);
          
          // Fallback: Copy content to clipboard
          if (deviceInfo.supportsClipboard) {
            await navigator.clipboard.writeText(content.text);
            this.showCopySuccessMessage('Instagram content copied to clipboard! Opening Instagram...');
          }
          return true;
        }
      }
      
      // Enhanced fallback: Copy content and show instructions
      if (deviceInfo.supportsClipboard) {
        await navigator.clipboard.writeText(content.text);
        this.showInstagramInstructions(platform, content);
      } else {
        this.showShareModal('Instagram', 'Manual copy required', content.text);
      }
      return true;
    } catch (error) {
      try {
        if (errorService && typeof errorService.captureException === 'function') {
          errorService.captureException(error as Error, {
            context: 'social_share_instagram',
            platform,
            device_info: mobileDetectionService.getDeviceInfo()
          });
        }
      } catch (errorServiceError) {
        console.warn('Error service failed:', errorServiceError);
      }
      return false;
    }
  }

  // Handle TikTok sharing
  private async shareToTikTok(data: ShareData): Promise<boolean> {
    try {
      const content = this.createShareContent('tiktok', data);
      const deviceInfo = mobileDetectionService.getDeviceInfo();
      
      if (deviceInfo.isMobile && !deviceInfo.isInAppBrowser) {
        // Try TikTok deep link with better detection
        const deepLink = mobileDetectionService.generateDeepLink('tiktok', content);
        
        if (deepLink) {
          const link = document.createElement('a');
          link.href = deepLink;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          // Add haptic feedback
          mobileDetectionService.vibrate([100, 50, 100]);
        }
      }
      
      // Fallback: Copy content and show instructions
      if (deviceInfo.supportsClipboard) {
        await navigator.clipboard.writeText(content.text);
        this.showTikTokInstructions(content);
      } else {
        this.showShareModal('TikTok', 'Manual copy required', content.text);
      }
      return true;
    } catch (error) {
      try {
        if (errorService && typeof errorService.captureException === 'function') {
          errorService.captureException(error as Error, {
            context: 'social_share_tiktok',
            device_info: mobileDetectionService.getDeviceInfo()
          });
        }
      } catch (errorServiceError) {
        console.warn('Error service failed:', errorServiceError);
      }
      return false;
    }
  }

  // Show Instagram sharing instructions
  private showInstagramInstructions(platform: string, content: ShareData): void {
    const isStory = platform === 'instagram_story';
    const message = `📱 ${isStory ? 'Instagram Story' : 'Instagram Post'} content ready!\n\n` +
      `✅ Content copied to clipboard\n` +
      `📲 Open Instagram app\n` +
      `${isStory ? '📖 Tap "Your Story"' : '➕ Tap "+" to create post'}\n` +
      `📝 Paste your content\n` +
      `🚀 Share your vibe!`;
    
    this.showShareModal('Instagram', message, content.text);
  }

  // Show TikTok sharing instructions  
  private showTikTokInstructions(content: ShareData): void {
    const message = `🎵 TikTok content ready!\n\n` +
      `✅ Content copied to clipboard\n` +
      `📲 Open TikTok app\n` +
      `➕ Tap "+" to create video\n` +
      `🎬 Record your vibe moment\n` +
      `📝 Paste caption and share!`;
    
    this.showShareModal('TikTok', message, content.text);
  }

  // Show copy success message
  private showCopySuccessMessage(message: string): void {
    // Create a toast notification
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-pulse';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 3000);
  }

  // Show sharing modal with instructions
  private showShareModal(platform: string, instructions: string, content: string): void {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4';
    modal.innerHTML = `
      <div class="bg-white rounded-2xl p-6 max-w-sm w-full">
        <div class="text-center space-y-4">
          <h3 class="text-xl font-bold">Share to ${platform}</h3>
          <p class="text-gray-600 whitespace-pre-line">${instructions}</p>
          <div class="bg-gray-100 rounded-lg p-3 text-sm text-left">
            <p class="font-medium mb-2">Your content:</p>
            <p class="text-gray-700">${content}</p>
          </div>
          <button onclick="this.closest('.fixed').remove()" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-medium">
            Got it!
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (document.body.contains(modal)) {
        document.body.removeChild(modal);
      }
    }, 10000);
  }

  // Main share method
  async share(platform: string, data: ShareData): Promise<boolean> {
    try {
      // Safe analytics tracking
      try {
        if (analyticsService && typeof analyticsService.track === 'function') {
          analyticsService.track('social_share_initiated', {
            platform,
            venue: data.venue,
            vibe_score: data.vibeScore,
            has_image: !!data.image
          });
        }
      } catch (error) {
        console.warn('Analytics tracking failed for social share initiated:', error);
      }

      // Handle special platforms
      if (platform.startsWith('instagram')) {
        return await this.shareToInstagram(platform, data);
      }

      if (platform === 'tiktok') {
        return await this.shareToTikTok(data);
      }

      // Handle native sharing
      if (platform === 'native' && this.isWebShareSupported()) {
        const content = this.createShareContent(platform, data);
        const shareData = {
          title: content.title,
          text: content.text,
          url: content.url
        };

        if (navigator.canShare(shareData)) {
          await navigator.share(shareData);
          
          // Safe analytics tracking
          try {
            if (analyticsService && typeof analyticsService.track === 'function') {
              analyticsService.track('social_share_completed', {
                platform: 'native',
                venue: data.venue,
                vibe_score: data.vibeScore
              });
            }
          } catch (error) {
            console.warn('Analytics tracking failed for social share completed:', error);
          }
          
          return true;
        }
      }

      // Handle URL-based sharing (Twitter, etc.)
      const content = this.createShareContent(platform, data);
      const shareUrl = this.generateShareUrl(platform, content);
      
      // Open sharing URL in new window
      const popup = window.open(
        shareUrl,
        'share',
        'width=600,height=400,scrollbars=yes,resizable=yes'
      );

      if (popup) {
        // Check if popup was closed (indicating successful share)
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            
            // Safe analytics tracking
            try {
              if (analyticsService && typeof analyticsService.track === 'function') {
                analyticsService.track('social_share_completed', {
                  platform,
                  venue: data.venue,
                  vibe_score: data.vibeScore
                });
              }
            } catch (error) {
              console.warn('Analytics tracking failed for social share completed:', error);
            }
          }
        }, 1000);

        // Clear interval after 30 seconds
        setTimeout(() => clearInterval(checkClosed), 30000);
        
        return true;
      }

      return false;
    } catch (error) {
      try {
        if (errorService && typeof errorService.captureException === 'function') {
          errorService.captureException(error as Error, {
            context: 'social_share',
            platform,
            venue: data.venue
          });
        }
      } catch (errorServiceError) {
        console.warn('Error service failed:', errorServiceError);
      }
      
      // Fallback: Copy to clipboard
      try {
        const content = this.createShareContent(platform, data);
        await navigator.clipboard.writeText(content.text);
        this.showCopySuccessMessage('Content copied to clipboard! Share manually on your platform.');
        return true;
      } catch (clipboardError) {
        return false;
      }
    }
  }

  // Get available platforms for current device
  getAvailablePlatforms(): SocialPlatform[] {
    const deviceInfo = mobileDetectionService.getDeviceInfo();
    const available: SocialPlatform[] = [];
    
    // Native sharing is best on mobile when not in an in-app browser
    if (deviceInfo.supportsWebShare && !deviceInfo.isInAppBrowser) {
      available.push(this.platforms.native);
    }
    
    // Always include Twitter/X as it works everywhere
    available.push(this.platforms.twitter);
    
    // Mobile-specific platforms
    if (deviceInfo.isMobile && !deviceInfo.isInAppBrowser) {
      available.push(this.platforms.instagram_story, this.platforms.instagram_post);
      available.push(this.platforms.tiktok);
    }
    
    // Add additional platforms based on context
    if (deviceInfo.isInstagramBrowser) {
      // Already in Instagram, prioritize Instagram sharing
      available.unshift(this.platforms.instagram_story, this.platforms.instagram_post);
    } else if (deviceInfo.isTikTokBrowser) {
      // Already in TikTok, prioritize TikTok sharing
      available.unshift(this.platforms.tiktok);
    }
    
    return available.filter((platform, index, self) => 
      self.findIndex(p => p.id === platform.id) === index
    );
  }

  // Test sharing on current device
  async testSharingCapabilities(): Promise<{
    nativeShare: boolean;
    clipboard: boolean;
    deepLinks: Record<string, boolean>;
    deviceInfo: any;
  }> {
    const deviceInfo = mobileDetectionService.getDeviceInfo();
    
    const testResults = {
      nativeShare: false,
      clipboard: false,
      deepLinks: {} as Record<string, boolean>,
      deviceInfo
    };
    
    // Test native sharing
    if (deviceInfo.supportsWebShare) {
      try {
        const testData = { title: 'Test', text: 'Test', url: window.location.href };
        testResults.nativeShare = navigator.canShare && navigator.canShare(testData);
      } catch (error) {
        testResults.nativeShare = false;
      }
    }
    
    // Test clipboard
    testResults.clipboard = deviceInfo.supportsClipboard;
    
    // Test deep links
    const platforms = ['instagram', 'tiktok', 'twitter', 'whatsapp'];
    for (const platform of platforms) {
      const deepLink = mobileDetectionService.generateDeepLink(platform, {
        text: 'Test', 
        url: window.location.href
      });
      testResults.deepLinks[platform] = !!deepLink;
    }
    
    // Track test results
    try {
      if (analyticsService && typeof analyticsService.track === 'function') {
        analyticsService.track('sharing_capabilities_tested', testResults);
      }
    } catch (error) {
      console.warn('Analytics tracking failed for sharing test:', error);
    }
    
    return testResults;
  }

  // Create shareable URL for a venue/vibe score
  createShareableUrl(venueId: string, vibeScore?: number): string {
    const baseUrl = window.location.origin;
    const params = new URLSearchParams();
    
    if (vibeScore) {
      params.set('vibe', vibeScore.toString());
    }
    
    params.set('ref', 'social_share');
    
    return `${baseUrl}/app/venue/${venueId}?${params.toString()}`;
  }

  // Generate hashtags based on venue and vibe data
  generateHashtags(venue: string, vibeScore?: number): string[] {
    const hashtags = ['Bytspot'];
    
    if (vibeScore && vibeScore >= 8) {
      hashtags.push('PerfectVibe');
    }
    
    hashtags.push('NightlifeFinds', 'SpotFinder');
    
    // Add venue-specific hashtag
    const venueHashtag = venue.replace(/[^a-zA-Z0-9]/g, '');
    if (venueHashtag.length > 3) {
      hashtags.push(venueHashtag);
    }
    
    return hashtags;
  }
}

export const socialMediaService = new SocialMediaService();
export default socialMediaService;