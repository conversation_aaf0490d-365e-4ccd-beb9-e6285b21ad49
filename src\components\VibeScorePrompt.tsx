import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Star, 
  Share2, 
  X, 
  <PERSON>, 
  Spark<PERSON>, 
  <PERSON>, 
  Camera,
  Check,
  ExternalLink
} from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { useAnalytics } from '../lib/hooks/useAnalytics';
import { socialMediaService, type ShareData } from '../lib/services/socialMediaService';
import { notificationService } from '../lib/services/notificationService';

interface VibeScorePromptProps {
  isOpen: boolean;
  onClose: () => void;
  venueData: {
    id: string;
    name: string;
    vibeScore: number;
    visitDuration: number; // in minutes
    userRating?: number;
  };
  onShareVibe: (platform: string, vibeData: any) => void;
  onTakePhoto: () => void;
}

interface SocialPlatform {
  id: string;
  name: string;
  icon: string;
  color: string;
  suggestedText: string;
}

export function VibeScorePrompt({ 
  isOpen, 
  onClose, 
  venueData, 
  onShareVibe,
  onTakePhoto 
}: VibeScorePromptProps) {
  const { track } = useAnalytics();
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [animationPhase, setAnimationPhase] = useState(0);

  // Trigger animation sequence when component opens
  useEffect(() => {
    if (isOpen) {
      // Reset animation
      setAnimationPhase(0);
      setShowShareOptions(false);
      
      // Animate through phases
      const timer1 = setTimeout(() => setAnimationPhase(1), 500);
      const timer2 = setTimeout(() => setAnimationPhase(2), 1000);
      const timer3 = setTimeout(() => setAnimationPhase(3), 1500);
      
      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [isOpen]);

  const socialPlatforms: SocialPlatform[] = [
    {
      id: 'instagram_story',
      name: 'Instagram Story',
      icon: '📸',
      color: 'from-purple-600 to-pink-600',
      suggestedText: `Just experienced an incredible ${venueData.vibeScore}/10 vibe at ${venueData.name}! ✨ The energy here is unmatched! #Bytspot #VibeScore #${venueData.name.replace(/\s+/g, '')}`
    },
    {
      id: 'instagram_post',
      name: 'Instagram Post',
      icon: '📷',
      color: 'from-pink-500 to-rose-600',
      suggestedText: `Perfect ${venueData.vibeScore}/10 vibe at ${venueData.name}! 🎉\n\nFound this gem through @bytspot and the atmosphere is absolutely electric! You need to check this place out! ⚡\n\n#Bytspot #PerfectVibe #${venueData.name.replace(/\s+/g, '')} #NightlifeFinds`
    },
    {
      id: 'twitter',
      name: 'Twitter/X',
      icon: '🐦',
      color: 'from-blue-500 to-cyan-600',
      suggestedText: `🔥 Just hit a ${venueData.vibeScore}/10 vibe score at ${venueData.name}! The energy is UNREAL! Thanks @bytspot for finding me the perfect spot! ⚡ #VibeScore #Bytspot`
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      icon: '🎵',
      color: 'from-gray-800 to-gray-900',
      suggestedText: `POV: You found the perfect spot with @bytspot and the vibe is a solid ${venueData.vibeScore}/10! ✨ #Bytspot #PerfectVibe #SpotFinder #VibeCheck`
    }
  ];

  const getVibeScoreEmoji = (score: number) => {
    if (score >= 9) return '🔥';
    if (score >= 8) return '⚡';
    if (score >= 7) return '✨';
    if (score >= 6) return '🌟';
    return '💫';
  };

  const getVibeScoreColor = (score: number) => {
    if (score >= 9) return 'from-green-400 to-emerald-600';
    if (score >= 8) return 'from-yellow-400 to-orange-500';
    if (score >= 7) return 'from-orange-400 to-red-500';
    if (score >= 6) return 'from-purple-400 to-pink-500';
    return 'from-blue-400 to-purple-500';
  };

  const getVibeScoreMessage = (score: number) => {
    if (score >= 9) return 'Absolutely incredible! Your friends need to know about this place!';
    if (score >= 8) return 'Amazing vibe! This is definitely worth sharing!';
    if (score >= 7) return 'Great atmosphere! Your friends would love this spot!';
    if (score >= 6) return 'Solid vibe! Share your discovery with friends!';
    return 'Good spot! Let your friends know what you found!';
  };

  const handleShare = async (platform: SocialPlatform) => {
    track('vibe_score_shared', {
      venue_id: venueData.id,
      venue_name: venueData.name,
      vibe_score: venueData.vibeScore,
      platform: platform.id,
      visit_duration: venueData.visitDuration
    });
    
    setSelectedPlatform(platform.id);
    
    // Create share data for social media service
    const shareData: ShareData = {
      title: `${venueData.vibeScore}/10 Vibe at ${venueData.name}!`,
      text: platform.suggestedText,
      url: socialMediaService.createShareableUrl(venueData.id, venueData.vibeScore),
      hashtags: socialMediaService.generateHashtags(venueData.name, venueData.vibeScore),
      venue: venueData.name,
      vibeScore: venueData.vibeScore
    };

    try {
      // Use the real social media service
      const success = await socialMediaService.share(platform.id, shareData);
      
      if (success) {
        // Notify about successful share for social currency boost
        await notificationService.notifyAchievementUnlocked(
          'Vibe Influencer', 
          `Shared your ${venueData.vibeScore}/10 vibe score! Your friends will love this recommendation.`
        );
      }
    } catch (error) {
      console.error('Error sharing vibe score:', error);
    }
    
    // Legacy callback for backward compatibility
    const vibeData = {
      venue: venueData.name,
      score: venueData.vibeScore,
      emoji: getVibeScoreEmoji(venueData.vibeScore),
      suggestedText: platform.suggestedText,
      visitDuration: venueData.visitDuration
    };
    
    onShareVibe(platform.id, vibeData);
    
    // Show success state briefly then close
    setTimeout(() => {
      onClose();
      setSelectedPlatform(null);
    }, 2000);
  };

  const handleTakeVibePhoto = () => {
    track('vibe_photo_initiated', {
      venue_id: venueData.id,
      venue_name: venueData.name,
      vibe_score: venueData.vibeScore
    });
    onTakePhoto();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 50 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          className="bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-xl rounded-3xl p-6 max-w-md w-full border border-white/30 shadow-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 w-8 h-8 p-0 hover:bg-white/30"
            onClick={onClose}
          >
            <X className="w-4 h-4" />
          </Button>

          {/* Header Animation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: animationPhase >= 1 ? 1 : 0, y: animationPhase >= 1 ? 0 : 20 }}
            transition={{ duration: 0.5 }}
            className="text-center space-y-4 mb-6"
          >
            {/* Animated Vibe Score */}
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ 
                scale: animationPhase >= 1 ? 1 : 0, 
                rotate: animationPhase >= 1 ? 0 : -180 
              }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className={`w-24 h-24 mx-auto rounded-full bg-gradient-to-r ${getVibeScoreColor(venueData.vibeScore)} flex items-center justify-center shadow-2xl`}
            >
              <span className="text-3xl font-bold text-white">{venueData.vibeScore}</span>
            </motion.div>

            {/* Floating Emojis */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: animationPhase >= 2 ? 1 : 0 }}
              transition={{ duration: 0.4 }}
              className="relative"
            >
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute text-2xl"
                  style={{
                    left: `${20 + i * 15}%`,
                    top: `${-10 + (i % 2) * 20}px`,
                  }}
                  animate={{
                    y: [-5, -15, -5],
                    opacity: [0.7, 1, 0.7],
                    rotate: [0, 10, -10, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                >
                  {getVibeScoreEmoji(venueData.vibeScore)}
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: animationPhase >= 2 ? 1 : 0, y: animationPhase >= 2 ? 0 : 10 }}
              transition={{ duration: 0.4, delay: 0.2 }}
            >
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                {venueData.vibeScore >= 8 ? 'Perfect Vibe!' : 'Great Vibe!'} 
                <span className="text-2xl ml-2">{getVibeScoreEmoji(venueData.vibeScore)}</span>
              </h2>
              <p className="text-gray-600 text-sm mb-1">
                Your vibe score contribution at <span className="font-semibold">{venueData.name}</span> was amazing!
              </p>
              <p className="text-gray-500 text-xs">
                {getVibeScoreMessage(venueData.vibeScore)}
              </p>
            </motion.div>
          </motion.div>

          {/* Share Options */}
          {!showShareOptions ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: animationPhase >= 3 ? 1 : 0, y: animationPhase >= 3 ? 0 : 20 }}
              transition={{ duration: 0.4, delay: 0.3 }}
              className="space-y-4"
            >
              {/* Social Currency Explanation */}
              <Card className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-400/30 p-4">
                <div className="flex items-start gap-3">
                  <Trophy className="w-6 h-6 text-yellow-500 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-800 text-sm mb-1">Social Currency Boost!</h4>
                    <p className="text-xs text-gray-600 mb-2">
                      Sharing high vibe scores makes you the cool, knowledgeable friend who always knows the best spots!
                    </p>
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
                      <Sparkles className="w-3 h-3 mr-1" />
                      +50 Social Points
                    </Badge>
                  </div>
                </div>
              </Card>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  className="w-full h-14 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                  onClick={() => setShowShareOptions(true)}
                >
                  <Share2 className="w-5 h-5 mr-3" />
                  <div className="text-left">
                    <div className="font-semibold">Share Your Vibe Score</div>
                    <div className="text-xs opacity-90">Show friends where the best vibes are</div>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="w-full h-12 bg-white/50 border-gray-300 hover:bg-white/70"
                  onClick={handleTakeVibePhoto}
                >
                  <Camera className="w-5 h-5 mr-2" />
                  Take a Vibe Photo
                </Button>

                <Button
                  variant="ghost"
                  className="w-full text-gray-600 hover:bg-white/30"
                  onClick={onClose}
                >
                  Maybe Later
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-4"
            >
              <div className="text-center mb-4">
                <h3 className="font-semibold text-gray-800 mb-1">Choose Your Platform</h3>
                <p className="text-sm text-gray-600">Share your {venueData.vibeScore}/10 vibe score</p>
              </div>

              <div className="space-y-3">
                {socialPlatforms.map((platform) => (
                  <motion.div
                    key={platform.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: socialPlatforms.indexOf(platform) * 0.1 }}
                  >
                    <Button
                      variant="outline"
                      className={`w-full h-16 justify-start bg-gradient-to-r ${platform.color} text-white border-0 hover:scale-105 transition-transform duration-200 ${
                        selectedPlatform === platform.id ? 'ring-2 ring-white ring-offset-2' : ''
                      }`}
                      onClick={() => handleShare(platform)}
                      disabled={selectedPlatform !== null}
                    >
                      {selectedPlatform === platform.id ? (
                        <div className="flex items-center gap-3">
                          <Check className="w-6 h-6" />
                          <span className="font-semibold">Shared!</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">{platform.icon}</span>
                          <div className="text-left">
                            <div className="font-semibold">{platform.name}</div>
                            <div className="text-xs opacity-90 line-clamp-1">
                              {platform.suggestedText.slice(0, 50)}...
                            </div>
                          </div>
                        </div>
                      )}
                    </Button>
                  </motion.div>
                ))}
              </div>

              <Button
                variant="ghost"
                className="w-full text-gray-600 hover:bg-white/30"
                onClick={() => setShowShareOptions(false)}
                disabled={selectedPlatform !== null}
              >
                Back
              </Button>
            </motion.div>
          )}

          {/* Points Earned Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: animationPhase >= 3 ? 1 : 0, scale: animationPhase >= 3 ? 1 : 0 }}
            transition={{ duration: 0.4, delay: 0.5 }}
            className="absolute -top-3 -right-3"
          >
            <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0 shadow-lg">
              <Star className="w-3 h-3 mr-1 fill-current" />
              +{venueData.vibeScore * 10} Points
            </Badge>
          </motion.div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}