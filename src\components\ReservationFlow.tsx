import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { ArrowLeft, Calendar, Clock, Users, MapPin, CreditCard, Check, QrCode, Download, Share2, Smartphone } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import QRCodeGenerator from 'qrcode';

interface ReservationRequest {
  venueId: string;
  venueName: string;
  date: string;
  time: string;
  partySize: number;
  specialRequests?: string;
}

interface ReservationFlowProps {
  reservationRequest: ReservationRequest;
  onComplete: (confirmed: boolean) => void;
  onBack: () => void;
}

type FlowStep = 'details' | 'payment' | 'confirmation';

export function ReservationFlow({ reservationRequest, onComplete, onBack }: ReservationFlowProps) {
  const [currentStep, setCurrentStep] = useState<FlowStep>('details');
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [reservationId] = useState(() => 'RES-' + Date.now().toString(36).toUpperCase());
  const [formData, setFormData] = useState({
    date: reservationRequest.date,
    time: reservationRequest.time,
    partySize: reservationRequest.partySize,
    specialRequests: reservationRequest.specialRequests || '',
    name: '',
    phone: '',
    email: ''
  });

  // Generate QR code when confirmation step is reached
  const generateQRCode = async () => {
    const reservationData = {
      id: reservationId,
      venue: {
        id: reservationRequest.venueId,
        name: reservationRequest.venueName,
        address: '123 Main Street, Downtown'
      },
      customer: {
        name: formData.name,
        phone: formData.phone,
        email: formData.email
      },
      booking: {
        date: formData.date,
        time: formData.time,
        partySize: formData.partySize,
        specialRequests: formData.specialRequests
      },
      payment: {
        deposit: 25.00,
        currency: 'USD',
        status: 'confirmed'
      },
      metadata: {
        platform: 'Bytspot',
        bookingType: 'venue_reservation',
        confirmationTime: new Date().toISOString(),
        qrVersion: '1.0'
      }
    };

    try {
      const qrDataUrl = await QRCodeGenerator.toDataURL(JSON.stringify(reservationData), {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#ffffff'
        }
      });
      setQrCodeDataUrl(qrDataUrl);
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeDataUrl) return;
    
    const link = document.createElement('a');
    link.download = `bytspot-reservation-${reservationId}.png`;
    link.href = qrCodeDataUrl;
    link.click();
  };

  const shareQRCode = async () => {
    if (!qrCodeDataUrl) return;
    
    try {
      // Convert data URL to blob
      const response = await fetch(qrCodeDataUrl);
      const blob = await response.blob();
      
      if (navigator.share) {
        await navigator.share({
          title: 'Bytspot Reservation',
          text: `My reservation at ${reservationRequest.venueName}`,
          files: [new File([blob], `reservation-${reservationId}.png`, { type: 'image/png' })]
        });
      } else {
        // Fallback: copy to clipboard or show message
        downloadQRCode();
      }
    } catch (error) {
      console.error('Failed to share QR code:', error);
      downloadQRCode(); // Fallback to download
    }
  };

  const handleDetailsSubmit = () => {
    setCurrentStep('payment');
  };

  const handlePaymentSubmit = () => {
    setCurrentStep('confirmation');
    // Generate QR code when moving to confirmation
    generateQRCode();
    setTimeout(() => {
      onComplete(true);
    }, 2000);
  };

  const timeSlots = [
    '5:00 PM', '5:30 PM', '6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM',
    '8:00 PM', '8:30 PM', '9:00 PM', '9:30 PM', '10:00 PM', '10:30 PM'
  ];

  const renderDetailsStep = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Reserve Your Table</h2>
        <p className="text-white/70">Complete your reservation details</p>
      </div>

      {/* Venue Info */}
      <div className="bg-white/10 rounded-2xl p-4 border border-white/20">
        <div className="flex items-center gap-3 mb-2">
          <MapPin className="w-5 h-5 text-white" />
          <h3 className="text-white font-semibold">{reservationRequest.venueName}</h3>
        </div>
        <p className="text-white/70 text-sm">Premium dining experience with city views</p>
      </div>

      {/* Reservation Details */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-white mb-2 block">Date</Label>
            <Input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
              className="bg-white/10 border-white/20 text-white"
            />
          </div>
          <div>
            <Label className="text-white mb-2 block">Time</Label>
            <Select value={formData.time} onValueChange={(value) => setFormData({ ...formData, time: value })}>
              <SelectTrigger className="bg-white/10 border-white/20 text-white">
                <SelectValue placeholder="Select time" />
              </SelectTrigger>
              <SelectContent>
                {timeSlots.map((time) => (
                  <SelectItem key={time} value={time}>{time}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label className="text-white mb-2 block">Party Size</Label>
          <Select value={formData.partySize.toString()} onValueChange={(value) => setFormData({ ...formData, partySize: parseInt(value) })}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white">
              <SelectValue placeholder="Select party size" />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3, 4, 5, 6, 7, 8].map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size} {size === 1 ? 'Guest' : 'Guests'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Contact Information */}
        <div className="space-y-3">
          <h4 className="text-white font-medium">Contact Information</h4>
          <Input
            placeholder="Full Name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
          <Input
            placeholder="Phone Number"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
          <Input
            placeholder="Email Address"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
        </div>

        <div>
          <Label className="text-white mb-2 block">Special Requests (Optional)</Label>
          <Textarea
            placeholder="Dietary restrictions, celebration details, seating preferences..."
            value={formData.specialRequests}
            onChange={(e) => setFormData({ ...formData, specialRequests: e.target.value })}
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
            rows={3}
          />
        </div>
      </div>

      <Button
        className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3"
        onClick={handleDetailsSubmit}
        disabled={!formData.name || !formData.phone || !formData.email}
      >
        Continue to Payment
      </Button>
    </motion.div>
  );

  const renderPaymentStep = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Secure Your Reservation</h2>
        <p className="text-white/70">Small deposit required to confirm</p>
      </div>

      {/* Reservation Summary */}
      <div className="bg-white/10 rounded-2xl p-4 border border-white/20 space-y-3">
        <h3 className="text-white font-semibold mb-3">Reservation Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between text-white/80">
            <span>Venue:</span>
            <span>{reservationRequest.venueName}</span>
          </div>
          <div className="flex justify-between text-white/80">
            <span>Date & Time:</span>
            <span>{formData.date} at {formData.time}</span>
          </div>
          <div className="flex justify-between text-white/80">
            <span>Party Size:</span>
            <span>{formData.partySize} guests</span>
          </div>
          <div className="border-t border-white/20 pt-2 mt-3">
            <div className="flex justify-between text-white font-semibold">
              <span>Deposit Required:</span>
              <span>$25.00</span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Form */}
      <div className="space-y-4">
        <h4 className="text-white font-medium">Payment Information</h4>
        <Input
          placeholder="Card Number"
          className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
        />
        <div className="grid grid-cols-2 gap-4">
          <Input
            placeholder="MM/YY"
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
          <Input
            placeholder="CVV"
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
        </div>
        <Input
          placeholder="Cardholder Name"
          className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
        />
      </div>

      <div className="bg-blue-500/20 border border-blue-400/30 rounded-xl p-3">
        <p className="text-blue-200 text-sm">
          🔒 Your payment is secured with 256-bit SSL encryption. The deposit will be applied to your final bill.
        </p>
      </div>

      <Button
        className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3"
        onClick={handlePaymentSubmit}
      >
        <CreditCard className="w-5 h-5 mr-2" />
        Confirm Reservation ($25.00)
      </Button>
    </motion.div>
  );

  const renderConfirmationStep = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring", bounce: 0.5 }}
        className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto"
      >
        <Check className="w-10 h-10 text-white" />
      </motion.div>

      <div>
        <h2 className="text-2xl font-bold text-white mb-2">Reservation Confirmed!</h2>
        <p className="text-white/70">Your table is secured</p>
      </div>

      {/* Reservation ID */}
      <div className="bg-white/10 rounded-xl p-4 border border-white/20">
        <p className="text-white/60 text-sm mb-1">Reservation ID</p>
        <p className="text-white font-mono text-lg font-semibold">{reservationId}</p>
      </div>

      {/* QR Code Section */}
      {qrCodeDataUrl && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl p-6 border border-white/20"
        >
          <div className="flex items-center gap-2 justify-center mb-4">
            <QrCode className="w-5 h-5 text-purple-600" />
            <h3 className="font-semibold text-gray-800">Reservation QR Code</h3>
          </div>
          
          <div className="mb-4">
            <img
              src={qrCodeDataUrl}
              alt="Reservation QR Code"
              className="w-48 h-48 mx-auto rounded-lg"
            />
          </div>
          
          <p className="text-sm text-gray-600 mb-4 leading-relaxed">
            <Smartphone className="w-4 h-4 inline mr-1" />
            Show this QR code at the venue for check-in and receipt generation. 
            Contains all reservation details for AI training.
          </p>

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={downloadQRCode}
              className="border-purple-300 text-purple-600 hover:bg-purple-50"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={shareQRCode}
              className="border-blue-300 text-blue-600 hover:bg-blue-50"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </motion.div>
      )}

      <div className="bg-white/10 rounded-2xl p-6 border border-white/20">
        <h3 className="text-white font-semibold mb-4">Reservation Details</h3>
        <div className="space-y-3 text-left">
          <div className="flex items-center gap-3">
            <MapPin className="w-5 h-5 text-green-400" />
            <div>
              <p className="text-white font-medium">{reservationRequest.venueName}</p>
              <p className="text-white/60 text-sm">123 Main Street, Downtown</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Calendar className="w-5 h-5 text-blue-400" />
            <div>
              <p className="text-white font-medium">{formData.date}</p>
              <p className="text-white/60 text-sm">{formData.time}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Users className="w-5 h-5 text-purple-400" />
            <div>
              <p className="text-white font-medium">{formData.partySize} Guests</p>
              <p className="text-white/60 text-sm">Table reserved for your party</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-xl p-4">
        <p className="text-green-200 text-sm">
          ✅ Confirmation details sent to {formData.email}
        </p>
        <p className="text-green-200/70 text-xs mt-1">
          QR code contains encrypted reservation data for seamless check-in
        </p>
      </div>
    </motion.div>
  );

  return (
    <div className="h-full bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_white_1px,_transparent_0)] bg-[size:20px_20px]" />
      </div>

      {/* Header */}
      <div className="relative z-10 p-6 pb-0">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/10 p-2"
            onClick={onBack}
          >
            <ArrowLeft className="w-6 h-6" />
          </Button>
          <div className="flex items-center gap-2">
            {(['details', 'payment', 'confirmation'] as FlowStep[]).map((step, index) => (
              <div
                key={step}
                className={`w-3 h-3 rounded-full transition-colors ${
                  step === currentStep
                    ? 'bg-white'
                    : index < (['details', 'payment', 'confirmation'] as FlowStep[]).indexOf(currentStep)
                    ? 'bg-green-400'
                    : 'bg-white/30'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 px-6 pb-6 max-w-md mx-auto">
        <AnimatePresence mode="wait">
          {currentStep === 'details' && renderDetailsStep()}
          {currentStep === 'payment' && renderPaymentStep()}
          {currentStep === 'confirmation' && renderConfirmationStep()}
        </AnimatePresence>
      </div>
    </div>
  );
}