import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Users, 
  X, 
  Search, 
  UserPlus, 
  Check, 
  MapPin,
  Camera,
  Share2,
  Crown,
  Sparkles
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { useAnalytics } from '../lib/hooks/useAnalytics';

interface Friend {
  id: string;
  name: string;
  username: string;
  avatar: string;
  isOnBytspot: boolean;
  mutualFriends?: number;
}

interface FriendTaggingProps {
  isOpen: boolean;
  onClose: () => void;
  venueData: {
    id: string;
    name: string;
    address: string;
    vibeScore?: number;
  };
  onTagComplete: (taggedFriends: Friend[], shareData: any) => void;
}

export function FriendTagging({ 
  isOpen, 
  onClose, 
  venueData, 
  onTagComplete 
}: FriendTaggingProps) {
  const { track } = useAnalytics();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFriends, setSelectedFriends] = useState<Friend[]>([]);
  const [shareToSocial, setShareToSocial] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Mock friends data - replace with real data from your backend
  const allFriends: Friend[] = [
    {
      id: '1',
      name: 'Sarah Martinez',
      username: '@sarah_m',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b131?w=150&h=150&fit=crop&crop=face',
      isOnBytspot: true,
      mutualFriends: 12
    },
    {
      id: '2',
      name: 'Mike Chen',
      username: '@mike_explores',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      isOnBytspot: true,
      mutualFriends: 8
    },
    {
      id: '3',
      name: 'Emma Wilson',
      username: '@emmaw',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      isOnBytspot: false
    },
    {
      id: '4',
      name: 'Alex Rodriguez',
      username: '@alex_rod',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      isOnBytspot: true,
      mutualFriends: 15
    },
    {
      id: '5',
      name: 'Jessica Taylor',
      username: '@jess_t',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face',
      isOnBytspot: false
    }
  ];

  const filteredFriends = allFriends.filter(friend =>
    friend.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    friend.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleFriendToggle = (friend: Friend) => {
    setSelectedFriends(prev => {
      const isAlreadySelected = prev.find(f => f.id === friend.id);
      if (isAlreadySelected) {
        return prev.filter(f => f.id !== friend.id);
      } else {
        return [...prev, friend];
      }
    });
  };

  const handleTag = () => {
    if (selectedFriends.length === 0) return;

    track('friends_tagged_at_venue', {
      venue_id: venueData.id,
      venue_name: venueData.name,
      friends_count: selectedFriends.length,
      friends_on_bytspot: selectedFriends.filter(f => f.isOnBytspot).length,
      share_to_social: shareToSocial
    });

    const shareData = {
      venue: venueData.name,
      friends: selectedFriends,
      shareToSocial,
      suggestedText: `Had an amazing time at ${venueData.name} with ${selectedFriends.map(f => f.username).join(', ')}! ${venueData.vibeScore ? `The vibe was ${venueData.vibeScore}/10!` : ''} #Bytspot #SquadGoals`
    };

    setShowSuccess(true);
    
    setTimeout(() => {
      onTagComplete(selectedFriends, shareData);
      onClose();
      setShowSuccess(false);
      setSelectedFriends([]);
      setSearchTerm('');
      setShareToSocial(false);
    }, 2000);
  };

  const bytspotFriends = selectedFriends.filter(f => f.isOnBytspot);
  const nonBytspotFriends = selectedFriends.filter(f => !f.isOnBytspot);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 50 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-xl rounded-3xl p-6 max-w-md w-full max-h-[80vh] overflow-hidden border border-white/30 shadow-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          {showSuccess ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center space-y-4 py-8"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
                className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mx-auto flex items-center justify-center"
              >
                <Check className="w-8 h-8 text-white" />
              </motion.div>
              
              <div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">Friends Tagged!</h3>
                <p className="text-gray-600">
                  {bytspotFriends.length > 0 && (
                    <>
                      {bytspotFriends.length} friend{bytspotFriends.length > 1 ? 's' : ''} will be notified on Bytspot
                      {nonBytspotFriends.length > 0 && ', and '}
                    </>
                  )}
                  {nonBytspotFriends.length > 0 && (
                    <>
                      {nonBytspotFriends.length} will get an invite to join!
                    </>
                  )}
                </p>
              </div>

              {shareToSocial && (
                <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl p-4 border border-purple-400/30">
                  <div className="flex items-center gap-2 justify-center">
                    <Share2 className="w-5 h-5 text-purple-600" />
                    <span className="text-purple-800 font-medium">Sharing to social media...</span>
                  </div>
                </div>
              )}
            </motion.div>
          ) : (
            <>
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-800">Tag Friends</h2>
                    <p className="text-sm text-gray-600">Who are you with at {venueData.name}?</p>
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-8 h-8 p-0 hover:bg-white/30"
                  onClick={onClose}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Search */}
              <div className="relative mb-6">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search friends..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/50 border-gray-300"
                />
              </div>

              {/* Selected Friends Preview */}
              {selectedFriends.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="mb-6"
                >
                  <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-4 border border-blue-400/30">
                    <div className="flex items-center gap-2 mb-3">
                      <Users className="w-4 h-4 text-blue-600" />
                      <span className="font-medium text-blue-800">
                        {selectedFriends.length} friend{selectedFriends.length > 1 ? 's' : ''} selected
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      {selectedFriends.map((friend) => (
                        <div
                          key={friend.id}
                          className="flex items-center gap-2 bg-white/50 rounded-full px-3 py-1 text-sm"
                        >
                          <Avatar className="w-5 h-5">
                            <AvatarImage src={friend.avatar} alt={friend.name} />
                            <AvatarFallback>{friend.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span>{friend.name.split(' ')[0]}</span>
                          {friend.isOnBytspot && <Crown className="w-3 h-3 text-yellow-500" />}
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Friends List */}
              <div className="space-y-3 max-h-64 overflow-y-auto mb-6">
                {filteredFriends.map((friend) => {
                  const isSelected = selectedFriends.find(f => f.id === friend.id);
                  
                  return (
                    <motion.div
                      key={friend.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`
                        p-3 rounded-xl border transition-all duration-200 cursor-pointer
                        ${isSelected 
                          ? 'bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-400/50' 
                          : 'bg-white/30 border-white/40 hover:bg-white/50'
                        }
                      `}
                      onClick={() => handleFriendToggle(friend)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <Avatar className="w-10 h-10">
                              <AvatarImage src={friend.avatar} alt={friend.name} />
                              <AvatarFallback>{friend.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            {friend.isOnBytspot && (
                              <Crown className="absolute -top-1 -right-1 w-4 h-4 text-yellow-500" />
                            )}
                          </div>
                          
                          <div>
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-gray-800">{friend.name}</p>
                              {!friend.isOnBytspot && (
                                <Badge variant="outline" className="text-xs">
                                  Invite
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-600">{friend.username}</p>
                            {friend.mutualFriends && (
                              <p className="text-xs text-gray-500">
                                {friend.mutualFriends} mutual friends
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className={`
                          w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200
                          ${isSelected 
                            ? 'bg-purple-600 border-purple-600' 
                            : 'border-gray-300'
                          }
                        `}>
                          {isSelected && <Check className="w-3 h-3 text-white" />}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Social Media Sharing Option */}
              <div className="mb-6">
                <label className="flex items-center gap-3 p-3 bg-white/30 rounded-xl border border-white/40 cursor-pointer hover:bg-white/50 transition-colors">
                  <input
                    type="checkbox"
                    checked={shareToSocial}
                    onChange={(e) => setShareToSocial(e.target.checked)}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                  <div className="flex items-center gap-2">
                    <Share2 className="w-4 h-4 text-purple-600" />
                    <span className="text-sm font-medium text-gray-800">
                      Also share to social media
                    </span>
                  </div>
                </label>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50"
                  onClick={handleTag}
                  disabled={selectedFriends.length === 0}
                >
                  <Users className="w-5 h-5 mr-2" />
                  Tag {selectedFriends.length} Friend{selectedFriends.length !== 1 ? 's' : ''}
                  {bytspotFriends.length > 0 && nonBytspotFriends.length > 0 && ' & Invite Others'}
                </Button>

                <Button
                  variant="ghost"
                  className="w-full text-gray-600 hover:bg-white/30"
                  onClick={onClose}
                >
                  Cancel
                </Button>
              </div>

              {/* Benefits Explanation */}
              {selectedFriends.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-400/30"
                >
                  <div className="flex items-start gap-2">
                    <Sparkles className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" />
                    <div className="text-xs text-green-800">
                      <p className="font-medium mb-1">Social Currency Boost!</p>
                      {bytspotFriends.length > 0 && (
                        <p>• {bytspotFriends.length} friend{bytspotFriends.length > 1 ? 's' : ''} will see your great taste in spots</p>
                      )}
                      {nonBytspotFriends.length > 0 && (
                        <p>• {nonBytspotFriends.length} friend{nonBytspotFriends.length > 1 ? 's' : ''} will get invited to join Bytspot (+200 points each!)</p>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}