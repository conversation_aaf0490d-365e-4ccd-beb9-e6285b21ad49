import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  ArrowLeft, 
  Building2, 
  Car, 
  Users, 
  MapPin, 
  Camera, 
  Star, 
  Plus,
  Check,
  Upload,
  Globe,
  Navigation,
  Calendar,
  DollarSign,
  Clock,
  Shield,
  Phone,
  Mail,
  AlertCircle,
  Search,
  Edit3,
  Trash2,
  ExternalLink
} from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Checkbox } from './ui/checkbox';
import { Switch } from './ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Separator } from './ui/separator';
import { Progress } from './ui/progress';
import { Dialog, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from './ui/dialog';

interface HostOnboardingProps {
  onBack: () => void;
  onComplete: () => void;
}

type ServiceType = 'venue' | 'parking' | 'valet';
type OnboardingStep = 'type-selection' | 'location-setup' | 'service-details' | 'verification' | 'review';

interface LocationData {
  address: string;
  coordinates?: { lat: number; lng: number };
  placeId?: string;
  verified: boolean;
  photos: string[];
  reviews: Review[];
}

interface Review {
  id: string;
  rating: number;
  comment: string;
  author: string;
  date: string;
}

interface ServiceDetails {
  name: string;
  description: string;
  capacity?: number;
  amenities: string[];
  pricing: PricingInfo;
  availability: AvailabilityInfo;
  policies: string[];
}

interface PricingInfo {
  basePrice: number;
  currency: string;
  unit: string; // per hour, per night, per service
  dynamicPricing: boolean;
}

interface AvailabilityInfo {
  schedule: Record<string, { open: string; close: string; available: boolean }>;
  advanceBooking: number; // days
  minDuration?: number; // minutes
  maxDuration?: number; // minutes
}

export function HostOnboarding({ onBack, onComplete }: HostOnboardingProps) {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('type-selection');
  const [serviceType, setServiceType] = useState<ServiceType | null>(null);
  const [isApiVerifying, setIsApiVerifying] = useState(false);
  
  // Form data
  const [locationData, setLocationData] = useState<LocationData>({
    address: '',
    verified: false,
    photos: [],
    reviews: []
  });
  
  const [serviceDetails, setServiceDetails] = useState<ServiceDetails>({
    name: '',
    description: '',
    amenities: [],
    pricing: {
      basePrice: 0,
      currency: 'USD',
      unit: 'per reservation',
      dynamicPricing: false
    },
    availability: {
      schedule: {
        monday: { open: '09:00', close: '17:00', available: true },
        tuesday: { open: '09:00', close: '17:00', available: true },
        wednesday: { open: '09:00', close: '17:00', available: true },
        thursday: { open: '09:00', close: '17:00', available: true },
        friday: { open: '09:00', close: '17:00', available: true },
        saturday: { open: '10:00', close: '16:00', available: true },
        sunday: { open: '10:00', close: '16:00', available: false }
      },
      advanceBooking: 30
    },
    policies: []
  });

  const [uploadedPhotos, setUploadedPhotos] = useState<string[]>([]);
  const [verificationStatus, setVerificationStatus] = useState({
    googlePlaces: false,
    businessLicense: false,
    insurance: false,
    ownership: false
  });

  // Mock API functions (in real app, these would be actual API calls)
  const mockGooglePlacesAPI = async (address: string) => {
    setIsApiVerifying(true);
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const mockResponse = {
      placeId: 'ChIJmocked-place-id',
      coordinates: { lat: 37.7749, lng: -122.4194 },
      verified: true,
      formattedAddress: `${address}, San Francisco, CA 94102`
    };
    
    setLocationData(prev => ({
      ...prev,
      address: mockResponse.formattedAddress,
      placeId: mockResponse.placeId,
      coordinates: mockResponse.coordinates,
      verified: true
    }));
    
    setIsApiVerifying(false);
    return mockResponse;
  };

  const getServiceTypeConfig = (type: ServiceType) => {
    const configs = {
      venue: {
        title: 'Venue Hosting',
        icon: Building2,
        description: 'Restaurants, bars, event spaces, and entertainment venues',
        color: 'from-purple-500 to-pink-500',
        amenities: ['WiFi', 'Sound System', 'Outdoor Seating', 'Full Bar', 'Kitchen', 'Private Events', 'Live Music', 'Dance Floor'],
        unit: 'per reservation'
      },
      parking: {
        title: 'Parking Management',
        icon: Car,
        description: 'Parking lots, garages, and private parking spaces',
        color: 'from-blue-500 to-cyan-500',
        amenities: ['Covered', 'Security', 'EV Charging', 'Valet Service', '24/7 Access', 'Reserved Spots', 'Surveillance', 'Attendant'],
        unit: 'per hour'
      },
      valet: {
        title: 'Valet Service',
        icon: Users,
        description: 'Professional valet and concierge services',
        color: 'from-green-500 to-emerald-500',
        amenities: ['Licensed Drivers', 'Insurance Coverage', 'Real-time Tracking', 'Car Wash', 'Maintenance Check', 'Fuel Service', '24/7 Support'],
        unit: 'per service'
      }
    };
    return configs[type];
  };

  const handleLocationVerification = async () => {
    if (!locationData.address.trim()) return;
    
    try {
      await mockGooglePlacesAPI(locationData.address);
      // Additional verification steps would go here
    } catch (error) {
      console.error('Location verification failed:', error);
    }
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      // In a real app, you'd upload to a storage service
      const newPhotos = Array.from(files).map(file => URL.createObjectURL(file));
      setUploadedPhotos(prev => [...prev, ...newPhotos]);
      setLocationData(prev => ({
        ...prev,
        photos: [...prev.photos, ...newPhotos]
      }));
    }
  };

  const handleAddReview = () => {
    const newReview: Review = {
      id: Date.now().toString(),
      rating: 5,
      comment: 'Great location with excellent service!',
      author: 'Mock Review',
      date: new Date().toISOString().split('T')[0]
    };
    
    setLocationData(prev => ({
      ...prev,
      reviews: [...prev.reviews, newReview]
    }));
  };

  const progress = {
    'type-selection': 20,
    'location-setup': 40,
    'service-details': 60,
    'verification': 80,
    'review': 100
  }[currentStep];

  const renderTypeSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">What would you like to host?</h2>
        <p className="text-gray-600 text-lg">Choose the type of service you want to offer on Bytspot</p>
      </div>

      <div className="grid gap-4">
        {(['venue', 'parking', 'valet'] as ServiceType[]).map((type) => {
          const config = getServiceTypeConfig(type);
          const Icon = config.icon;
          
          return (
            <motion.div
              key={type}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setServiceType(type);
                setServiceDetails(prev => ({
                  ...prev,
                  pricing: { ...prev.pricing, unit: config.unit }
                }));
              }}
            >
              <Card className={`p-6 cursor-pointer transition-all duration-200 border-2 ${
                serviceType === type 
                  ? 'border-purple-500 bg-purple-50' 
                  : 'border-gray-200 hover:border-purple-300'
              }`}>
                <div className="flex items-center gap-4">
                  <div className={`p-3 rounded-2xl bg-gradient-to-r ${config.color} text-white`}>
                    <Icon className="w-8 h-8" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">{config.title}</h3>
                    <p className="text-gray-600">{config.description}</p>
                  </div>
                  {serviceType === type && (
                    <Check className="w-6 h-6 text-purple-600" />
                  )}
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      <Button
        onClick={() => setCurrentStep('location-setup')}
        disabled={!serviceType}
        className="w-full py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 h-14"
      >
        Continue
      </Button>
    </div>
  );

  const renderLocationSetup = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">Location Setup</h2>
        <p className="text-gray-600 text-lg">Add your location and verify it with Google Places API</p>
      </div>

      <Card className="p-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="address">Location Address</Label>
            <div className="flex gap-2 mt-2">
              <div className="relative flex-1">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <Input
                  id="address"
                  value={locationData.address}
                  onChange={(e) => setLocationData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="Enter your business address..."
                  className="pl-10"
                />
              </div>
              <Button
                onClick={handleLocationVerification}
                disabled={isApiVerifying || !locationData.address.trim()}
                className="px-6"
              >
                {isApiVerifying ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Search className="w-4 h-4" />
                  </motion.div>
                ) : (
                  'Verify'
                )}
              </Button>
            </div>
          </div>

          {locationData.verified && (
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <Check className="w-5 h-5 text-green-600" />
              <span className="text-green-800 font-medium">Location verified with Google Places API</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <ExternalLink className="w-3 h-3 mr-1" />
                Verified
              </Badge>
            </div>
          )}

          <Separator />

          {/* Photo Upload */}
          <div>
            <Label>Location Photos</Label>
            <div className="mt-2">
              <div className="flex flex-wrap gap-4">
                {uploadedPhotos.map((photo, index) => (
                  <div key={index} className="relative w-24 h-24">
                    <img
                      src={photo}
                      alt={`Location ${index + 1}`}
                      className="w-full h-full object-cover rounded-lg"
                    />
                    <Button
                      size="sm"
                      variant="destructive"
                      className="absolute -top-2 -right-2 w-6 h-6 p-0 rounded-full"
                      onClick={() => {
                        setUploadedPhotos(prev => prev.filter((_, i) => i !== index));
                        setLocationData(prev => ({
                          ...prev,
                          photos: prev.photos.filter((_, i) => i !== index)
                        }));
                      }}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
                
                <label className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-purple-500 transition-colors">
                  <Camera className="w-6 h-6 text-gray-400 mb-1" />
                  <span className="text-xs text-gray-500">Add Photo</span>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    className="hidden"
                  />
                </label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Reviews Management */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <Label>Customer Reviews</Label>
              <Button
                size="sm"
                variant="outline"
                onClick={handleAddReview}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Review
              </Button>
            </div>
            
            <div className="space-y-3">
              {locationData.reviews.map((review) => (
                <Card key={review.id} className="p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                      <span className="font-medium text-gray-800">{review.author}</span>
                    </div>
                    <span className="text-sm text-gray-500">{review.date}</span>
                  </div>
                  <p className="text-gray-600">{review.comment}</p>
                </Card>
              ))}
              
              {locationData.reviews.length === 0 && (
                <p className="text-gray-500 text-center py-4">No reviews yet. Add your first review to get started!</p>
              )}
            </div>
          </div>

          {/* Address Updates */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">Address & Road Information</h4>
                <p className="text-blue-800 text-sm mb-3">
                  Keep your location information up to date. This helps customers find you easily and improves map accuracy.
                </p>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="text-blue-700 border-blue-300">
                    Update Road Info
                  </Button>
                  <Button size="sm" variant="outline" className="text-blue-700 border-blue-300">
                    Report Changes
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={() => setCurrentStep('type-selection')}
          className="flex-1"
        >
          Back
        </Button>
        <Button
          onClick={() => setCurrentStep('service-details')}
          disabled={!locationData.verified}
          className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
        >
          Continue
        </Button>
      </div>
    </div>
  );

  const renderServiceDetails = () => {
    const config = serviceType ? getServiceTypeConfig(serviceType) : null;
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Service Details</h2>
          <p className="text-gray-600 text-lg">Configure your {config?.title.toLowerCase()} settings</p>
        </div>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="pricing">Pricing</TabsTrigger>
            <TabsTrigger value="availability">Availability</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card className="p-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Service Name</Label>
                  <Input
                    id="name"
                    value={serviceDetails.name}
                    onChange={(e) => setServiceDetails(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={`Enter your ${serviceType} name...`}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={serviceDetails.description}
                    onChange={(e) => setServiceDetails(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your service, amenities, and what makes it special..."
                    rows={4}
                    className="mt-2"
                  />
                </div>

                {serviceType !== 'valet' && (
                  <div>
                    <Label htmlFor="capacity">Capacity</Label>
                    <Input
                      id="capacity"
                      type="number"
                      value={serviceDetails.capacity || ''}
                      onChange={(e) => setServiceDetails(prev => ({ ...prev, capacity: parseInt(e.target.value) || undefined }))}
                      placeholder={serviceType === 'venue' ? 'Maximum guests' : 'Parking spots'}
                      className="mt-2"
                    />
                  </div>
                )}

                <div>
                  <Label>Amenities & Features</Label>
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    {config?.amenities.map((amenity) => (
                      <label key={amenity} className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <Checkbox
                          checked={serviceDetails.amenities.includes(amenity)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setServiceDetails(prev => ({
                                ...prev,
                                amenities: [...prev.amenities, amenity]
                              }));
                            } else {
                              setServiceDetails(prev => ({
                                ...prev,
                                amenities: prev.amenities.filter(a => a !== amenity)
                              }));
                            }
                          }}
                        />
                        <span className="text-sm">{amenity}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <Card className="p-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="basePrice">Base Price</Label>
                  <div className="flex gap-2 mt-2">
                    <div className="relative flex-1">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        id="basePrice"
                        type="number"
                        step="0.01"
                        value={serviceDetails.pricing.basePrice}
                        onChange={(e) => setServiceDetails(prev => ({
                          ...prev,
                          pricing: { ...prev.pricing, basePrice: parseFloat(e.target.value) || 0 }
                        }))}
                        placeholder="0.00"
                        className="pl-10"
                      />
                    </div>
                    <Select
                      value={serviceDetails.pricing.unit}
                      onValueChange={(value) => setServiceDetails(prev => ({
                        ...prev,
                        pricing: { ...prev.pricing, unit: value }
                      }))}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="per hour">per hour</SelectItem>
                        <SelectItem value="per day">per day</SelectItem>
                        <SelectItem value="per service">per service</SelectItem>
                        <SelectItem value="per reservation">per reservation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium">Dynamic Pricing</h4>
                    <p className="text-sm text-gray-600">Automatically adjust prices based on demand</p>
                  </div>
                  <Switch
                    checked={serviceDetails.pricing.dynamicPricing}
                    onCheckedChange={(checked) => setServiceDetails(prev => ({
                      ...prev,
                      pricing: { ...prev.pricing, dynamicPricing: checked }
                    }))}
                  />
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="availability" className="space-y-4">
            <Card className="p-6">
              <div className="space-y-4">
                <div>
                  <Label>Weekly Schedule</Label>
                  <div className="mt-2 space-y-2">
                    {Object.entries(serviceDetails.availability.schedule).map(([day, schedule]) => (
                      <div key={day} className="flex items-center gap-4 p-3 border rounded-lg">
                        <div className="w-20">
                          <span className="capitalize font-medium">{day}</span>
                        </div>
                        <Switch
                          checked={schedule.available}
                          onCheckedChange={(checked) => setServiceDetails(prev => ({
                            ...prev,
                            availability: {
                              ...prev.availability,
                              schedule: {
                                ...prev.availability.schedule,
                                [day]: { ...schedule, available: checked }
                              }
                            }
                          }))}
                        />
                        {schedule.available && (
                          <>
                            <Input
                              type="time"
                              value={schedule.open}
                              onChange={(e) => setServiceDetails(prev => ({
                                ...prev,
                                availability: {
                                  ...prev.availability,
                                  schedule: {
                                    ...prev.availability.schedule,
                                    [day]: { ...schedule, open: e.target.value }
                                  }
                                }
                              }))}
                              className="w-32"
                            />
                            <span>to</span>
                            <Input
                              type="time"
                              value={schedule.close}
                              onChange={(e) => setServiceDetails(prev => ({
                                ...prev,
                                availability: {
                                  ...prev.availability,
                                  schedule: {
                                    ...prev.availability.schedule,
                                    [day]: { ...schedule, close: e.target.value }
                                  }
                                }
                              }))}
                              className="w-32"
                            />
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label htmlFor="advanceBooking">Advance Booking (days)</Label>
                  <Input
                    id="advanceBooking"
                    type="number"
                    value={serviceDetails.availability.advanceBooking}
                    onChange={(e) => setServiceDetails(prev => ({
                      ...prev,
                      availability: { ...prev.availability, advanceBooking: parseInt(e.target.value) || 0 }
                    }))}
                    className="mt-2"
                  />
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => setCurrentStep('location-setup')}
            className="flex-1"
          >
            Back
          </Button>
          <Button
            onClick={() => setCurrentStep('verification')}
            disabled={!serviceDetails.name || !serviceDetails.description}
            className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
          >
            Continue
          </Button>
        </div>
      </div>
    );
  };

  const renderVerification = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">API Verification</h2>
        <p className="text-gray-600 text-lg">Verify your business with external APIs and services</p>
      </div>

      <Card className="p-6">
        <div className="space-y-6">
          {/* Google Places Verification */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Globe className="w-6 h-6 text-blue-500" />
              <div>
                <h4 className="font-medium">Google Places API</h4>
                <p className="text-sm text-gray-600">Verify location and business information</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {verificationStatus.googlePlaces ? (
                <Badge className="bg-green-100 text-green-800">
                  <Check className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              ) : (
                <Button
                  size="sm"
                  onClick={() => setVerificationStatus(prev => ({ ...prev, googlePlaces: true }))}
                >
                  Verify Now
                </Button>
              )}
            </div>
          </div>

          {/* Business License */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Shield className="w-6 h-6 text-green-500" />
              <div>
                <h4 className="font-medium">Business License</h4>
                <p className="text-sm text-gray-600">Upload business registration documents</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {verificationStatus.businessLicense ? (
                <Badge className="bg-green-100 text-green-800">
                  <Check className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              ) : (
                <Button
                  size="sm"
                  onClick={() => setVerificationStatus(prev => ({ ...prev, businessLicense: true }))}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload
                </Button>
              )}
            </div>
          </div>

          {/* Insurance Verification */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Shield className="w-6 h-6 text-purple-500" />
              <div>
                <h4 className="font-medium">Insurance Coverage</h4>
                <p className="text-sm text-gray-600">Verify liability insurance</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {verificationStatus.insurance ? (
                <Badge className="bg-green-100 text-green-800">
                  <Check className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              ) : (
                <Button
                  size="sm"
                  onClick={() => setVerificationStatus(prev => ({ ...prev, insurance: true }))}
                >
                  Verify
                </Button>
              )}
            </div>
          </div>

          {/* Ownership Verification */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Building2 className="w-6 h-6 text-orange-500" />
              <div>
                <h4 className="font-medium">Property Ownership</h4>
                <p className="text-sm text-gray-600">Verify property ownership or lease agreement</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {verificationStatus.ownership ? (
                <Badge className="bg-green-100 text-green-800">
                  <Check className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              ) : (
                <Button
                  size="sm"
                  onClick={() => setVerificationStatus(prev => ({ ...prev, ownership: true }))}
                >
                  Verify
                </Button>
              )}
            </div>
          </div>

          {/* API Integration Status */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">API Integrations</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Badge variant="outline">Google Maps</Badge>
                <span className="text-sm text-blue-800">Connected</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Places API</Badge>
                <span className="text-sm text-blue-800">Connected</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Stripe</Badge>
                <span className="text-sm text-gray-600">Pending</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Twilio</Badge>
                <span className="text-sm text-gray-600">Pending</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={() => setCurrentStep('service-details')}
          className="flex-1"
        >
          Back
        </Button>
        <Button
          onClick={() => setCurrentStep('review')}
          disabled={!Object.values(verificationStatus).every(status => status)}
          className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
        >
          Continue
        </Button>
      </div>
    </div>
  );

  const renderReview = () => {
    const config = serviceType ? getServiceTypeConfig(serviceType) : null;
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Review & Submit</h2>
          <p className="text-gray-600 text-lg">Review your information before going live</p>
        </div>

        <Card className="p-6">
          <div className="space-y-6">
            {/* Service Overview */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Service Overview</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-gray-600">Type</Label>
                  <p className="font-medium">{config?.title}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Name</Label>
                  <p className="font-medium">{serviceDetails.name}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Base Price</Label>
                  <p className="font-medium">${serviceDetails.pricing.basePrice} {serviceDetails.pricing.unit}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Capacity</Label>
                  <p className="font-medium">{serviceDetails.capacity || 'Not specified'}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Location Info */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Location Information</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span>{locationData.address}</span>
                  {locationData.verified && (
                    <Badge className="bg-green-100 text-green-800">Verified</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Camera className="w-4 h-4 text-gray-500" />
                  <span>{locationData.photos.length} photos uploaded</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-gray-500" />
                  <span>{locationData.reviews.length} reviews</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Verification Status */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Verification Status</h3>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(verificationStatus).map(([key, status]) => (
                  <div key={key} className="flex items-center gap-2">
                    {status ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    )}
                    <span className={`capitalize ${status ? 'text-green-800' : 'text-red-800'}`}>
                      {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Amenities */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Amenities</h3>
              <div className="flex flex-wrap gap-2">
                {serviceDetails.amenities.map((amenity) => (
                  <Badge key={amenity} variant="outline">{amenity}</Badge>
                ))}
              </div>
            </div>
          </div>
        </Card>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => setCurrentStep('verification')}
            className="flex-1"
          >
            Back
          </Button>
          <Button
            onClick={() => {
              // Submit the application
              // In a real app, this would make an API call
              onComplete();
            }}
            className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
          >
            Submit Application
          </Button>
        </div>
      </div>
    );
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'type-selection':
        return renderTypeSelection();
      case 'location-setup':
        return renderLocationSetup();
      case 'service-details':
        return renderServiceDetails();
      case 'verification':
        return renderVerification();
      case 'review':
        return renderReview();
      default:
        return null;
    }
  };

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            onClick={onBack}
            className="hover:bg-white/30"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-gray-800">Host Onboarding</h1>
          <div className="w-20"></div> {/* Spacer */}
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-gray-600">Progress</span>
            <span className="text-sm text-gray-600">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStepContent()}
        </motion.div>
      </div>
    </div>
  );
}