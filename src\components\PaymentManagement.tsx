import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  CreditCard, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Download,
  Filter,
  Eye,
  Search,
  MoreHorizontal,
  Zap,
  Shield,
  Users,
  Calendar
} from 'lucide-react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Progress } from './ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { 
  LineChart, 
  Line, 
  AreaChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';

interface Transaction {
  id: string;
  type: 'reservation' | 'valet' | 'upgrade' | 'service';
  customer: {
    name: string;
    avatar?: string;
    tier: 'standard' | 'premium' | 'vip';
  };
  amount: number;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  paymentMethod: 'card' | 'digital_wallet' | 'cash';
  timestamp: string;
  reference: string;
  fee: number;
  netAmount: number;
  location?: string;
}

interface PaymentMethod {
  id: string;
  type: 'stripe' | 'square' | 'paypal' | 'apple_pay' | 'google_pay';
  name: string;
  status: 'active' | 'inactive';
  fee: number;
  volume: number;
  icon: string;
}

export function PaymentManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [realTimeData, setRealTimeData] = useState({
    totalRevenue: 28450,
    pendingAmount: 1250,
    successRate: 98.5,
    avgTransactionValue: 67
  });

  // Update real-time data
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeData(prev => ({
        ...prev,
        totalRevenue: prev.totalRevenue + Math.floor(Math.random() * 100),
        pendingAmount: Math.max(0, prev.pendingAmount + Math.floor(Math.random() * 50 - 25))
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Mock transaction data
  const transactions: Transaction[] = [
    {
      id: 'txn_001',
      type: 'reservation',
      customer: {
        name: 'Sarah Mitchell',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b812b8c5?w=150&h=150&fit=crop&crop=face',
        tier: 'premium'
      },
      amount: 85.00,
      status: 'completed',
      paymentMethod: 'card',
      timestamp: '2 min ago',
      reference: 'RES-2024-001',
      fee: 2.55,
      netAmount: 82.45,
      location: 'VIP Table 12'
    },
    {
      id: 'txn_002',
      type: 'valet',
      customer: {
        name: 'Michael Chen',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        tier: 'vip'
      },
      amount: 35.00,
      status: 'pending',
      paymentMethod: 'digital_wallet',
      timestamp: '5 min ago',
      reference: 'VAL-2024-045',
      fee: 1.05,
      netAmount: 33.95,
      location: 'Front Entrance'
    },
    {
      id: 'txn_003',
      type: 'service',
      customer: {
        name: 'Emma Thompson',
        tier: 'standard'
      },
      amount: 45.50,
      status: 'completed',
      paymentMethod: 'card',
      timestamp: '12 min ago',
      reference: 'SVC-2024-123',
      fee: 1.37,
      netAmount: 44.13,
      location: 'Bar Service'
    },
    {
      id: 'txn_004',
      type: 'upgrade',
      customer: {
        name: 'David Kim',
        tier: 'premium'
      },
      amount: 25.00,
      status: 'failed',
      paymentMethod: 'card',
      timestamp: '18 min ago',
      reference: 'UPG-2024-067',
      fee: 0,
      netAmount: 0,
      location: 'Table Upgrade'
    },
    {
      id: 'txn_005',
      type: 'reservation',
      customer: {
        name: 'Lisa Rodriguez',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        tier: 'vip'
      },
      amount: 120.00,
      status: 'completed',
      paymentMethod: 'digital_wallet',
      timestamp: '25 min ago',
      reference: 'RES-2024-002',
      fee: 3.60,
      netAmount: 116.40,
      location: 'Rooftop Section'
    }
  ];

  const paymentMethods: PaymentMethod[] = [
    {
      id: '1',
      type: 'stripe',
      name: 'Stripe',
      status: 'active',
      fee: 2.9,
      volume: 18500,
      icon: '💳'
    },
    {
      id: '2',
      type: 'square',
      name: 'Square',
      status: 'active',
      fee: 2.6,
      volume: 7200,
      icon: '🟫'
    },
    {
      id: '3',
      type: 'apple_pay',
      name: 'Apple Pay',
      status: 'active',
      fee: 2.7,
      volume: 2300,
      icon: '📱'
    },
    {
      id: '4',
      type: 'google_pay',
      name: 'Google Pay',
      status: 'active',
      fee: 2.7,
      volume: 450,
      icon: '🟢'
    }
  ];

  // Chart data
  const revenueData = [
    { time: '12 PM', revenue: 450, transactions: 12 },
    { time: '1 PM', revenue: 680, transactions: 18 },
    { time: '2 PM', revenue: 820, transactions: 24 },
    { time: '3 PM', revenue: 1100, transactions: 31 },
    { time: '4 PM', revenue: 950, transactions: 28 },
    { time: '5 PM', revenue: 1450, transactions: 42 },
    { time: '6 PM', revenue: 1800, transactions: 56 },
    { time: '7 PM', revenue: 2300, transactions: 72 },
    { time: '8 PM', revenue: 2100, transactions: 65 },
    { time: '9 PM', revenue: 1950, transactions: 58 }
  ];

  const paymentMethodData = [
    { name: 'Credit/Debit Cards', value: 65, color: '#8B5CF6' },
    { name: 'Digital Wallets', value: 25, color: '#06B6D4' },
    { name: 'Cash', value: 8, color: '#10B981' },
    { name: 'Other', value: 2, color: '#F59E0B' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'failed': return 'bg-red-100 text-red-800 border-red-300';
      case 'refunded': return 'bg-gray-100 text-gray-800 border-gray-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'refunded': return <RefreshCw className="w-4 h-4 text-gray-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'vip': return 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white';
      case 'premium': return 'bg-gradient-to-r from-purple-500 to-blue-500 text-white';
      case 'standard': return 'bg-gray-200 text-gray-800';
      default: return 'bg-gray-200 text-gray-800';
    }
  };

  const filteredTransactions = transactions.filter(txn => {
    const matchesSearch = txn.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         txn.reference.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || txn.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const totalFees = transactions.reduce((sum, txn) => sum + txn.fee, 0);
  const totalNet = transactions.reduce((sum, txn) => sum + txn.netAmount, 0);

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Payment Management</h1>
            <p className="text-gray-600">Real-time transaction monitoring and financial overview</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white">
              <Zap className="w-4 h-4 mr-2" />
              Auto-Process
            </Button>
          </div>
        </div>

        {/* Real-time Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                <span className="font-medium text-gray-700">Today's Revenue</span>
              </div>
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
                className="w-2 h-2 bg-green-500 rounded-full"
              />
            </div>
            <div className="space-y-1">
              <motion.p 
                key={realTimeData.totalRevenue}
                initial={{ scale: 1.1 }}
                animate={{ scale: 1 }}
                className="text-2xl font-bold text-gray-800"
              >
                ${realTimeData.totalRevenue.toLocaleString()}
              </motion.p>
              <p className="text-sm text-green-600">+12% vs yesterday</p>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-yellow-600" />
                <span className="font-medium text-gray-700">Pending</span>
              </div>
              <Badge className="bg-yellow-100 text-yellow-800 border-0">
                Live
              </Badge>
            </div>
            <div className="space-y-1">
              <motion.p 
                key={realTimeData.pendingAmount}
                initial={{ scale: 1.1 }}
                animate={{ scale: 1 }}
                className="text-2xl font-bold text-gray-800"
              >
                ${realTimeData.pendingAmount.toLocaleString()}
              </motion.p>
              <p className="text-sm text-gray-600">3 transactions</p>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-gray-700">Success Rate</span>
              </div>
              <TrendingUp className="w-4 h-4 text-green-500" />
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-800">{realTimeData.successRate}%</p>
              <p className="text-sm text-green-600">+0.3% this week</p>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-gray-700">Avg Transaction</span>
              </div>
              <TrendingUp className="w-4 h-4 text-green-500" />
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-800">${realTimeData.avgTransactionValue}</p>
              <p className="text-sm text-green-600">+$4 vs last week</p>
            </div>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="transactions" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-white/20 backdrop-blur-sm">
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="methods">Payment Methods</TabsTrigger>
            <TabsTrigger value="reconciliation">Reconciliation</TabsTrigger>
          </TabsList>

          <TabsContent value="transactions" className="space-y-6">
            {/* Search and Filter */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input 
                  placeholder="Search transactions..." 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/50 border-white/60 backdrop-blur-sm"
                />
              </div>
              <Button 
                variant="outline" 
                className="bg-white/20 border-white/30 hover:bg-white/30"
                onClick={() => setFilterStatus(filterStatus === 'all' ? 'completed' : 'all')}
              >
                <Filter className="w-4 h-4 mr-2" />
                {filterStatus === 'all' ? 'All' : 'Completed'}
              </Button>
            </div>

            {/* Transactions List */}
            <div className="space-y-4">
              <AnimatePresence>
                {filteredTransactions.map((transaction) => (
                  <motion.div
                    key={transaction.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="group"
                  >
                    <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6 hover:bg-white/30 transition-all duration-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 flex-1">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={transaction.customer.avatar} />
                            <AvatarFallback>{transaction.customer.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-1">
                              <h3 className="font-semibold text-gray-800">{transaction.customer.name}</h3>
                              <Badge className={getTierColor(transaction.customer.tier)}>
                                {transaction.customer.tier.toUpperCase()}
                              </Badge>
                              <Badge variant="outline" className={getStatusColor(transaction.status)}>
                                <div className="flex items-center gap-1">
                                  {getStatusIcon(transaction.status)}
                                  {transaction.status}
                                </div>
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">Amount</p>
                                <p className="font-semibold text-gray-800">${transaction.amount.toFixed(2)}</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Reference</p>
                                <p className="font-medium text-gray-700">{transaction.reference}</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Location</p>
                                <p className="text-gray-700">{transaction.location}</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Net Amount</p>
                                <p className="font-semibold text-green-700">${transaction.netAmount.toFixed(2)}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <p className="text-2xl font-bold text-gray-800">${transaction.amount.toFixed(2)}</p>
                            <p className="text-sm text-gray-600">{transaction.timestamp}</p>
                          </div>
                          <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Chart */}
              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Today's Revenue Trend</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                    <XAxis dataKey="time" stroke="#6B7280" />
                    <YAxis stroke="#6B7280" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(255,255,255,0.9)', 
                        border: 'none', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                      }} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="revenue" 
                      stroke="#10B981" 
                      fill="url(#revenueGradient)" 
                      strokeWidth={2}
                    />
                    <defs>
                      <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#10B981" stopOpacity={0.6} />
                        <stop offset="100%" stopColor="#10B981" stopOpacity={0.1} />
                      </linearGradient>
                    </defs>
                  </AreaChart>
                </ResponsiveContainer>
              </Card>

              {/* Payment Methods Breakdown */}
              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Payment Method Distribution</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={paymentMethodData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {paymentMethodData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      formatter={(value) => [`${value}%`, 'Percentage']}
                      contentStyle={{ 
                        backgroundColor: 'rgba(255,255,255,0.9)', 
                        border: 'none', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                      }} 
                    />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </div>

            {/* Financial Summary */}
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Financial Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-white/30 rounded-lg">
                  <p className="text-3xl font-bold text-gray-800">${totalNet.toFixed(2)}</p>
                  <p className="text-gray-600">Net Revenue</p>
                </div>
                <div className="text-center p-4 bg-white/30 rounded-lg">
                  <p className="text-3xl font-bold text-gray-800">${totalFees.toFixed(2)}</p>
                  <p className="text-gray-600">Processing Fees</p>
                </div>
                <div className="text-center p-4 bg-white/30 rounded-lg">
                  <p className="text-3xl font-bold text-gray-800">{((totalFees / (totalNet + totalFees)) * 100).toFixed(1)}%</p>
                  <p className="text-gray-600">Fee Percentage</p>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="methods" className="space-y-6">
            {/* Payment Methods Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {paymentMethods.map((method) => (
                <Card key={method.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{method.icon}</div>
                      <div>
                        <h3 className="font-semibold text-gray-800">{method.name}</h3>
                        <p className="text-sm text-gray-600">{method.fee}% processing fee</p>
                      </div>
                    </div>
                    <Badge 
                      variant="outline" 
                      className={method.status === 'active' 
                        ? 'bg-green-100 text-green-800 border-green-300' 
                        : 'bg-gray-100 text-gray-800 border-gray-300'
                      }
                    >
                      {method.status}
                    </Badge>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-600">Volume This Month</span>
                        <span className="font-medium text-gray-800">${method.volume.toLocaleString()}</span>
                      </div>
                      <Progress value={(method.volume / 20000) * 100} className="h-2" />
                    </div>
                    
                    <div className="flex justify-between items-center pt-2 border-t border-white/30">
                      <span className="text-sm text-gray-600">Est. Monthly Fees:</span>
                      <span className="font-semibold text-gray-800">
                        ${((method.volume * method.fee) / 100).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reconciliation" className="space-y-6">
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800">Daily Reconciliation</h3>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reconcile Now
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="p-4 bg-white/30 rounded-lg text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-gray-800">Matched</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-800">127</p>
                  <p className="text-sm text-gray-600">transactions</p>
                </div>
                
                <div className="p-4 bg-white/30 rounded-lg text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <AlertTriangle className="w-5 h-5 text-yellow-600" />
                    <span className="font-medium text-gray-800">Pending</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-800">3</p>
                  <p className="text-sm text-gray-600">transactions</p>
                </div>
                
                <div className="p-4 bg-white/30 rounded-lg text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <XCircle className="w-5 h-5 text-red-600" />
                    <span className="font-medium text-gray-800">Discrepancies</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-800">0</p>
                  <p className="text-sm text-gray-600">found</p>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-green-50/60 rounded-lg border border-green-200/60">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-800">All Clear</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  All transactions have been successfully reconciled for today.
                </p>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}