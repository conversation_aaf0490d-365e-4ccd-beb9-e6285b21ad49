# Bytspot Mobile Social Sharing & Analytics Setup Guide

This guide walks you through setting up real analytics tracking, testing mobile social sharing flows, and implementing push notification service workers.

## 🚀 Quick Start

### 1. Environment Setup

Copy the example environment file and add your API keys:

```bash
cp .env.example .env
```

Fill in your actual API keys in `.env`:

```env
# Analytics Services
VITE_GA_MEASUREMENT_ID=G-YOUR_ACTUAL_ID
VITE_MIXPANEL_TOKEN=your_mixpanel_token
VITE_AMPLITUDE_API_KEY=your_amplitude_key

# Error Tracking  
VITE_SENTRY_DSN=https://<EMAIL>/project

# Push Notifications
VITE_VAPID_PUBLIC_KEY=your_vapid_public_key
VITE_PUSH_SUBSCRIPTION_ENDPOINT=https://your-api.com/push/subscribe
```

### 2. Testing Mobile Sharing

Visit `/app/test-sharing` to access the comprehensive mobile sharing test suite:

```
https://your-domain.com/app/test-sharing
```

This test page will:
- Detect your device capabilities
- Test each social platform individually  
- Show deep link compatibility
- Verify clipboard and notification features
- Provide detailed debug information

## 📱 Mobile Testing Instructions

### On Your Mobile Device:

1. **Open the test page** on your actual mobile device
2. **Test each platform**:
   - Instagram Story/Post
   - TikTok 
   - Twitter/X
   - Native sharing
3. **Verify behaviors**:
   - Deep links open correct apps
   - Content copies to clipboard as fallback
   - Instructions appear when needed
   - Haptic feedback works (vibration)

### Expected Results by Platform:

**Instagram (Mobile)**:
- Attempts to open Instagram app
- Copies content to clipboard
- Shows sharing instructions
- Provides haptic feedback

**TikTok (Mobile)**:
- Tries TikTok deep link
- Falls back to clipboard + instructions
- Haptic feedback on interaction

**Twitter/X**:
- Opens Twitter web intent or app
- Works on all devices
- No special mobile handling needed

**Native Share (Mobile)**:
- Uses device's built-in sharing
- Available when not in in-app browsers
- Best user experience on mobile

## 🔧 Analytics Implementation

### Supported Platforms:

1. **Google Analytics 4**
   - Page views
   - Custom events  
   - User identification
   - Performance tracking

2. **Mixpanel**
   - Event tracking
   - User profiles
   - Funnel analysis

3. **Amplitude**
   - User behavior analytics
   - Retention analysis
   - Custom properties

### Key Events Tracked:

```javascript
// Social sharing events
analyticsService.track('social_share_initiated', {
  platform: 'instagram_story',
  venue: 'Rooftop Lounge', 
  vibe_score: 9,
  has_image: true
});

// Notification events
analyticsService.track('notification_shown', {
  type: 'friend_vibe_score',
  notification_id: 'friend_vibe_123'
});

// Device capability tests
analyticsService.track('sharing_capabilities_tested', {
  nativeShare: true,
  clipboard: true, 
  deepLinks: { instagram: true, tiktok: true }
});
```

## 🔔 Push Notifications Setup

### Service Worker Features:

- **Background notifications** with action buttons
- **Offline caching** for core app functionality  
- **Background sync** for offline actions
- **Install prompts** for PWA installation

### Testing Push Notifications:

1. **Grant permission** when prompted
2. **Test notification types**:
   - Friend activity
   - Achievement unlocks
   - Challenge progress
   - Venue recommendations

3. **Verify behaviors**:
   - Notifications appear even when app is closed
   - Action buttons work correctly
   - Clicking opens correct app sections

## 🌐 Real API Integration

### Setting Up Analytics APIs:

#### Google Analytics:
1. Create GA4 property
2. Get Measurement ID (`G-XXXXXXXXXX`)
3. Add to `VITE_GA_MEASUREMENT_ID`

#### Mixpanel:
1. Create Mixpanel project
2. Get project token
3. Add to `VITE_MIXPANEL_TOKEN`

#### Amplitude:
1. Create Amplitude project  
2. Get API key
3. Add to `VITE_AMPLITUDE_API_KEY`

### Setting Up Push Notifications:

#### Generate VAPID Keys:
```bash
npm install -g web-push
web-push generate-vapid-keys
```

#### Backend Endpoint:
Create an endpoint to handle push subscriptions:

```javascript
// POST /push/subscribe
app.post('/push/subscribe', (req, res) => {
  const { subscription, userAgent, deviceInfo } = req.body;
  
  // Store subscription in database
  await saveSubscription(subscription, deviceInfo);
  
  res.status(200).json({ success: true });
});
```

## 🧪 Testing Checklist

### Desktop Testing:
- [ ] Analytics events fire correctly
- [ ] Error tracking captures issues
- [ ] Social sharing opens popups
- [ ] Clipboard fallbacks work

### Mobile Testing:
- [ ] Device detection accurate
- [ ] Deep links attempt app opening
- [ ] Clipboard copying works
- [ ] Haptic feedback functions
- [ ] Native sharing available
- [ ] PWA install prompt appears
- [ ] Push notifications work
- [ ] Service worker registers

### Cross-Platform:
- [ ] Instagram sharing flow
- [ ] TikTok sharing flow  
- [ ] Twitter sharing works
- [ ] Native sharing (mobile)
- [ ] Notification permissions
- [ ] Analytics across devices

## 🚨 Troubleshooting

### Common Issues:

**Deep Links Not Working:**
- Check if apps are installed
- Verify URL schemes are correct
- Test on actual devices, not emulators

**Analytics Not Tracking:**
- Verify API keys are correct
- Check network requests in dev tools
- Ensure environment variables are loaded

**Notifications Not Appearing:**
- Check permission status
- Verify service worker registration
- Test with HTTPS (required for push)

**PWA Not Installing:**
- Ensure HTTPS is enabled
- Verify manifest.json is accessible
- Check service worker is registered

### Debug Tools:

1. **Mobile Sharing Test**: `/app/test-sharing`
2. **Browser DevTools**: Application tab for PWA features
3. **Analytics Debug**: Check network requests
4. **Service Worker**: Application > Service Workers in DevTools

## 📊 Performance Monitoring

The analytics service automatically tracks:

- **Core Web Vitals** (LCP, FID, CLS)
- **API response times**
- **Page load performance**
- **Custom metrics**

Monitor these in your analytics dashboards to optimize the mobile experience.

## 🔒 Privacy & Security

- All analytics respect user privacy
- Push subscriptions stored securely
- No PII collected without consent
- Error tracking filters sensitive data
- Social sharing respects platform policies

## 🚀 Deployment

### Production Checklist:

- [ ] Replace all placeholder API keys
- [ ] Set up real backend endpoints
- [ ] Configure HTTPS for push notifications
- [ ] Test on multiple mobile devices
- [ ] Verify analytics in production
- [ ] Set up error monitoring alerts
- [ ] Test PWA installation flow

### Performance Optimization:

- Service worker caches static assets
- Analytics loaded asynchronously
- Social sharing optimized for mobile
- Push notifications use background sync
- Offline functionality implemented

---

This setup provides comprehensive mobile social sharing with real analytics tracking and push notifications. Test thoroughly on actual devices for the best results! 🎉