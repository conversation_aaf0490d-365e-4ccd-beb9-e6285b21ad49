import React from 'react';
import { isServiceReady } from '../lib/services/serviceManager';

export function ServiceStatus() {
  const errorServiceReady = isServiceReady('error');
  const analyticsServiceReady = isServiceReady('analytics');
  const storeServiceReady = isServiceReady('store');

  return (
    <div className="fixed bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 border text-sm">
      <h4 className="font-medium mb-2">Service Status</h4>
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${errorServiceReady ? 'bg-green-500' : 'bg-red-500'}`} />
          <span>Error Service</span>
        </div>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${analyticsServiceReady ? 'bg-green-500' : 'bg-red-500'}`} />
          <span>Analytics Service</span>
        </div>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${storeServiceReady ? 'bg-green-500' : 'bg-red-500'}`} />
          <span>Store Service</span>
        </div>
      </div>
    </div>
  );
}