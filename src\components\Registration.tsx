import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  MapPin, 
  Wifi, 
  Bluetooth, 
  Camera, 
  Bell, 
  Check, 
  ArrowRight, 
  Shield,
  Car,
  Wine,
  DollarSign,
  Clock
} from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Switch } from './ui/switch';
import { Badge } from './ui/badge';

interface RegistrationProps {
  address: string;
  onComplete: (preferences: any, permissions: any) => void;
  profileType?: 'individual' | 'business';
  context?: 'host' | 'feature' | 'search';
  selectedFeature?: string;
}

const steps = [
  {
    id: 'preferences',
    title: 'Your Preferences',
    description: 'Help us personalize your matches',
    icon: Shield,
  },
  {
    id: 'permissions',
    title: 'Permissions',
    description: 'Enable features for better experience',
    icon: Shield,
  }
];

const preferenceOptions = {
  parkingType: [
    { id: 'covered', label: 'Covered Parking', icon: '🏢' },
    { id: 'street', label: 'Street Parking', icon: '🛣️' },
    { id: 'valet', label: 'Valet Service', icon: '🔑' },
    { id: 'any', label: 'Any Available', icon: '🚗' },
  ],
  venueStyle: [
    { id: 'upscale', label: 'Upscale & Trendy', icon: '✨' },
    { id: 'casual', label: 'Casual & Relaxed', icon: '😊' },
    { id: 'nightlife', label: 'Nightlife & Bars', icon: '🍸' },
    { id: 'cultural', label: 'Cultural & Arts', icon: '🎭' },
  ],
  budget: [
    { id: 'budget', label: '$10-30', icon: '💚' },
    { id: 'moderate', label: '$30-60', icon: '💛' },
    { id: 'premium', label: '$60+', icon: '💜' },
  ],
  distance: [
    { id: 'walking', label: 'Walking (5 min)', icon: '🚶' },
    { id: 'short', label: 'Short Drive (10 min)', icon: '🚗' },
    { id: 'anywhere', label: 'Anywhere', icon: '🌍' },
  ],
};

const permissions = [
  {
    id: 'gps',
    icon: MapPin,
    title: 'Location Services',
    description: 'Find nearby spots and venues',
    required: true,
  }
];

export function Registration({ address, onComplete, profileType, context, selectedFeature }: RegistrationProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [preferences, setPreferences] = useState<{
    parkingType?: string;
    venueStyle?: string;
    budget?: string;
    distance?: string;
  }>({});
  
  const [permissionStates, setPermissionStates] = useState(
    permissions.reduce((acc, permission) => ({
      ...acc,
      [permission.id]: permission.required,
    }), {} as Record<string, boolean>)
  );
  
  const [isCompleting, setIsCompleting] = useState(false);
  const currentStepData = steps[currentStep];

  const handlePreferenceSelect = (category: string, value: string) => {
    setPreferences(prev => ({
      ...prev,
      [category]: prev[category as keyof typeof prev] === value ? undefined : value,
    }));
  };

  const handlePermissionToggle = (permissionId: string, value: boolean) => {
    setPermissionStates(prev => ({
      ...prev,
      [permissionId]: value,
    }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      setIsCompleting(true);
      setTimeout(() => {
        onComplete(preferences, permissionStates as any);
      }, 1500);
    }
  };

  const canProceed = () => {
    if (currentStep === 0) {
      // At least one preference should be selected
      return Object.keys(preferences).length > 0;
    } else {
      // GPS must be enabled for permissions step
      return permissionStates.gps;
    }
  };

  const renderPreferencesStep = () => (
    <div className="space-y-6">
      {Object.entries(preferenceOptions).map(([category, options]) => (
        <div key={category} className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800 capitalize flex items-center gap-2">
            {category === 'parkingType' && <Car className="w-5 h-5" />}
            {category === 'venueStyle' && <Wine className="w-5 h-5" />}
            {category === 'budget' && <DollarSign className="w-5 h-5" />}
            {category === 'distance' && <Clock className="w-5 h-5" />}
            {category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </h3>
          
          <div className="grid grid-cols-2 gap-3">
            {options.map((option) => (
              <motion.button
                key={option.id}
                onClick={() => handlePreferenceSelect(category, option.id)}
                className={`
                  p-4 rounded-2xl border-2 transition-all duration-200 text-left
                  ${preferences[category as keyof typeof preferences] === option.id
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-white/30 bg-white/20 hover:bg-white/30'
                  }
                `}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{option.icon}</span>
                  <span className="font-medium text-gray-800">{option.label}</span>
                </div>
              </motion.button>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  const renderPermissionsStep = () => (
    <div className="space-y-4">
      {permissions.map((permission) => {
        const Icon = permission.icon;
        return (
          <div
            key={permission.id}
            className="bg-white/20 backdrop-blur-sm rounded-2xl p-4 border border-white/30"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className={`
                  w-12 h-12 rounded-xl flex items-center justify-center
                  ${permission.required ? 'bg-purple-500' : 'bg-gray-400'}
                `}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 flex items-center gap-2">
                    {permission.title}
                    {permission.required && (
                      <Badge variant="outline" className="bg-red-100 border-red-300 text-red-700 text-xs">
                        Required
                      </Badge>
                    )}
                  </h4>
                  <p className="text-sm text-gray-600">{permission.description}</p>
                </div>
              </div>
              
              <Switch
                checked={permissionStates[permission.id]}
                onCheckedChange={(checked) => 
                  handlePermissionToggle(permission.id, checked)
                }
                disabled={permission.required}
                className="data-[state=checked]:bg-purple-500"
              />
            </div>
          </div>
        );
      })}
    </div>
  );

  return (
    <div className="h-full flex flex-col p-6 relative overflow-hidden bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-purple-400/30 to-pink-400/30 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      </div>

      {/* Header */}
      <motion.div
        className="relative z-10 mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Setup Your Preferences
          </h1>
          <p className="text-gray-600">Destination: {address}</p>
        </div>
        
        <div className="flex justify-center gap-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index <= currentStep ? 'bg-purple-500' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </motion.div>

      {/* Content */}
      <div className="flex-1 relative z-10 overflow-y-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            className="backdrop-blur-xl bg-white/30 rounded-3xl border border-white/40 p-6"
          >
            <div className="text-center mb-6">
              <h2 className="text-xl font-bold text-gray-800 mb-2">
                {currentStepData.title}
              </h2>
              <p className="text-gray-600">{currentStepData.description}</p>
            </div>

            {currentStep === 0 ? renderPreferencesStep() : renderPermissionsStep()}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Bottom Actions */}
      <motion.div
        className="relative z-10 mt-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Button
          onClick={handleNext}
          disabled={!canProceed() || isCompleting}
          className="w-full py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 rounded-2xl shadow-xl h-14"
        >
          <div className="flex items-center justify-center gap-3">
            {isCompleting ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Shield className="w-5 h-5" />
              </motion.div>
            ) : currentStep === steps.length - 1 ? (
              <Check className="w-5 h-5" />
            ) : (
              <ArrowRight className="w-5 h-5" />
            )}
            <span className="font-semibold">
              {isCompleting 
                ? 'Setting up your matches...' 
                : currentStep === steps.length - 1 
                  ? 'Start Matching' 
                  : 'Continue'
              }
            </span>
          </div>
        </Button>

        <p className="text-center text-sm text-gray-500 mt-3">
          Step {currentStep + 1} of {steps.length}
        </p>
      </motion.div>
    </div>
  );
}