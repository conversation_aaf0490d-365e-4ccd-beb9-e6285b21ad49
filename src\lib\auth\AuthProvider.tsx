import React, { createContext, useContext, useEffect, useReducer, useCallback } from 'react';
import { User, AuthState, LoginCredentials, RegisterCredentials } from './types';
import { authService } from './authService';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  hasPermission: (resource: string, action: string) => boolean;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: User }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'CLEAR_ERROR' };

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'AUTH_LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
}

const initialState: AuthState = {
  user: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      dispatch({ type: 'AUTH_START' });
      const response = await authService.login(credentials);
      dispatch({ type: 'AUTH_SUCCESS', payload: response.user });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, []);

  const register = useCallback(async (credentials: RegisterCredentials) => {
    try {
      dispatch({ type: 'AUTH_START' });
      const response = await authService.register(credentials);
      dispatch({ type: 'AUTH_SUCCESS', payload: response.user });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Registration failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await authService.logout();
      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails on server, clear local state
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  }, []);

  const refreshUser = useCallback(async () => {
    try {
      dispatch({ type: 'AUTH_START' });
      const user = await authService.getCurrentUser();
      if (user) {
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } else {
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to refresh user';
      dispatch({ type: 'AUTH_ERROR', payload: message });
    }
  }, []);

  const hasPermission = useCallback((resource: string, action: string): boolean => {
    if (!state.user) return false;
    
    // Check if user has specific permission
    return state.user.permissions.some(
      permission => permission.resource === resource && permission.actions.includes(action)
    );
  }, [state.user]);

  const hasRole = useCallback((role: string): boolean => {
    return state.user?.role === role;
  }, [state.user]);

  // Initialize auth state on mount
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        console.log('AuthProvider: Initializing auth...');
        if (authService.isAuthenticated()) {
          console.log('AuthProvider: User is authenticated, getting current user...');
          const user = await authService.getCurrentUser();
          if (mounted && user) {
            console.log('AuthProvider: User found, dispatching AUTH_SUCCESS');
            dispatch({ type: 'AUTH_SUCCESS', payload: user });
          } else if (mounted) {
            console.log('AuthProvider: No user found, dispatching AUTH_LOGOUT');
            dispatch({ type: 'AUTH_LOGOUT' });
          }
        } else {
          console.log('AuthProvider: User not authenticated, dispatching AUTH_LOGOUT');
          if (mounted) {
            dispatch({ type: 'AUTH_LOGOUT' });
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          dispatch({ type: 'AUTH_LOGOUT' });
        }
      }
    };

    // Add a small delay to ensure all providers are ready
    const timer = setTimeout(() => {
      initializeAuth();
    }, 100);

    // Fallback timeout to prevent infinite loading
    const fallbackTimer = setTimeout(() => {
      if (mounted) {
        console.warn('Auth initialization timeout, falling back to logged out state');
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    }, 3000);

    return () => {
      mounted = false;
      clearTimeout(timer);
      clearTimeout(fallbackTimer);
    };
  }, []); // Empty dependency array - only run once

  // Clear error after 5 seconds
  useEffect(() => {
    if (state.error) {
      const timer = setTimeout(() => {
        dispatch({ type: 'CLEAR_ERROR' });
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [state.error]);

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshUser,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Safe version of useAuth that doesn't throw errors
export function useAuthSafe() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    // Return loading state instead of throwing error
    return {
      isAuthenticated: false,
      isLoading: true,
      user: null,
      error: null,
      contextReady: false,
      login: async () => {
        throw new Error('Auth context not ready');
      },
      register: async () => {
        throw new Error('Auth context not ready');
      },
      logout: async () => {
        throw new Error('Auth context not ready');
      },
      refreshUser: async () => {
        throw new Error('Auth context not ready');
      },
      hasPermission: () => false,
      hasRole: () => false,
    };
  }
  
  return {
    ...context,
    contextReady: true,
  };
}