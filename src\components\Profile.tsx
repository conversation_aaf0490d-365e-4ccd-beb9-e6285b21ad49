import { useState } from 'react';
import { motion } from 'motion/react';
import { 
  ArrowLeft, 
  User, 
  Star, 
  CreditCard, 
  Bell, 
  Shield, 
  HelpCircle, 
  LogOut,
  Edit3,
  ChevronRight,
  Settings,
  Crown,
  Gift,
  MapPin,
  Phone,
  Mail,
  Camera,
  Wallet,
  History,
  Award,
  Smartphone,
  Heart,
  Sparkles,
  Vibrate,
  Wifi,
  Bluetooth,
  Compass,
  ScanLine,
  CheckCircle,
  Activity,
  Cpu,
  Radio,
  Satellite
} from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Switch } from './ui/switch';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Separator } from './ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';

interface ProfileProps {
  onBack: () => void;
}

interface UserProfile {
  name: string;
  email: string;
  phone: string;
  location: string;
  avatar: string;
  memberSince: string;
  pointsBalance: number;
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  totalEarned: number;
  toNextTier: number;
}

interface PaymentMethod {
  id: string;
  type: 'card' | 'digital_wallet';
  name: string;
  last4?: string;
  brand?: string;
  isDefault: boolean;
}

interface NotificationSettings {
  bookingConfirmations: boolean;
  promotions: boolean;
  pointsUpdates: boolean;
  nearbyOffers: boolean;
  weeklyDigest: boolean;
}

export function Profile({ onBack }: ProfileProps) {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  
  // Mock user data
  const [userProfile, setUserProfile] = useState<UserProfile>({
    name: 'Alex Chen',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    memberSince: 'January 2024',
    pointsBalance: 2450,
    tier: 'Gold',
    totalEarned: 15240,
    toNextTier: 760
  });

  const [paymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'card',
      name: 'Chase Sapphire',
      last4: '4242',
      brand: 'Visa',
      isDefault: true
    },
    {
      id: '2',
      type: 'card',
      name: 'Amex Gold',
      last4: '8431',
      brand: 'Amex',
      isDefault: false
    },
    {
      id: '3',
      type: 'digital_wallet',
      name: 'Apple Pay',
      isDefault: false
    }
  ]);

  const [notifications, setNotifications] = useState<NotificationSettings>({
    bookingConfirmations: true,
    promotions: true,
    pointsUpdates: true,
    nearbyOffers: false,
    weeklyDigest: true
  });

  const [permissions, setPermissions] = useState({
    location: true,
    wifi: true,
    bluetooth: true,
    camera: false,
    notifications: true,
    imu: true
  });

  const [appSettings, setAppSettings] = useState({
    hapticFeedback: true,
    darkMode: false,
    autoSync: true,
    dataCollection: true,
    analytics: true
  });

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'bg-gradient-to-r from-amber-600 to-amber-500';
      case 'Silver': return 'bg-gradient-to-r from-gray-400 to-gray-500';
      case 'Gold': return 'bg-gradient-to-r from-yellow-400 to-yellow-500';
      case 'Platinum': return 'bg-gradient-to-r from-purple-400 to-purple-500';
      default: return 'bg-gradient-to-r from-gray-400 to-gray-500';
    }
  };

  const tierProgress = ((userProfile.totalEarned % 2500) / 2500) * 100;

  const recentActivity = [
    { date: '2024-01-20', action: 'Valet Service', points: '+250', location: 'Downtown' },
    { date: '2024-01-18', action: 'Parking Reserved', points: '+120', location: 'Financial District' },
    { date: '2024-01-15', action: 'VIP Table', points: '+340', location: 'Rooftop Lounge' },
    { date: '2024-01-12', action: 'Points Redeemed', points: '-500', location: 'Various' }
  ];

  const handleProfileUpdate = (field: keyof UserProfile, value: string) => {
    setUserProfile(prev => ({ ...prev, [field]: value }));
  };

  const handleNotificationToggle = (setting: keyof NotificationSettings) => {
    setNotifications(prev => ({ ...prev, [setting]: !prev[setting] }));
  };

  const handlePermissionToggle = (permission: string) => {
    setPermissions(prev => ({ ...prev, [permission]: !prev[permission as keyof typeof prev] }));
  };

  const handleSettingToggle = (setting: string) => {
    setAppSettings(prev => ({ ...prev, [setting]: !prev[setting as keyof typeof prev] }));
  };

  const renderProfileHeader = () => (
    <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6 mb-6">
      <div className="flex items-center gap-4 mb-4">
        <div className="relative">
          <Avatar className="w-20 h-20">
            <AvatarImage src={userProfile.avatar} alt={userProfile.name} />
            <AvatarFallback>{userProfile.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          <Button
            size="sm"
            className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full bg-white/80 hover:bg-white/90 p-0"
            onClick={() => setIsEditingProfile(true)}
          >
            <Camera className="w-4 h-4 text-gray-700" />
          </Button>
        </div>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h2 className="text-xl font-semibold text-gray-800">{userProfile.name}</h2>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsEditingProfile(true)}
              className="w-8 h-8 p-0 hover:bg-white/30"
            >
              <Edit3 className="w-4 h-4" />
            </Button>
          </div>
          <p className="text-gray-600 text-sm">{userProfile.email}</p>
          <p className="text-gray-500 text-xs">Member since {userProfile.memberSince}</p>
        </div>
      </div>

      {/* Bytspot Points Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${getTierColor(userProfile.tier)} text-white`}>
              <Crown className="w-5 h-5" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">Bytspot Points</h3>
              <p className="text-sm text-gray-600">{userProfile.tier} Member</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-gray-800">{userProfile.pointsBalance.toLocaleString()}</p>
            <p className="text-xs text-gray-600">points available</p>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progress to {userProfile.tier === 'Gold' ? 'Platinum' : 'next tier'}</span>
            <span className="text-gray-800 font-medium">{userProfile.toNextTier} points to go</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${getTierColor(userProfile.tier)}`}
              style={{ width: `${(tierProgress)}%` }}
            />
          </div>
        </div>
      </div>
    </Card>
  );

  const renderQuickActions = () => (
    <div className="grid grid-cols-4 gap-3 mb-6">
      <Button
        variant="outline"
        className="h-20 flex-col gap-2 backdrop-blur-xl bg-white/20 border-white/30 hover:bg-white/30"
        onClick={() => setActiveSection('payment')}
      >
        <Wallet className="w-5 h-5" />
        <span className="text-xs">Payment</span>
      </Button>
      
      <Button
        variant="outline"
        className="h-20 flex-col gap-2 backdrop-blur-xl bg-white/20 border-white/30 hover:bg-white/30"
        onClick={() => setActiveSection('history')}
      >
        <History className="w-5 h-5" />
        <span className="text-xs">History</span>
      </Button>
      
      <Button
        variant="outline"
        className="h-20 flex-col gap-2 backdrop-blur-xl bg-white/20 border-white/30 hover:bg-white/30"
        onClick={() => setActiveSection('rewards')}
      >
        <Gift className="w-5 h-5" />
        <span className="text-xs">Rewards</span>
      </Button>
      
      <Button
        variant="outline"
        className="h-20 flex-col gap-2 backdrop-blur-xl bg-white/20 border-white/30 hover:bg-white/30"
        onClick={() => setActiveSection('help')}
      >
        <HelpCircle className="w-5 h-5" />
        <span className="text-xs">Help</span>
      </Button>
    </div>
  );

  const renderMenuSection = (title: string, items: Array<{
    icon: React.ReactNode;
    label: string;
    action: () => void;
    badge?: string;
  }>) => (
    <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4 mb-4">
      <h3 className="font-semibold text-gray-800 mb-3">{title}</h3>
      <div className="space-y-1">
        {items.map((item, index) => (
          <button
            key={index}
            onClick={item.action}
            className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-white/30 transition-colors"
          >
            <div className="flex items-center gap-3">
              {item.icon}
              <span className="text-gray-700">{item.label}</span>
            </div>
            <div className="flex items-center gap-2">
              {item.badge && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">
                  {item.badge}
                </Badge>
              )}
              <ChevronRight className="w-4 h-4 text-gray-400" />
            </div>
          </button>
        ))}
      </div>
    </Card>
  );

  const renderEditProfileDialog = () => (
    <Dialog open={isEditingProfile} onOpenChange={setIsEditingProfile}>
      <DialogContent className="backdrop-blur-xl bg-white/90 border-white/30">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label>Full Name</Label>
            <Input
              value={userProfile.name}
              onChange={(e) => handleProfileUpdate('name', e.target.value)}
              className="mt-1"
            />
          </div>
          <div>
            <Label>Email</Label>
            <Input
              value={userProfile.email}
              onChange={(e) => handleProfileUpdate('email', e.target.value)}
              className="mt-1"
            />
          </div>
          <div>
            <Label>Phone</Label>
            <Input
              value={userProfile.phone}
              onChange={(e) => handleProfileUpdate('phone', e.target.value)}
              className="mt-1"
            />
          </div>
          <div>
            <Label>Location</Label>
            <Input
              value={userProfile.location}
              onChange={(e) => handleProfileUpdate('location', e.target.value)}
              className="mt-1"
            />
          </div>
          <div className="flex gap-2 pt-4">
            <Button 
              variant="outline" 
              onClick={() => setIsEditingProfile(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button 
              onClick={() => setIsEditingProfile(false)}
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  const renderDetailView = () => {
    switch (activeSection) {
      case 'payment':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveSection(null)}
                className="hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <h2 className="text-xl font-semibold">Payment Methods</h2>
            </div>

            {paymentMethods.map((method) => (
              <Card key={method.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/30 rounded-lg flex items-center justify-center">
                    {method.type === 'card' ? <CreditCard className="w-5 h-5" /> : <Smartphone className="w-5 h-5" />}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{method.name}</p>
                    {method.last4 && (
                      <p className="text-sm text-gray-600">•••• •••• •••• {method.last4}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {method.isDefault && (
                      <Badge className="bg-green-100 text-green-800">Default</Badge>
                    )}
                    {method.brand && (
                      <Badge variant="outline">{method.brand}</Badge>
                    )}
                  </div>
                </div>
              </Card>
            ))}

            <Button className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
              Add Payment Method
            </Button>
          </div>
        );

      case 'history':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveSection(null)}
                className="hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <h2 className="text-xl font-semibold">Activity History</h2>
            </div>

            {recentActivity.map((activity, index) => (
              <Card key={index} className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{activity.action}</p>
                    <p className="text-sm text-gray-600">{activity.location}</p>
                    <p className="text-xs text-gray-500">{activity.date}</p>
                  </div>
                  <div className={`font-semibold ${activity.points.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {activity.points} pts
                  </div>
                </div>
              </Card>
            ))}
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveSection(null)}
                className="hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <h2 className="text-xl font-semibold">Notifications</h2>
            </div>

            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Booking Confirmations</p>
                    <p className="text-sm text-gray-600">Get notified when bookings are confirmed</p>
                  </div>
                  <Switch
                    checked={notifications.bookingConfirmations}
                    onCheckedChange={() => handleNotificationToggle('bookingConfirmations')}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Promotions & Offers</p>
                    <p className="text-sm text-gray-600">Receive special deals and promotions</p>
                  </div>
                  <Switch
                    checked={notifications.promotions}
                    onCheckedChange={() => handleNotificationToggle('promotions')}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Points Updates</p>
                    <p className="text-sm text-gray-600">Get notified when you earn or redeem points</p>
                  </div>
                  <Switch
                    checked={notifications.pointsUpdates}
                    onCheckedChange={() => handleNotificationToggle('pointsUpdates')}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Nearby Offers</p>
                    <p className="text-sm text-gray-600">Location-based recommendations</p>
                  </div>
                  <Switch
                    checked={notifications.nearbyOffers}
                    onCheckedChange={() => handleNotificationToggle('nearbyOffers')}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Weekly Digest</p>
                    <p className="text-sm text-gray-600">Summary of your weekly activity</p>
                  </div>
                  <Switch
                    checked={notifications.weeklyDigest}
                    onCheckedChange={() => handleNotificationToggle('weeklyDigest')}
                  />
                </div>
              </div>
            </Card>
          </div>
        );

      case 'vibe-preferences':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveSection(null)}
                className="hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <h2 className="text-xl font-semibold">Vibe Preferences</h2>
            </div>

            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">Premium Feature</h3>
                  <p className="text-gray-600 text-sm">
                    Customize your venue discovery preferences with AI-powered matching
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 py-4">
                  {[
                    { icon: Sparkles, text: 'Smart Matching' },
                    { icon: Heart, text: 'Mood Learning' },
                    { icon: Crown, text: 'Exclusive Access' },
                    { icon: Settings, text: 'Custom Filters' }
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-left">
                      <feature.icon className="w-4 h-4 text-purple-600" />
                      <span className="text-sm text-gray-700">{feature.text}</span>
                    </div>
                  ))}
                </div>

                <div className="pt-4">
                  <p className="text-xs text-gray-500 mb-4">
                    Access premium vibe preferences from the Vibe tab
                  </p>
                  <Button 
                    className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                    onClick={() => {
                      // Navigate to VenueInsider with premium flow
                      setActiveSection(null);
                      onBack(); // Go back to main app, user can then click Vibe
                    }}
                  >
                    <Crown className="w-4 h-4 mr-2" />
                    Go to Vibe Preferences
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        );

      case 'permissions':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveSection(null)}
                className="hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <h2 className="text-xl font-semibold">Permissions</h2>
            </div>

            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4 mb-4">
              <div className="space-y-2 mb-4">
                <h3 className="font-semibold text-gray-800">Enable features for better experience</h3>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Compass className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">Location Services</p>
                      <p className="text-sm text-gray-600">Required</p>
                      <p className="text-xs text-gray-500">Find nearby spots and venues</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <Switch
                      checked={permissions.location}
                      onCheckedChange={() => handlePermissionToggle('location')}
                    />
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <Wifi className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">WiFi Access</p>
                      <p className="text-xs text-gray-500">Improve location accuracy</p>
                    </div>
                  </div>
                  <Switch
                    checked={permissions.wifi}
                    onCheckedChange={() => handlePermissionToggle('wifi')}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Bluetooth className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">Bluetooth</p>
                      <p className="text-xs text-gray-500">Connect with venue beacons</p>
                    </div>
                  </div>
                  <Switch
                    checked={permissions.bluetooth}
                    onCheckedChange={() => handlePermissionToggle('bluetooth')}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Camera className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="font-medium">Camera Access</p>
                      <p className="text-xs text-gray-500">Scan QR codes and take photos</p>
                    </div>
                  </div>
                  <Switch
                    checked={permissions.camera}
                    onCheckedChange={() => handlePermissionToggle('camera')}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Bell className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium">Push Notifications</p>
                      <p className="text-xs text-gray-500">Get updates about matches</p>
                    </div>
                  </div>
                  <Switch
                    checked={permissions.notifications}
                    onCheckedChange={() => handlePermissionToggle('notifications')}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                      <Activity className="w-5 h-5 text-teal-600" />
                    </div>
                    <div>
                      <p className="font-medium">Motion Sensors (IMU)</p>
                      <p className="text-xs text-gray-500">Enhanced gesture control and activity detection</p>
                    </div>
                  </div>
                  <Switch
                    checked={permissions.imu}
                    onCheckedChange={() => handlePermissionToggle('imu')}
                  />
                </div>
              </div>
            </Card>
          </div>
        );

      case 'settings':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setActiveSection(null)}
                className="hover:bg-white/30"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <h2 className="text-xl font-semibold">App Settings</h2>
            </div>

            {/* User Experience Settings */}
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4 mb-4">
              <h3 className="font-semibold text-gray-800 mb-4">User Experience</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Vibrate className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium">Haptic Feedback</p>
                      <p className="text-sm text-gray-600">Vibration for interactions</p>
                    </div>
                  </div>
                  <Switch
                    checked={appSettings.hapticFeedback}
                    onCheckedChange={() => handleSettingToggle('hapticFeedback')}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <Activity className="w-5 h-5 text-indigo-600" />
                    </div>
                    <div>
                      <p className="font-medium">Auto Sync</p>
                      <p className="text-sm text-gray-600">Sync preferences across devices</p>
                    </div>
                  </div>
                  <Switch
                    checked={appSettings.autoSync}
                    onCheckedChange={() => handleSettingToggle('autoSync')}
                  />
                </div>
              </div>
            </Card>

            {/* Technical Information */}
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
              <h3 className="font-semibold text-gray-800 mb-4">System Components</h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>User Devices:</strong> The Bytspot app on user phones, collecting multi-modal sensor data (GPS, Wi-Fi, BLE, IMU).</p>
                
                <p><strong>Beacon Infrastructure:</strong> Bluetooth Low Energy (BLE) beacons installed at partner venues for precise indoor positioning.</p>
                
                <p><strong>Cloud Processing:</strong> Real-time data processing and machine learning algorithms for personalized venue recommendations.</p>
                
                <p><strong>Edge Computing:</strong> Local processing on device for immediate response and privacy protection.</p>
              </div>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  if (activeSection) {
    return (
      <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
        <div className="p-6 max-w-2xl mx-auto">
          {renderDetailView()}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            onClick={onBack}
            className="hover:bg-white/30"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-gray-800">Profile</h1>
          <div className="w-16"></div> {/* Spacer for center alignment */}
        </div>

        {/* Profile Header */}
        {renderProfileHeader()}

        {/* Quick Actions */}
        {renderQuickActions()}

        {/* Account Section */}
        {renderMenuSection('Account', [
          {
            icon: <User className="w-5 h-5 text-gray-600" />,
            label: 'Personal Information',
            action: () => setIsEditingProfile(true)
          },
          {
            icon: <Heart className="w-5 h-5 text-purple-600" />,
            label: 'Vibe Preferences',
            action: () => setActiveSection('vibe-preferences'),
            badge: 'Premium'
          },
          {
            icon: <CreditCard className="w-5 h-5 text-gray-600" />,
            label: 'Payment Methods',
            action: () => setActiveSection('payment')
          },
          {
            icon: <Bell className="w-5 h-5 text-gray-600" />,
            label: 'Notifications',
            action: () => setActiveSection('notifications')
          },
          {
            icon: <Shield className="w-5 h-5 text-gray-600" />,
            label: 'Privacy & Security',
            action: () => setActiveSection('permissions')
          }
        ])}

        {/* Support Section */}
        {renderMenuSection('Support', [
          {
            icon: <HelpCircle className="w-5 h-5 text-gray-600" />,
            label: 'Help Center',
            action: () => setActiveSection('help')
          },
          {
            icon: <Award className="w-5 h-5 text-gray-600" />,
            label: 'Rewards Program',
            action: () => setActiveSection('rewards')
          },
          {
            icon: <Settings className="w-5 h-5 text-gray-600" />,
            label: 'App Settings',
            action: () => setActiveSection('settings')
          }
        ])}

        {/* Sign Out */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4 mb-6">
          <button
            onClick={() => {/* Handle sign out */}}
            className="w-full flex items-center justify-center gap-3 p-3 rounded-lg hover:bg-red-50/50 transition-colors text-red-600"
          >
            <LogOut className="w-5 h-5" />
            <span className="font-medium">Sign Out</span>
          </button>
        </Card>

        {/* Edit Profile Dialog */}
        {renderEditProfileDialog()}
      </div>
    </div>
  );
}