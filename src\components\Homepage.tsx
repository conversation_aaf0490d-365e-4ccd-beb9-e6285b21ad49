import { useState } from 'react';
import { motion } from 'motion/react';
import { Search, MapPin, Sparkles, Navigation, Building2 } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';

interface HomepageProps {
  onAddressSubmit: (address: string) => void;
  onHostClick?: () => void;
  onFeatureClick?: (feature: string) => void;
}

export function Homepage({ onAddressSubmit, onHostClick, onFeatureClick }: HomepageProps) {
  const [address, setAddress] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!address.trim()) return;
    
    setIsAnimating(true);
    setTimeout(() => {
      onAddressSubmit(address);
    }, 800);
  };

  const handleExploreNow = () => {
    if (address.trim()) {
      handleSubmit({ preventDefault: () => {} } as React.FormEvent);
    } else {
      // Use current location if no address is provided
      setIsAnimating(true);
      setTimeout(() => {
        onAddressSubmit('Current Location');
      }, 800);
    }
  };

  const mockSuggestions = [
    "123 Main St, San Francisco, CA",
    "456 Market St, San Francisco, CA", 
    "789 Mission St, San Francisco, CA"
  ];

  return (
    <div className="h-full flex flex-col items-center justify-center p-6 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-purple-400/20 to-pink-400/20 backdrop-blur-sm"
            style={{
              width: Math.random() * 300 + 150,
              height: Math.random() * 300 + 150,
              left: `${Math.random() * 120 - 10}%`,
              top: `${Math.random() * 120 - 10}%`,
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        className="relative z-10 w-full max-w-md"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        {/* Logo/Brand */}
        <motion.div
          className="text-center mb-12"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", bounce: 0.6 }}
        >
          <div className="relative">
            <motion.div
              className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-2xl"
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Sparkles className="w-12 h-12 text-white" />
            </motion.div>
            
            <motion.div
              className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center"
              animate={{ 
                scale: [1, 1.3, 1],
                rotate: [0, 180, 360]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut" 
              }}
            >
              <Navigation className="w-4 h-4 text-white" />
            </motion.div>
          </div>
          
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-3">
            MatchSpot
          </h1>
          <p className="text-gray-600 text-lg">
            Discover perfect spots with AI-powered matching
          </p>
        </motion.div>

        {/* Glassmorphism Search Card */}
        <motion.form
          onSubmit={handleSubmit}
          className="space-y-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <div className="relative">
            {/* Glassmorphism Background with Enhanced Effects */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-3xl blur-xl" />
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-3xl blur-2xl" />
            
            <div className="relative backdrop-blur-xl bg-white/25 rounded-3xl border border-white/40 p-8 shadow-2xl">
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-3xl"
                animate={{
                  opacity: [0.3, 0.6, 0.3],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              
              <div className="relative space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">
                    Where do you want to explore?
                  </h2>
                  <p className="text-gray-600">
                    Enter your destination to find perfect matches
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="relative">
                    <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500" />
                    <Input
                      type="text"
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      placeholder="Enter your destination address..."
                      className="pl-12 pr-4 py-4 bg-white/60 border-white/50 backdrop-blur-sm rounded-2xl text-base focus:ring-2 focus:ring-purple-500 focus:border-transparent h-14"
                      disabled={isAnimating}
                    />
                  </div>

                  {/* Address Suggestions */}
                  {address.length > 2 && !isAnimating && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-2"
                    >
                      {mockSuggestions
                        .filter(suggestion => 
                          suggestion.toLowerCase().includes(address.toLowerCase())
                        )
                        .slice(0, 3)
                        .map((suggestion, index) => (
                          <motion.button
                            key={suggestion}
                            type="button"
                            onClick={() => setAddress(suggestion)}
                            className="w-full text-left p-3 rounded-xl bg-white/40 hover:bg-white/60 transition-all duration-200 text-gray-700 backdrop-blur-sm border border-white/30"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-center gap-3">
                              <MapPin className="w-4 h-4 text-gray-500" />
                              <span>{suggestion}</span>
                            </div>
                          </motion.button>
                        ))}
                    </motion.div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      onClick={handleExploreNow}
                      disabled={isAnimating}
                      className="w-full py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 rounded-2xl shadow-xl relative overflow-hidden group h-14"
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      />
                      
                      <div className="relative flex items-center justify-center gap-3">
                        {isAnimating ? (
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          >
                            <Search className="w-6 h-6" />
                          </motion.div>
                        ) : (
                          <Search className="w-6 h-6" />
                        )}
                        <span className="font-semibold text-lg">
                          {isAnimating ? 'Starting Discovery...' : 'Explore Now'}
                        </span>
                      </div>
                    </Button>
                  </motion.div>
                  
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => handleExploreNow()}
                      disabled={isAnimating}
                      className="w-full py-4 bg-white/30 border-white/50 text-gray-700 hover:bg-white/40 rounded-2xl backdrop-blur-sm h-14"
                    >
                      <MapPin className="w-5 h-5 mr-2" />
                      Use Current Location
                    </Button>
                  </motion.div>

                  {/* Host Button */}
                  {onHostClick && (
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                    >
                      <Button
                        type="button"
                        onClick={onHostClick}
                        disabled={isAnimating}
                        className="w-full py-4 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white border-0 rounded-2xl shadow-xl relative overflow-hidden group h-14"
                      >
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-cyan-500 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        />
                        
                        <div className="relative flex items-center justify-center gap-3">
                          <Building2 className="w-6 h-6" />
                          <span className="font-semibold text-lg">
                            Become a Host
                          </span>
                        </div>
                      </Button>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.form>

        {/* Feature Highlights */}
        <motion.div
          className="mt-8 grid grid-cols-3 gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          {[
            { icon: '🚗', label: 'Smart Parking', color: 'from-blue-400 to-cyan-400' },
            { icon: '🎉', label: 'Trending Spots', color: 'from-purple-400 to-pink-400' },
            { icon: '🔑', label: 'Valet Service', color: 'from-green-400 to-emerald-400' },
          ].map((feature, index) => (
            <motion.div
              key={feature.label}
              className={`text-center p-4 backdrop-blur-sm bg-gradient-to-br ${feature.color}/20 rounded-2xl border border-white/30 cursor-pointer`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 + index * 0.1 }}
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => onFeatureClick?.(feature.label)}
            >
              <div className="text-3xl mb-2">{feature.icon}</div>
              <p className="text-xs text-gray-700 font-medium">{feature.label}</p>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
}