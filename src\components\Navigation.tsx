import { Home, Map, Users, MessageCircle, Building2, User, Trophy } from 'lucide-react';
import { motion } from 'motion/react';

interface NavigationProps {
  currentView: 'matches' | 'map' | 'insider' | 'concierge' | 'profile' | 'social';
  onViewChange: (view: 'matches' | 'map' | 'insider' | 'concierge' | 'profile' | 'social') => void;
  onToggleToBusiness?: () => void;
  hasNewSocialActivity?: boolean;
}

const navItems = [
  { id: 'matches', icon: Home, label: 'Matches' },
  { id: 'map', icon: Map, label: 'Map' },
  { id: 'insider', icon: Users, label: 'Vibe' },
  { id: 'social', icon: Trophy, label: 'Social' },
  { id: 'profile', icon: User, label: 'Profile' },
] as const;

export function Navigation({ currentView, onViewChange, onToggleToBusiness, hasNewSocialActivity = false }: NavigationProps) {
  return (
    <motion.nav 
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      className="fixed bottom-0 left-0 right-0 z-50"
    >
      <div className="mx-4 mb-4">
        <div className="backdrop-blur-xl bg-white/20 rounded-3xl border border-white/30 shadow-xl p-2">
          <div className="flex items-center justify-around">
            {navItems.map((item) => {
              const isActive = currentView === item.id;
              const Icon = item.icon;
              const showBadge = item.id === 'social' && hasNewSocialActivity && !isActive;
              
              return (
                <motion.button
                  key={item.id}
                  onClick={() => onViewChange(item.id as any)}
                  className={`
                    relative flex flex-col items-center gap-1 p-3 rounded-2xl transition-all duration-200
                    ${isActive 
                      ? 'text-purple-600' 
                      : 'text-gray-600 hover:text-purple-500'
                    }
                  `}
                  whileTap={{ scale: 0.95 }}
                >
                  {isActive && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute inset-0 bg-white/40 rounded-2xl border border-white/50"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                  
                  <div className="relative z-10">
                    <Icon className={`w-5 h-5 ${isActive ? 'text-purple-600' : ''}`} />
                    {showBadge && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"
                      />
                    )}
                  </div>
                  
                  <span className={`
                    text-xs font-medium relative z-10
                    ${isActive ? 'text-purple-600' : ''}
                  `}>
                    {item.label}
                  </span>
                </motion.button>
              );
            })}
            
            {/* Business Mode Toggle */}
            {onToggleToBusiness && (
              <motion.button
                onClick={onToggleToBusiness}
                className="relative flex flex-col items-center gap-1 p-3 rounded-2xl transition-all duration-200 text-gray-600 hover:text-blue-500"
                whileTap={{ scale: 0.95 }}
              >
                <div className="relative z-10">
                  <Building2 className="w-5 h-5" />
                </div>
                
                <span className="text-xs font-medium relative z-10">
                  Business
                </span>
              </motion.button>
            )}
          </div>
        </div>
      </div>
    </motion.nav>
  );
}