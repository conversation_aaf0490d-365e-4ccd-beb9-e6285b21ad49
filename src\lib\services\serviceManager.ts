// Service Manager - Handles safe initialization order for all services
// This prevents circular dependencies and ensures proper startup sequence

interface ServiceInitializer {
  name: string;
  initialize: () => void | Promise<void>;
  dependencies?: string[];
  initialized: boolean;
}

class ServiceManager {
  private static instance: ServiceManager;
  private services: Map<string, ServiceInitializer> = new Map();
  private initializationPromise: Promise<void> | null = null;
  private isInitializing = false;

  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  registerService(config: Omit<ServiceInitializer, 'initialized'>): void {
    this.services.set(config.name, {
      ...config,
      initialized: false,
    });
  }

  async initializeAll(): Promise<void> {
    // Prevent multiple concurrent initializations
    if (this.isInitializing && this.initializationPromise) {
      return this.initializationPromise;
    }

    if (this.isInitializing) {
      return;
    }

    this.isInitializing = true;

    this.initializationPromise = this._performInitialization();
    
    try {
      await this.initializationPromise;
    } finally {
      this.isInitializing = false;
    }
  }

  private async _performInitialization(): Promise<void> {
    const initializationOrder = this.resolveInitializationOrder();
    
    console.log('🚀 Starting service initialization...');
    
    for (const serviceName of initializationOrder) {
      const service = this.services.get(serviceName);
      if (!service || service.initialized) continue;

      try {
        console.log(`  ⚡ Initializing ${serviceName}...`);
        await service.initialize();
        service.initialized = true;
        console.log(`  ✅ ${serviceName} initialized successfully`);
      } catch (error) {
        console.error(`  ❌ Failed to initialize ${serviceName}:`, error);
        // Don't throw - let other services initialize
      }
    }
    
    console.log('🎉 Service initialization complete');
  }

  private resolveInitializationOrder(): string[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const order: string[] = [];

    const visit = (serviceName: string) => {
      if (visiting.has(serviceName)) {
        console.warn(`Circular dependency detected involving ${serviceName}`);
        return;
      }
      
      if (visited.has(serviceName)) {
        return;
      }

      visiting.add(serviceName);
      
      const service = this.services.get(serviceName);
      if (service?.dependencies) {
        for (const dep of service.dependencies) {
          if (this.services.has(dep)) {
            visit(dep);
          }
        }
      }

      visiting.delete(serviceName);
      visited.add(serviceName);
      order.push(serviceName);
    };

    // Visit all services
    for (const serviceName of this.services.keys()) {
      visit(serviceName);
    }

    return order;
  }

  isServiceInitialized(serviceName: string): boolean {
    return this.services.get(serviceName)?.initialized || false;
  }

  async waitForService(serviceName: string): Promise<void> {
    // Wait for overall initialization to complete first
    if (this.initializationPromise) {
      await this.initializationPromise;
    }

    // Check if service is initialized
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service ${serviceName} not registered`);
    }

    if (!service.initialized) {
      throw new Error(`Service ${serviceName} failed to initialize`);
    }
  }

  reset(): void {
    for (const service of this.services.values()) {
      service.initialized = false;
    }
    this.initializationPromise = null;
    this.isInitializing = false;
  }
}

export const serviceManager = ServiceManager.getInstance();

// Service registration helpers
export function registerService(config: Omit<ServiceInitializer, 'initialized'>): void {
  serviceManager.registerService(config);
}

export function initializeAllServices(): Promise<void> {
  return serviceManager.initializeAll();
}

export function isServiceReady(serviceName: string): boolean {
  return serviceManager.isServiceInitialized(serviceName);
}

export function waitForService(serviceName: string): Promise<void> {
  return serviceManager.waitForService(serviceName);
}