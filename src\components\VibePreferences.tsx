import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  MapPin, 
  Star, 
  DollarSign, 
  Users, 
  Volume2, 
  Music, 
  UtensilsCrossed,
  Clock,
  Calendar,
  Settings,
  ChevronDown,
  Check,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';
import { Button } from './ui/button';
import { Slider } from './ui/slider';
import { Switch } from './ui/switch';
import { Badge } from './ui/badge';
import { Checkbox } from './ui/checkbox';

interface VibePreferencesProps {
  onComplete: (preferences: UserVibePreferences) => void;
  onBack?: () => void;
  initialPreferences?: Partial<UserVibePreferences>;
  currentLocation?: string;
}

export interface UserVibePreferences {
  location: {
    city: string;
    maxDistance: number; // in miles
  };
  basics: {
    minRating: number; // 1-5
    priceRange: [number, number]; // [min, max] 1-4 ($-$$$$)
  };
  atmosphere: {
    crowdEnergy: 'calm' | 'medium' | 'high';
    noiseLevel: 'quiet' | 'moderate' | 'lively';
  };
  music: string[]; // selected genres
  cuisine: string[]; // selected cuisine types
  time: {
    timeOfDay: string[]; // morning, afternoon, evening, late-night
    dayOfWeek: 'weekdays' | 'weekends' | 'both';
  };
}

const musicGenres = [
  { id: 'pop', label: 'Pop', icon: '🎵' },
  { id: 'hip-hop', label: 'Hip-Hop', icon: '🎤' },
  { id: 'electronic', label: 'Electronic', icon: '🎧' },
  { id: 'rock', label: 'Rock', icon: '🎸' },
  { id: 'jazz', label: 'Jazz', icon: '🎺' },
  { id: 'latin', label: 'Latin', icon: '💃' },
  { id: 'indie', label: 'Indie', icon: '🎼' },
  { id: 'country', label: 'Country', icon: '🤠' },
  { id: 'r&b', label: 'R&B', icon: '🎶' },
  { id: 'reggae', label: 'Reggae', icon: '🌴' },
];

const cuisineTypes = [
  { id: 'italian', label: 'Italian', icon: '🍝' },
  { id: 'mexican', label: 'Mexican', icon: '🌮' },
  { id: 'thai', label: 'Thai', icon: '🍜' },
  { id: 'japanese', label: 'Japanese', icon: '🍣' },
  { id: 'indian', label: 'Indian', icon: '🍛' },
  { id: 'chinese', label: 'Chinese', icon: '🥡' },
  { id: 'mediterranean', label: 'Mediterranean', icon: '🫒' },
  { id: 'american', label: 'American', icon: '🍔' },
  { id: 'french', label: 'French', icon: '🥖' },
  { id: 'korean', label: 'Korean', icon: '🥢' },
];

const timeOfDayOptions = [
  { id: 'morning', label: 'Morning', icon: '🌅', range: '6AM - 12PM' },
  { id: 'afternoon', label: 'Afternoon', icon: '☀️', range: '12PM - 6PM' },
  { id: 'evening', label: 'Evening', icon: '🌆', range: '6PM - 10PM' },
  { id: 'late-night', label: 'Late Night', icon: '🌙', range: '10PM - 2AM' },
];

const crowdEnergyOptions = [
  { id: 'calm', label: 'Calm & Relaxed', description: 'Peaceful, quiet atmosphere', color: 'from-blue-400 to-green-400' },
  { id: 'medium', label: 'Medium Energy', description: 'Balanced, social vibe', color: 'from-yellow-400 to-orange-400' },
  { id: 'high', label: 'High Energy', description: 'Lively, exciting atmosphere', color: 'from-red-400 to-pink-400' },
];

const noiseLevelOptions = [
  { id: 'quiet', label: 'Quiet', description: 'Conversation-friendly', icon: '🤫' },
  { id: 'moderate', label: 'Moderate', description: 'Some background noise', icon: '🗣️' },
  { id: 'lively', label: 'Lively', description: 'Vibrant and energetic', icon: '📢' },
];

export function VibePreferences({ 
  onComplete, 
  onBack, 
  initialPreferences,
  currentLocation = "San Francisco, CA" 
}: VibePreferencesProps) {
  const [currentSection, setCurrentSection] = useState(0);
  
  // Create default preferences structure
  const defaultPreferences: UserVibePreferences = {
    location: {
      city: currentLocation,
      maxDistance: 25,
    },
    basics: {
      minRating: 4.0,
      priceRange: [1, 3],
    },
    atmosphere: {
      crowdEnergy: 'medium',
      noiseLevel: 'moderate',
    },
    music: [],
    cuisine: [],
    time: {
      timeOfDay: ['evening'],
      dayOfWeek: 'both',
    },
  };

  // Safely merge initial preferences with defaults
  const [preferences, setPreferences] = useState<UserVibePreferences>(() => {
    if (!initialPreferences) {
      return defaultPreferences;
    }

    return {
      location: {
        ...defaultPreferences.location,
        ...initialPreferences.location,
      },
      basics: {
        ...defaultPreferences.basics,
        ...initialPreferences.basics,
      },
      atmosphere: {
        ...defaultPreferences.atmosphere,
        ...initialPreferences.atmosphere,
      },
      music: Array.isArray(initialPreferences.music) ? initialPreferences.music : [],
      cuisine: Array.isArray(initialPreferences.cuisine) ? initialPreferences.cuisine : [],
      time: {
        ...defaultPreferences.time,
        ...initialPreferences.time,
        timeOfDay: Array.isArray(initialPreferences.time?.timeOfDay) 
          ? initialPreferences.time.timeOfDay 
          : defaultPreferences.time.timeOfDay,
      },
    };
  });

  const [venueCount, setVenueCount] = useState(247); // Mock venue count
  const [isCompleting, setIsCompleting] = useState(false);

  const sections = [
    { id: 'location', title: 'Location & Basics', icon: MapPin },
    { id: 'atmosphere', title: 'Atmosphere & Vibe', icon: Users },
    { id: 'preferences', title: 'Music & Cuisine', icon: Music },
    { id: 'time', title: 'Time Preferences', icon: Clock },
  ];

  // Mock venue count update based on distance
  useEffect(() => {
    const count = Math.max(50, Math.floor(Math.random() * 200) + preferences.location.maxDistance * 8);
    setVenueCount(count);
  }, [preferences.location.maxDistance]);

  const handleNext = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(prev => prev + 1);
    } else {
      setIsCompleting(true);
      setTimeout(() => {
        onComplete(preferences);
      }, 1500);
    }
  };

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(prev => prev - 1);
    } else if (onBack) {
      onBack();
    }
  };

  const updatePreferences = (section: keyof UserVibePreferences, updates: any) => {
    setPreferences(prev => ({
      ...prev,
      [section]: { ...prev[section], ...updates }
    }));
  };

  const toggleArrayItem = (array: string[], item: string): string[] => {
    return array.includes(item) 
      ? array.filter(i => i !== item)
      : [...array, item];
  };

  const renderLocationSection = () => (
    <div className="space-y-8">
      {/* Current Location */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MapPin className="w-5 h-5 text-purple-600" />
            <div>
              <h3 className="font-semibold text-gray-800">Location</h3>
              <p className="text-sm text-gray-600">Your search area</p>
            </div>
          </div>
          <Button variant="outline" className="bg-white/40 border-white/50">
            Change
          </Button>
        </div>
        
        <div className="bg-white/30 rounded-2xl p-4 border border-white/40">
          <p className="font-medium text-gray-800">{preferences.location.city}</p>
        </div>
      </div>

      {/* Max Distance */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-gray-800">Maximum Distance</h3>
            <p className="text-sm text-gray-600">{venueCount} venues within range</p>
          </div>
          <Badge variant="outline" className="bg-purple-100 border-purple-300 text-purple-700">
            {preferences.location.maxDistance} miles
          </Badge>
        </div>
        
        <div className="space-y-3">
          <Slider
            value={[preferences.location.maxDistance]}
            onValueChange={([value]) => updatePreferences('location', { maxDistance: value })}
            max={50}
            min={5}
            step={5}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-gray-500">
            <span>5 miles</span>
            <span>50 miles</span>
          </div>
        </div>
      </div>

      {/* Minimum Rating */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-gray-800">Minimum Rating</h3>
            <p className="text-sm text-gray-600">Filter by venue quality</p>
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span className="font-medium text-gray-800">{preferences.basics.minRating.toFixed(1)}</span>
          </div>
        </div>
        
        <Slider
          value={[preferences.basics.minRating]}
          onValueChange={([value]) => updatePreferences('basics', { minRating: value })}
          max={5}
          min={1}
          step={0.5}
          className="w-full"
        />
      </div>

      {/* Price Range */}
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold text-gray-800">Price Range</h3>
          <p className="text-sm text-gray-600">Set your budget preferences</p>
        </div>
        
        <div className="grid grid-cols-4 gap-2">
          {['$', '$$', '$$$', '$$$$'].map((price, index) => (
            <Button
              key={price}
              variant="outline"
              onClick={() => {
                const currentRange = preferences.basics.priceRange;
                const level = index + 1;
                let newRange: [number, number];
                
                if (level < currentRange[0]) {
                  newRange = [level, currentRange[1]];
                } else if (level > currentRange[1]) {
                  newRange = [currentRange[0], level];
                } else {
                  newRange = currentRange;
                }
                
                updatePreferences('basics', { priceRange: newRange });
              }}
              className={`
                ${index + 1 >= preferences.basics.priceRange[0] && 
                  index + 1 <= preferences.basics.priceRange[1]
                  ? 'bg-purple-100 border-purple-300 text-purple-700'
                  : 'bg-white/40 border-white/50'
                }
              `}
            >
              {price}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderAtmosphereSection = () => (
    <div className="space-y-8">
      {/* Crowd Energy */}
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold text-gray-800">Crowd Energy</h3>
          <p className="text-sm text-gray-600">What kind of vibe do you prefer?</p>
        </div>
        
        <div className="space-y-3">
          {crowdEnergyOptions.map((option) => (
            <motion.button
              key={option.id}
              onClick={() => updatePreferences('atmosphere', { crowdEnergy: option.id })}
              className={`
                w-full p-4 rounded-2xl border-2 transition-all duration-200 text-left relative overflow-hidden
                ${preferences.atmosphere.crowdEnergy === option.id
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-white/30 bg-white/20 hover:bg-white/30'
                }
              `}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className={`absolute inset-0 bg-gradient-to-r ${option.color} opacity-10`} />
              <div className="relative">
                <h4 className="font-medium text-gray-800">{option.label}</h4>
                <p className="text-sm text-gray-600">{option.description}</p>
              </div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Noise Preference */}
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold text-gray-800">Noise Preference</h3>
          <p className="text-sm text-gray-600">How loud do you like your venues?</p>
        </div>
        
        <div className="grid grid-cols-3 gap-3">
          {noiseLevelOptions.map((option) => (
            <motion.button
              key={option.id}
              onClick={() => updatePreferences('atmosphere', { noiseLevel: option.id })}
              className={`
                p-4 rounded-2xl border-2 transition-all duration-200 text-center
                ${preferences.atmosphere.noiseLevel === option.id
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-white/30 bg-white/20 hover:bg-white/30'
                }
              `}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="text-2xl mb-2">{option.icon}</div>
              <h4 className="font-medium text-gray-800 text-sm">{option.label}</h4>
              <p className="text-xs text-gray-600">{option.description}</p>
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPreferencesSection = () => (
    <div className="space-y-8">
      {/* Music Preferences */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Music className="w-5 h-5 text-purple-600" />
          <div>
            <h3 className="font-semibold text-gray-800">Music Preferences</h3>
            <p className="text-sm text-gray-600">Select genres you enjoy</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          {musicGenres.map((genre) => (
            <motion.div
              key={genre.id}
              className={`
                flex items-center gap-3 p-3 rounded-xl border transition-all duration-200 cursor-pointer
                ${preferences.music.includes(genre.id)
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-white/30 bg-white/20 hover:bg-white/30'
                }
              `}
              onClick={() => updatePreferences('music', toggleArrayItem(preferences.music, genre.id))}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Checkbox 
                checked={preferences.music.includes(genre.id)}
                onChange={() => {}}
                className="pointer-events-none"
              />
              <span className="text-lg">{genre.icon}</span>
              <span className="font-medium text-gray-800">{genre.label}</span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Cuisine Preferences */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <UtensilsCrossed className="w-5 h-5 text-purple-600" />
          <div>
            <h3 className="font-semibold text-gray-800">Cuisine Preferences</h3>
            <p className="text-sm text-gray-600">What food do you love?</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          {cuisineTypes.map((cuisine) => (
            <motion.div
              key={cuisine.id}
              className={`
                flex items-center gap-3 p-3 rounded-xl border transition-all duration-200 cursor-pointer
                ${preferences.cuisine.includes(cuisine.id)
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-white/30 bg-white/20 hover:bg-white/30'
                }
              `}
              onClick={() => updatePreferences('cuisine', toggleArrayItem(preferences.cuisine, cuisine.id))}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Checkbox 
                checked={preferences.cuisine.includes(cuisine.id)}
                onChange={() => {}}
                className="pointer-events-none"
              />
              <span className="text-lg">{cuisine.icon}</span>
              <span className="font-medium text-gray-800">{cuisine.label}</span>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTimeSection = () => (
    <div className="space-y-8">
      {/* Time of Day */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Clock className="w-5 h-5 text-purple-600" />
          <div>
            <h3 className="font-semibold text-gray-800">Time of Day</h3>
            <p className="text-sm text-gray-600">When do you usually go out?</p>
          </div>
        </div>
        
        <div className="space-y-3">
          {timeOfDayOptions.map((time) => (
            <motion.div
              key={time.id}
              className={`
                flex items-center justify-between p-4 rounded-xl border transition-all duration-200 cursor-pointer
                ${preferences.time.timeOfDay.includes(time.id)
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-white/30 bg-white/20 hover:bg-white/30'
                }
              `}
              onClick={() => updatePreferences('time', { 
                timeOfDay: toggleArrayItem(preferences.time.timeOfDay, time.id) 
              })}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center gap-4">
                <Checkbox 
                  checked={preferences.time.timeOfDay.includes(time.id)}
                  onChange={() => {}}
                  className="pointer-events-none"
                />
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{time.icon}</span>
                  <div>
                    <h4 className="font-medium text-gray-800">{time.label}</h4>
                    <p className="text-sm text-gray-600">{time.range}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Day of Week */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Calendar className="w-5 h-5 text-purple-600" />
          <div>
            <h3 className="font-semibold text-gray-800">Day of Week</h3>
            <p className="text-sm text-gray-600">Weekdays, weekends, or both?</p>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-3">
          {[
            { id: 'weekdays', label: 'Weekdays', icon: '📅', desc: 'Mon - Fri' },
            { id: 'weekends', label: 'Weekends', icon: '🎉', desc: 'Sat - Sun' },
            { id: 'both', label: 'Both', icon: '🗓️', desc: 'Any day' },
          ].map((option) => (
            <motion.button
              key={option.id}
              onClick={() => updatePreferences('time', { dayOfWeek: option.id as any })}
              className={`
                p-4 rounded-2xl border-2 transition-all duration-200 text-center
                ${preferences.time.dayOfWeek === option.id
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-white/30 bg-white/20 hover:bg-white/30'
                }
              `}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="text-2xl mb-2">{option.icon}</div>
              <h4 className="font-medium text-gray-800 text-sm">{option.label}</h4>
              <p className="text-xs text-gray-600">{option.desc}</p>
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderCurrentSection = () => {
    switch (currentSection) {
      case 0: return renderLocationSection();
      case 1: return renderAtmosphereSection();
      case 2: return renderPreferencesSection();
      case 3: return renderTimeSection();
      default: return null;
    }
  };

  const canProceed = () => {
    switch (currentSection) {
      case 0: return true; // Location section always allows proceeding
      case 1: return true; // Atmosphere section always allows proceeding
      case 2: return preferences.music.length > 0 || preferences.cuisine.length > 0;
      case 3: return preferences.time.timeOfDay.length > 0;
      default: return false;
    }
  };

  const currentSectionData = sections[currentSection];
  const SectionIcon = currentSectionData.icon;

  return (
    <div className="h-full bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100 p-4 overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          style={{ top: '10%', right: '10%' }}
        />
      </div>

      <div className="relative z-10 h-full flex flex-col">
        {/* Header */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="text-center mb-4">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">Vibe Preferences</h1>
            <p className="text-gray-600">Personalize your perfect matches</p>
          </div>
          
          {/* Progress Bar */}
          <div className="flex justify-center gap-2 mb-4">
            {sections.map((_, index) => (
              <div
                key={index}
                className={`w-12 h-1 rounded-full transition-all duration-300 ${
                  index <= currentSection ? 'bg-purple-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          {/* Section Title */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <SectionIcon className="w-5 h-5 text-purple-600" />
              <h2 className="text-lg font-semibold text-gray-800">
                {currentSectionData.title}
              </h2>
            </div>
            <p className="text-sm text-gray-500">
              Step {currentSection + 1} of {sections.length}
            </p>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSection}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.3 }}
              className="backdrop-blur-xl bg-white/30 rounded-3xl border border-white/40 p-6 h-full"
            >
              {renderCurrentSection()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Bottom Actions */}
        <motion.div
          className="mt-6 space-y-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex gap-3">
            <Button
              onClick={handlePrevious}
              variant="outline"
              className="flex-1 py-3 bg-white/40 border-white/50 text-gray-700 hover:bg-white/60"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              {currentSection === 0 ? 'Back' : 'Previous'}
            </Button>

            <Button
              onClick={handleNext}
              disabled={!canProceed() || isCompleting}
              className="flex-2 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 shadow-xl"
            >
              <div className="flex items-center justify-center gap-2">
                {isCompleting ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Settings className="w-4 h-4" />
                  </motion.div>
                ) : currentSection === sections.length - 1 ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <ArrowRight className="w-4 h-4" />
                )}
                <span>
                  {isCompleting 
                    ? 'Personalizing...' 
                    : currentSection === sections.length - 1 
                      ? 'Complete Setup' 
                      : 'Continue'
                  }
                </span>
              </div>
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}