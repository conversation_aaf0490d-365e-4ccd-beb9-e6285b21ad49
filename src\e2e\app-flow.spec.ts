import { test, expect } from '@playwright/test';

test.describe('Bytspot App Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.addInitScript(() => {
      localStorage.setItem('bytspot_tokens', JSON.stringify({
        accessToken: 'mock-token',
        refreshToken: 'mock-refresh-token',
        expiresAt: Date.now() + 3600000,
      }));
    });

    // Mock API responses
    await page.route('**/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'test-user',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'consumer',
          permissions: [],
          preferences: {
            permissions: {
              gps: true,
              wifi: true,
              bluetooth: true,
              camera: true,
              notifications: true,
              imu: true,
            },
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }),
      });
    });
  });

  test('should navigate through main app views', async ({ page }) => {
    await page.goto('/app');

    // Should start on matches view
    await expect(page).toHaveURL(/.*app\/matches/);
    
    // Test navigation
    await page.getByRole('button', { name: /map/i }).click();
    await expect(page).toHaveURL(/.*app\/map/);
    
    await page.getByRole('button', { name: /profile/i }).click();
    await expect(page).toHaveURL(/.*app\/profile/);
  });

  test('should handle venue matching flow', async ({ page }) => {
    await page.goto('/app/matches');
    
    // Mock matches API
    await page.route('**/api/matches*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: '1',
            type: 'venue',
            title: 'Test Venue',
            subtitle: 'Great place',
            distance: '5 min walk',
            availability: 'Open now',
            rating: 4.5,
            image: 'https://example.com/image.jpg',
            details: {
              description: 'Test venue description',
              features: ['Feature 1', 'Feature 2'],
            },
          },
        ]),
      });
    });

    // Should display match card
    await expect(page.getByText('Test Venue')).toBeVisible();
    await expect(page.getByText('Great place')).toBeVisible();
  });

  test('should handle live vibe camera feature', async ({ page, context }) => {
    // Grant camera permissions
    await context.grantPermissions(['camera', 'microphone']);
    
    await page.goto('/app/map');
    
    // Click FAB to open camera
    const fabButton = page.getByRole('button', { name: /capture live vibe/i });
    await expect(fabButton).toBeVisible();
    await fabButton.click();
    
    // Camera modal should open
    await expect(page.getByText('Live Vibe Capture')).toBeVisible();
    await expect(page.getByText('Share what\'s happening right now')).toBeVisible();
  });

  test('should handle business mode toggle for hosts', async ({ page }) => {
    // Mock user as host
    await page.route('**/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'test-user',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'host',
          permissions: [
            { resource: 'venue', actions: ['read', 'write', 'manage'] },
          ],
          preferences: {
            permissions: {
              gps: true,
              wifi: true,
              bluetooth: true,
              camera: true,
              notifications: true,
              imu: true,
            },
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }),
      });
    });

    await page.goto('/app');
    
    // Should show business toggle option
    const businessToggle = page.getByRole('button', { name: /business/i });
    await expect(businessToggle).toBeVisible();
    
    await businessToggle.click();
    await expect(page).toHaveURL(/.*business/);
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE
    
    await page.goto('/app');
    
    // Check mobile layout
    await expect(page.locator('[data-testid="mobile-navigation"]')).toBeVisible();
    
    // Test touch interactions
    const matchCard = page.locator('[data-testid="match-card"]').first();
    if (await matchCard.isVisible()) {
      await matchCard.swipeGesture({ direction: 'left' });
      // Should navigate to next match
    }
  });

  test('should handle offline functionality', async ({ page, context }) => {
    await page.goto('/app');
    
    // Go offline
    await context.setOffline(true);
    
    // App should show offline indicator
    await expect(page.getByText(/offline/i)).toBeVisible();
    
    // Go back online
    await context.setOffline(false);
    
    // Offline indicator should disappear
    await expect(page.getByText(/offline/i)).not.toBeVisible();
  });

  test('should track analytics events', async ({ page }) => {
    // Mock analytics endpoint
    const analyticsEvents: any[] = [];
    await page.route('**/analytics/track', async route => {
      const request = route.request();
      analyticsEvents.push(await request.json());
      await route.fulfill({ status: 200, body: 'OK' });
    });

    await page.goto('/app');
    
    // Navigate to trigger page view events
    await page.getByRole('button', { name: /map/i }).click();
    
    // Check that analytics events were fired
    expect(analyticsEvents.length).toBeGreaterThan(0);
    expect(analyticsEvents.some(event => event.name === 'page_view')).toBeTruthy();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/**', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ message: 'Server error' }),
      });
    });

    await page.goto('/app');
    
    // Should show error state
    await expect(page.getByText(/something went wrong/i)).toBeVisible();
    
    // Should have retry option
    const retryButton = page.getByRole('button', { name: /try again/i });
    if (await retryButton.isVisible()) {
      await retryButton.click();
    }
  });
});