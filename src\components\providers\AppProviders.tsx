import React, { useEffect, useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { BrowserRouter } from 'react-router-dom';
import { Toaster } from 'sonner';
import { AuthProvider } from '../../lib/auth/AuthProvider';
import { errorService, ErrorBoundary } from '../../lib/monitoring/errorService';
import { initializeBytspotServices } from '../../lib/services/serviceInitializer';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error instanceof Error && error.message.includes('4')) {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
      onError: (error) => {
        // Safe error handling - only if service is initialized
        try {
          errorService.captureError(error as Error, {
            feature: 'api',
            action: 'mutation',
          });
        } catch (e) {
          console.error('Failed to capture mutation error:', e);
        }
      },
    },
  },
});

interface AppProvidersProps {
  children: React.ReactNode;
}

function AppProvidersInner({ children }: AppProvidersProps) {
  const [servicesInitialized, setServicesInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    const initializeServices = async () => {
      try {
        console.log('🚀 Starting service initialization...');
        await initializeBytspotServices();
        
        if (isMounted) {
          setServicesInitialized(true);
          console.log('✅ All services initialized successfully');
        }
      } catch (error) {
        console.error('❌ Service initialization failed:', error);
        if (isMounted) {
          setInitializationError(error instanceof Error ? error.message : 'Unknown initialization error');
        }
      }
    };

    initializeServices();

    // Global error handlers (only if services available)
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      try {
        errorService.captureError(new Error(event.reason), {
          feature: 'global',
          action: 'unhandled_promise_rejection',
        });
      } catch (e) {
        console.error('Unhandled promise rejection:', event.reason);
      }
    };

    const handleJavaScriptError = (event: ErrorEvent) => {
      try {
        errorService.captureError(event.error, {
          feature: 'global',
          action: 'javascript_error',
          metadata: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
          },
        });
      } catch (e) {
        console.error('JavaScript error:', event.error);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleJavaScriptError);

    return () => {
      isMounted = false;
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleJavaScriptError);
    };
  }, []);

  // Show loading state while services initialize
  if (!servicesInitialized && !initializationError) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
        <div className="text-center space-y-4 p-8 bg-white/80 backdrop-blur-sm rounded-lg shadow-lg max-w-md">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto animate-spin"></div>
          <h2 className="text-xl font-semibold">Initializing Bytspot</h2>
          <p className="text-gray-600">Starting up services...</p>
        </div>
      </div>
    );
  }

  // Show error state if initialization failed
  if (initializationError) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
        <div className="text-center space-y-4 p-8 bg-white/80 backdrop-blur-sm rounded-lg shadow-lg max-w-md">
          <h2 className="text-xl font-semibold text-red-600">Initialization Failed</h2>
          <p className="text-gray-600">Failed to start services: {initializationError}</p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          {children}
          
          {/* Global toast notifications */}
          <Toaster 
            position="top-center"
            toastOptions={{
              style: {
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
              },
            }}
          />
          
          {/* Development tools */}
          {import.meta.env.DEV && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </AuthProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
}

// Wrap with error boundary
export function AppProviders({ children }: AppProvidersProps) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
          <div className="text-center space-y-4 p-8 bg-white/80 backdrop-blur-sm rounded-lg shadow-lg max-w-md">
            <h2 className="text-xl font-semibold text-red-600">Application Error</h2>
            <p className="text-gray-600">Sorry, something went wrong with the app.</p>
            <div className="space-x-2">
              <button 
                onClick={resetError}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md"
              >
                Try Again
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md"
              >
                Reload Page
              </button>
            </div>
            <details className="mt-4 text-left">
              <summary className="cursor-pointer text-sm text-gray-500">Error Details</summary>
              <pre className="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {error?.message || 'Unknown error'}
              </pre>
            </details>
          </div>
        </div>
      )}
    >
      <AppProvidersInner>
        {children}
      </AppProvidersInner>
    </ErrorBoundary>
  );
}