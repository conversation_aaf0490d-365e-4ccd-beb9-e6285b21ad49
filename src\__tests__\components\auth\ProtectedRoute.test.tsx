import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { ProtectedRoute } from '../../../components/auth/ProtectedRoute';
import { AuthProvider } from '../../../lib/auth/AuthProvider';
import type { User } from '../../../lib/auth/types';

// Mock the auth service
jest.mock('../../../lib/auth/authService', () => ({
  getCurrentUser: jest.fn(),
  validateToken: jest.fn(),
  logout: jest.fn(),
}));

// Test component to render inside protected route
const TestComponent = () => <div>Protected Content</div>;

// Mock user data
const mockConsumerUser: User = {
  id: 'user-1',
  email: '<EMAIL>',
  name: 'Consumer User',
  role: 'consumer',
  permissions: [],
  preferences: {
    permissions: {
      gps: true,
      wifi: true,
      bluetooth: true,
      camera: true,
      notifications: true,
      imu: true,
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const mockHostUser: User = {
  id: 'user-2',
  email: '<EMAIL>',
  name: 'Host User',
  role: 'host',
  permissions: [
    { resource: 'venue', actions: ['read', 'write', 'manage'] },
    { resource: 'analytics', actions: ['read'] },
  ],
  preferences: {
    permissions: {
      gps: true,
      wifi: true,
      bluetooth: true,
      camera: true,
      notifications: true,
      imu: true,
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const mockAdminUser: User = {
  id: 'user-3',
  email: '<EMAIL>',
  name: 'Admin User',
  role: 'admin',
  permissions: [
    { resource: '*', actions: ['*'] },
  ],
  preferences: {
    permissions: {
      gps: true,
      wifi: true,
      bluetooth: true,
      camera: true,
      notifications: true,
      imu: true,
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

// Helper to render component with auth context
const renderWithAuth = (
  component: React.ReactElement,
  authState: {
    isAuthenticated: boolean;
    isLoading: boolean;
    user: User | null;
  }
) => {
  const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
    const contextValue = {
      ...authState,
      hasRole: (role: string) => authState.user?.role === role,
      hasPermission: (resource: string, action: string) => {
        if (!authState.user?.permissions) return false;
        return authState.user.permissions.some(
          perm => 
            (perm.resource === resource || perm.resource === '*') &&
            (perm.actions.includes(action) || perm.actions.includes('*'))
        );
      },
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn(),
    };

    return (
      <AuthProvider.Provider value={contextValue as any}>
        {children}
      </AuthProvider.Provider>
    );
  };

  return render(
    <BrowserRouter>
      <MockAuthProvider>
        <Routes>
          <Route path="/" element={component} />
          <Route path="/login" element={<div>Login Page</div>} />
          <Route path="/unauthorized" element={<div>Unauthorized Page</div>} />
        </Routes>
      </MockAuthProvider>
    </BrowserRouter>
  );
};

describe('ProtectedRoute', () => {
  describe('Loading State', () => {
    it('should show loading skeleton when auth is loading', () => {
      renderWithAuth(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: false,
          isLoading: true,
          user: null,
        }
      );

      expect(screen.getByText('Loading Bytspot...')).toBeInTheDocument();
      expect(screen.getByText('Preparing your personalized experience')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });
  });

  describe('Unauthenticated Access', () => {
    it('should redirect to login when user is not authenticated', () => {
      renderWithAuth(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: false,
          isLoading: false,
          user: null,
        }
      );

      expect(screen.getByText('Login Page')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('should redirect to custom fallback path when specified', () => {
      render(
        <BrowserRouter>
          <Routes>
            <Route path="/" element={
              <ProtectedRoute fallbackPath="/custom-login">
                <TestComponent />
              </ProtectedRoute>
            } />
            <Route path="/custom-login" element={<div>Custom Login Page</div>} />
          </Routes>
        </BrowserRouter>
      );

      expect(screen.getByText('Custom Login Page')).toBeInTheDocument();
    });
  });

  describe('Authenticated Access', () => {
    it('should render children when user is authenticated', () => {
      renderWithAuth(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockConsumerUser,
        }
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
      expect(screen.queryByText('Login Page')).not.toBeInTheDocument();
    });
  });

  describe('Role-Based Access Control', () => {
    it('should allow access when user has required role', () => {
      renderWithAuth(
        <ProtectedRoute requiredRole="consumer">
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockConsumerUser,
        }
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should deny access when user lacks required role', () => {
      renderWithAuth(
        <ProtectedRoute requiredRole="host">
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockConsumerUser,
        }
      );

      expect(screen.getByText('Unauthorized Page')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('should allow host access to consumer routes', () => {
      renderWithAuth(
        <ProtectedRoute requiredRole="consumer">
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockHostUser,
        }
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should allow admin access to all routes', () => {
      renderWithAuth(
        <ProtectedRoute requiredRole="host">
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockAdminUser,
        }
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });
  });

  describe('Permission-Based Access Control', () => {
    it('should allow access when user has required permission', () => {
      renderWithAuth(
        <ProtectedRoute requiredPermission={{ resource: 'venue', action: 'read' }}>
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockHostUser,
        }
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should deny access when user lacks required permission', () => {
      renderWithAuth(
        <ProtectedRoute requiredPermission={{ resource: 'venue', action: 'delete' }}>
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockHostUser,
        }
      );

      expect(screen.getByText('Unauthorized Page')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('should allow admin access with wildcard permissions', () => {
      renderWithAuth(
        <ProtectedRoute requiredPermission={{ resource: 'anything', action: 'delete' }}>
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockAdminUser,
        }
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });
  });

  describe('Combined Role and Permission Checks', () => {
    it('should check both role and permission requirements', () => {
      renderWithAuth(
        <ProtectedRoute 
          requiredRole="host" 
          requiredPermission={{ resource: 'venue', action: 'manage' }}
        >
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockHostUser,
        }
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should deny access if role requirement fails', () => {
      renderWithAuth(
        <ProtectedRoute 
          requiredRole="admin" 
          requiredPermission={{ resource: 'venue', action: 'read' }}
        >
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockHostUser,
        }
      );

      expect(screen.getByText('Unauthorized Page')).toBeInTheDocument();
    });

    it('should deny access if permission requirement fails', () => {
      renderWithAuth(
        <ProtectedRoute 
          requiredRole="host" 
          requiredPermission={{ resource: 'admin', action: 'delete' }}
        >
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockHostUser,
        }
      );

      expect(screen.getByText('Unauthorized Page')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing auth context gracefully', () => {
      // Render without AuthProvider to test safe auth hook
      render(
        <BrowserRouter>
          <Routes>
            <Route path="/" element={
              <ProtectedRoute>
                <TestComponent />
              </ProtectedRoute>
            } />
            <Route path="/login" element={<div>Login Page</div>} />
          </Routes>
        </BrowserRouter>
      );

      expect(screen.getByText('Login Page')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should provide accessible loading state', () => {
      renderWithAuth(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: false,
          isLoading: true,
          user: null,
        }
      );

      const loadingText = screen.getByText('Loading Bytspot...');
      expect(loadingText).toBeInTheDocument();
      
      const statusText = screen.getByText('Preparing your personalized experience');
      expect(statusText).toBeInTheDocument();
    });

    it('should have proper heading structure on unauthorized page', () => {
      renderWithAuth(
        <ProtectedRoute requiredRole="admin">
          <TestComponent />
        </ProtectedRoute>,
        {
          isAuthenticated: true,
          isLoading: false,
          user: mockConsumerUser,
        }
      );

      expect(screen.getByText('Unauthorized Page')).toBeInTheDocument();
    });
  });
});