import { useState } from 'react';
import { motion } from 'motion/react';
import { ArrowLeft, Car, User, Star, Clock, MapPin, Shield, CheckCircle, Phone, Navigation, MessageCircle, CreditCard, Check } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { PaymentFlow } from './PaymentFlow';
import { Card } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Separator } from './ui/separator';
import { Progress } from './ui/progress';

interface ValetDetailProps {
  match: {
    id: string;
    title: string;
    subtitle: string;
    distance: string;
    availability: string;
    price?: string;
    image: string;
    details: {
      description: string;
      features: string[];
    };
  };
  onBack: () => void;
}

type FlowStep = 'details' | 'booking' | 'vehicle' | 'payment' | 'confirmation' | 'tracking';

export function ValetDetail({ match, onBack }: ValetDetailProps) {
  const [currentStep, setCurrentStep] = useState<FlowStep>('details');
  const [showPayment, setShowPayment] = useState(false);
  const [bookingData, setBookingData] = useState({
    pickupLocation: '',
    vehicleType: '',
    licensePlate: '',
    specialInstructions: ''
  });

  const steps = [
    { id: 'booking', label: 'Pickup', icon: MapPin },
    { id: 'vehicle', label: 'Vehicle', icon: Car },
    { id: 'payment', label: 'Payment', icon: CreditCard },
    { id: 'confirmation', label: 'Confirm', icon: Check }
  ];

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.id === currentStep);
  };

  const mockDriver = {
    name: "James Rodriguez",
    rating: 4.9,
    eta: "3 minutes",
    vehicle: "Mercedes C-Class",
    plate: "VALET-123",
    photo: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'booking':
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold mb-2">Pickup Location</h2>
              <p className="text-gray-600 mb-4">Where should we pick up your vehicle?</p>
              
              <div className="space-y-4">
                <div>
                  <Label>Current Location</Label>
                  <Input 
                    placeholder="123 Main Street, Downtown"
                    value={bookingData.pickupLocation}
                    onChange={(e) => setBookingData(prev => ({...prev, pickupLocation: e.target.value}))}
                    className="mt-1"
                  />
                </div>
                
                <Card className="backdrop-blur-xl bg-blue-50/50 border-blue-200 p-4">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-blue-500" />
                    <div>
                      <p className="font-medium">Use Current Location</p>
                      <p className="text-sm text-gray-600">Financial District, San Francisco</p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <h3 className="font-semibold mb-3">Service Details</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Pickup</span>
                  <span className="font-medium">Within 5 minutes</span>
                </div>
                <div className="flex justify-between">
                  <span>Service Fee</span>
                  <span className="font-medium">{match.price}</span>
                </div>
                <div className="flex justify-between">
                  <span>Duration</span>
                  <span className="font-medium">As needed</span>
                </div>
              </div>
            </div>
          </div>
        );

      case 'vehicle':
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold mb-2">Vehicle Information</h2>
              <p className="text-gray-600 mb-4">Help us identify your vehicle</p>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Vehicle Type</Label>
                    <Input 
                      placeholder="e.g., Honda Civic"
                      value={bookingData.vehicleType}
                      onChange={(e) => setBookingData(prev => ({...prev, vehicleType: e.target.value}))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label>License Plate</Label>
                    <Input 
                      placeholder="ABC-1234"
                      value={bookingData.licensePlate}
                      onChange={(e) => setBookingData(prev => ({...prev, licensePlate: e.target.value}))}
                      className="mt-1"
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Special Instructions (Optional)</Label>
                  <Input 
                    placeholder="e.g., Keys in glove compartment"
                    value={bookingData.specialInstructions}
                    onChange={(e) => setBookingData(prev => ({...prev, specialInstructions: e.target.value}))}
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            <Card className="backdrop-blur-xl bg-yellow-50/50 border-yellow-200 p-4">
              <div className="flex items-start gap-3">
                <Shield className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="font-medium text-yellow-800">Insurance Coverage</p>
                  <p className="text-sm text-yellow-700">Your vehicle is fully insured during valet service up to $100,000</p>
                </div>
              </div>
            </Card>
          </div>
        );

      case 'payment':
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold mb-2">Payment Method</h2>
              <p className="text-gray-600 mb-4">Choose your payment method</p>
              
              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4 mb-4">
                <div className="flex items-center gap-3">
                  <CreditCard className="w-5 h-5 text-blue-500" />
                  <div className="flex-1">
                    <p className="font-medium">•••• •••• •••• 4242</p>
                    <p className="text-sm text-gray-600">Expires 12/26</p>
                  </div>
                  <Button variant="ghost" size="sm">Change</Button>
                </div>
              </Card>
            </div>

            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
              <h3 className="font-semibold mb-3">Service Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Valet Service</span>
                  <span>{match.price}</span>
                </div>
                <div className="flex justify-between">
                  <span>Service Fee</span>
                  <span>$3.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>$2.80</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>${(parseFloat(match.price?.replace('$', '') || '25') + 5.80).toFixed(2)}</span>
                </div>
              </div>
            </Card>

            <Card className="backdrop-blur-xl bg-green-50/50 border-green-200 p-4">
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-800">Satisfaction Guarantee</p>
                  <p className="text-sm text-green-700">100% money back if not satisfied with service</p>
                </div>
              </div>
            </Card>
          </div>
        );

      case 'confirmation':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="w-8 h-8 text-green-600" />
              </div>
              <h2 className="text-xl font-bold mb-2">Valet Requested!</h2>
              <p className="text-gray-600">Your valet is on the way</p>
            </div>

            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div 
                  className="w-12 h-12 rounded-full bg-cover bg-center"
                  style={{ backgroundImage: `url(${mockDriver.photo})` }}
                />
                <div className="flex-1">
                  <h3 className="font-semibold">{mockDriver.name}</h3>
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm">{mockDriver.rating}</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Phone className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>ETA</span>
                  <span className="font-medium text-blue-600">{mockDriver.eta}</span>
                </div>
                <div className="flex justify-between">
                  <span>Vehicle</span>
                  <span className="font-medium">{mockDriver.vehicle}</span>
                </div>
                <div className="flex justify-between">
                  <span>License</span>
                  <span className="font-medium">{mockDriver.plate}</span>
                </div>
              </div>
            </Card>

            <Card className="backdrop-blur-xl bg-blue-50/50 border-blue-200 p-4">
              <h4 className="font-semibold mb-2">What happens next?</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Valet arrives at your location</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Vehicle verification and handover</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Safe parking and real-time updates</span>
                </div>
              </div>
            </Card>
          </div>
        );

      case 'tracking':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-bold mb-2">Vehicle in Safe Hands</h2>
              <p className="text-gray-600">Track your vehicle's status</p>
            </div>

            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div 
                  className="w-12 h-12 rounded-full bg-cover bg-center"
                  style={{ backgroundImage: `url(${mockDriver.photo})` }}
                />
                <div className="flex-1">
                  <h3 className="font-semibold">{mockDriver.name}</h3>
                  <p className="text-sm text-green-600">Vehicle secured</p>
                </div>
                <Badge className="bg-green-100 text-green-800">Active</Badge>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">Vehicle picked up</p>
                    <p className="text-sm text-gray-600">2:30 PM</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">Parked safely</p>
                    <p className="text-sm text-gray-600">Premium Garage, Level 2</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-gray-400" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-500">Ready for pickup</p>
                    <p className="text-sm text-gray-400">When you're ready</p>
                  </div>
                </div>
              </div>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              <Button variant="outline" className="flex-1">
                <Phone className="w-4 h-4 mr-2" />
                Call Valet
              </Button>
              
              <Button 
                className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                onClick={() => {/* Handle retrieval */}}
              >
                Request Pickup
              </Button>
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            {/* Service Overview */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <h3 className="text-lg font-semibold mb-4">Service Features</h3>
              <div className="grid grid-cols-2 gap-4">
                {match.details.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-green-500" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Valet Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
                <Star className="w-6 h-6 text-yellow-400 fill-current mx-auto mb-2" />
                <p className="text-lg font-bold">4.9</p>
                <p className="text-xs text-gray-600">Rating</p>
              </div>
              
              <div className="text-center backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
                <Clock className="w-6 h-6 text-blue-500 mx-auto mb-2" />
                <p className="text-lg font-bold">3 min</p>
                <p className="text-xs text-gray-600">ETA</p>
              </div>
              
              <div className="text-center backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
                <Shield className="w-6 h-6 text-green-500 mx-auto mb-2" />
                <p className="text-lg font-bold">$100K</p>
                <p className="text-xs text-gray-600">Insured</p>
              </div>
            </div>

            {/* How it Works */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
              <h3 className="text-lg font-semibold mb-4">How It Works</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-blue-600 font-bold text-sm">1</span>
                  </div>
                  <div>
                    <p className="font-medium">Request pickup</p>
                    <p className="text-sm text-gray-600">Professional valet arrives within minutes</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-blue-600 font-bold text-sm">2</span>
                  </div>
                  <div>
                    <p className="font-medium">Secure parking</p>
                    <p className="text-sm text-gray-600">Vehicle parked in premium location</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-blue-600 font-bold text-sm">3</span>
                  </div>
                  <div>
                    <p className="font-medium">Return service</p>
                    <p className="text-sm text-gray-600">Vehicle returned when you're ready</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  const getNextStep = () => {
    const stepOrder: FlowStep[] = ['details', 'booking', 'vehicle', 'payment', 'confirmation', 'tracking'];
    const currentIndex = stepOrder.indexOf(currentStep);
    return currentIndex < stepOrder.length - 1 ? stepOrder[currentIndex + 1] : null;
  };

  const handleNext = () => {
    const nextStep = getNextStep();
    if (nextStep) {
      setCurrentStep(nextStep);
    }
  };

  const handleBack = () => {
    if (currentStep === 'details') {
      onBack();
    } else {
      const stepOrder: FlowStep[] = ['details', 'booking', 'vehicle', 'payment', 'confirmation', 'tracking'];
      const currentIndex = stepOrder.indexOf(currentStep);
      if (currentIndex > 0) {
        setCurrentStep(stepOrder[currentIndex - 1]);
      }
    }
  };

  const handlePaymentComplete = (paymentDetails: any) => {
    console.log('Payment completed:', paymentDetails);
    setShowPayment(false);
    setCurrentStep('confirmation');
  };

  if (showPayment) {
    return (
      <PaymentFlow
        service={{
          id: match.id,
          type: 'valet',
          title: match.title,
          subtitle: match.subtitle,
          basePrice: parseFloat(match.price?.replace('$', '') || '25'),
          location: match.distance,
          features: match.details.features
        }}
        onBack={() => setShowPayment(false)}
        onComplete={handlePaymentComplete}
      />
    );
  }

  return (
    <div className="h-full bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="h-full overflow-auto"
      >
        {/* Header */}
        <div className="sticky top-0 z-10 backdrop-blur-xl bg-white/20 border-b border-white/30 p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="text-gray-700 hover:bg-white/20"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div className="flex-1">
              <h1 className="text-lg font-semibold">{match.title}</h1>
              {currentStep !== 'details' && (
                <p className="text-sm text-gray-600">{match.price} • Available Now</p>
              )}
            </div>
          </div>
          
          {/* Progress Bar */}
          {currentStep !== 'details' && (
            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                {steps.map((step, index) => {
                  const StepIcon = step.icon;
                  const isActive = getCurrentStepIndex() >= index;
                  return (
                    <div key={step.id} className="flex flex-col items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        isActive ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-400'
                      }`}>
                        <StepIcon className="w-4 h-4" />
                      </div>
                      <span className="text-xs mt-1">{step.label}</span>
                    </div>
                  );
                })}
              </div>
              <Progress value={(getCurrentStepIndex() + 1) / steps.length * 100} className="h-2" />
            </div>
          )}
        </div>

        {/* Header Image - Only show on details step */}
        {currentStep === 'details' && (
          <div className="relative h-48 bg-cover bg-center" style={{ backgroundImage: `url(${match.image})` }}>
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
            <div className="absolute bottom-4 left-4 right-4">
              <div className="flex items-center gap-2 mb-2">
                <Badge className="bg-green-500 text-white">Available Now</Badge>
                <Badge variant="outline" className="bg-white/20 border-white/30 text-white">
                  {match.distance}
                </Badge>
              </div>
              <h1 className="text-2xl font-bold text-white">{match.title}</h1>
              <p className="text-white/80">{match.subtitle}</p>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {renderStepContent()}
        </div>

        {/* Action Button */}
        {currentStep !== 'tracking' && (
          <div className="sticky bottom-0 p-6 bg-gradient-to-t from-white via-white/95 to-transparent backdrop-blur-sm">
            <Button
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
              size="lg"
              onClick={currentStep === 'details' ? () => setCurrentStep('booking') : 
                       currentStep === 'vehicle' ? () => setShowPayment(true) : handleNext}
            >
              {currentStep === 'details' ? `Book Valet • ${match.price}` : 
               currentStep === 'confirmation' ? 'Start Tracking' : 
               'Continue'}
            </Button>
          </div>
        )}
      </motion.div>
    </div>
  );
}