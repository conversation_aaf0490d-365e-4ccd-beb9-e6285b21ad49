import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Share2, 
  Trophy, 
  Users, 
  Gift, 
  Star, 
  Crown, 
  Copy, 
  Check,
  TrendingUp,
  Camera,
  MessageCircle,
  Heart,
  Award,
  Target,
  ChevronRight,
  ExternalLink,
  Sparkles,
  Zap,
  Badge as BadgeIcon
} from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Separator } from './ui/separator';
import { useAnalytics } from '../lib/hooks/useAnalytics';

interface SocialHubProps {
  userProfile: {
    name: string;
    vibeScore: number;
    visitedSpots: number;
    friendsReferred: number;
    points: number;
    tier: string;
    achievements: Achievement[];
  };
  onShareVibeScore: (platform: string) => void;
  onStartChallenge: (challenge: Challenge) => void;
  onInviteFriend: () => void;
  onViewProfile: () => void;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt?: Date;
  progress?: number;
  maxProgress?: number;
}

interface Challenge {
  id: string;
  title: string;
  description: string;
  reward: number;
  deadline: Date;
  progress: number;
  maxProgress: number;
  type: 'venue' | 'social' | 'vibe';
}

interface RecentActivity {
  id: string;
  type: 'vibe_score' | 'visit' | 'referral' | 'achievement';
  description: string;
  points: number;
  timestamp: Date;
  shareableContent?: {
    title: string;
    description: string;
    vibeScore?: number;
    venue?: string;
  };
}

export function SocialHub({ userProfile, onShareVibeScore, onStartChallenge, onInviteFriend, onViewProfile }: SocialHubProps) {
  const { track } = useAnalytics();
  const [copiedReferral, setCopiedReferral] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<RecentActivity | null>(null);
  
  // Mock data - replace with real data from your backend
  const challenges: Challenge[] = [
    {
      id: 'top10-atlanta',
      title: 'Atlanta Top 10 Challenge',
      description: 'Visit 10 of the hottest spots in Atlanta this month',
      reward: 500,
      deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days
      progress: userProfile.visitedSpots,
      maxProgress: 10,
      type: 'venue'
    },
    {
      id: 'vibe-master',
      title: 'Vibe Master',
      description: 'Share 5 perfect vibe scores this week',
      reward: 250,
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      progress: 3,
      maxProgress: 5,
      type: 'vibe'
    },
    {
      id: 'social-connector',
      title: 'Social Connector',
      description: 'Invite 3 friends to join Bytspot',
      reward: 750,
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      progress: userProfile.friendsReferred,
      maxProgress: 3,
      type: 'social'
    }
  ];

  const recentActivity: RecentActivity[] = [
    {
      id: '1',
      type: 'vibe_score',
      description: 'Perfect 10 Vibe Score at Rooftop Lounge',
      points: 100,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      shareableContent: {
        title: 'Perfect Vibe Score! 🎉',
        description: 'Just experienced an amazing 10/10 vibe at Rooftop Lounge! The energy was electric!',
        vibeScore: 10,
        venue: 'Rooftop Lounge'
      }
    },
    {
      id: '2',
      type: 'achievement',
      description: 'Unlocked "Nightlife Explorer" badge',
      points: 150,
      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000)
    },
    {
      id: '3',
      type: 'referral',
      description: 'Sarah joined through your referral',
      points: 200,
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000)
    }
  ];

  const referralCode = `BYTSPOT-${userProfile.name.toUpperCase().replace(' ', '')}-${Math.random().toString(36).substr(2, 4)}`;

  const handleCopyReferralCode = async () => {
    try {
      await navigator.clipboard.writeText(referralCode);
      setCopiedReferral(true);
      track('referral_code_copied', { code: referralCode });
      setTimeout(() => setCopiedReferral(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleShareVibeScore = (platform: string, activity?: RecentActivity) => {
    track('vibe_score_share_initiated', {
      platform,
      vibe_score: activity?.shareableContent?.vibeScore || userProfile.vibeScore,
      venue: activity?.shareableContent?.venue
    });
    onShareVibeScore(platform);
  };

  const handleStartChallenge = (challenge: Challenge) => {
    track('challenge_started', {
      challenge_id: challenge.id,
      challenge_type: challenge.type
    });
    onStartChallenge(challenge);
  };

  const getVibeScoreColor = (score: number) => {
    if (score >= 9) return 'from-green-500 to-emerald-600';
    if (score >= 7) return 'from-yellow-500 to-orange-500';
    if (score >= 5) return 'from-orange-500 to-red-500';
    return 'from-red-500 to-pink-500';
  };

  const getChallengeTypeIcon = (type: Challenge['type']) => {
    switch (type) {
      case 'venue': return Trophy;
      case 'vibe': return Star;
      case 'social': return Users;
      default: return Target;
    }
  };

  return (
    <div className="h-full bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100 overflow-auto">
      <div className="p-6 space-y-6">
        {/* Hero Section - Vibe Score Sharing */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <div className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className={`w-24 h-24 mx-auto rounded-full bg-gradient-to-r ${getVibeScoreColor(userProfile.vibeScore)} flex items-center justify-center shadow-2xl`}
            >
              <span className="text-2xl font-bold text-white">{userProfile.vibeScore}</span>
            </motion.div>
            
            <div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Your Vibe Score</h2>
              <p className="text-gray-600">Share your perfect vibe scores and become the go-to spot finder for your friends!</p>
            </div>

            {userProfile.vibeScore >= 8 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-2xl p-4 border border-yellow-400/30"
              >
                <div className="flex items-center gap-3 mb-3">
                  <Crown className="w-6 h-6 text-yellow-500" />
                  <span className="font-semibold text-gray-800">High Vibe Alert! ⚡</span>
                </div>
                <p className="text-sm text-gray-700 mb-4">
                  Your vibe score of {userProfile.vibeScore}/10 is amazing! Share it to show your friends where the best spots are.
                </p>
                
                <div className="grid grid-cols-3 gap-2">
                  {['Instagram', 'Twitter', 'TikTok'].map((platform) => (
                    <Button
                      key={platform}
                      size="sm"
                      variant="outline"
                      className="bg-white/30 border-white/40 hover:bg-white/50"
                      onClick={() => handleShareVibeScore(platform)}
                    >
                      <Share2 className="w-4 h-4 mr-1" />
                      {platform}
                    </Button>
                  ))}
                </div>
              </motion.div>
            )}
          </div>
        </Card>

        {/* Active Challenges */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <div className="flex items-center gap-3 mb-4">
            <Trophy className="w-6 h-6 text-yellow-500" />
            <h3 className="text-xl font-semibold text-gray-800">Active Challenges</h3>
          </div>
          
          <div className="space-y-4">
            {challenges.map((challenge) => {
              const Icon = getChallengeTypeIcon(challenge.type);
              const progress = (challenge.progress / challenge.maxProgress) * 100;
              const daysLeft = Math.ceil((challenge.deadline.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
              
              return (
                <motion.div
                  key={challenge.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="bg-white/30 rounded-2xl p-4 border border-white/40"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <Icon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800">{challenge.title}</h4>
                        <p className="text-sm text-gray-600">{challenge.description}</p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-yellow-600">
                        <Star className="w-4 h-4 fill-current" />
                        <span className="font-semibold">{challenge.reward}</span>
                      </div>
                      <p className="text-xs text-gray-500">{daysLeft} days left</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Progress</span>
                      <span className="font-medium">{challenge.progress}/{challenge.maxProgress}</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>
                  
                  {progress < 100 && (
                    <Button
                      size="sm"
                      className="w-full mt-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                      onClick={() => handleStartChallenge(challenge)}
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      Continue Challenge
                    </Button>
                  )}
                </motion.div>
              );
            })}
          </div>
        </Card>

        {/* Referral Program */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <div className="flex items-center gap-3 mb-4">
            <Gift className="w-6 h-6 text-green-500" />
            <h3 className="text-xl font-semibold text-gray-800">Invite Friends</h3>
          </div>
          
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-2xl p-4 border border-green-400/30">
              <div className="text-center space-y-3">
                <div className="text-2xl">🎁</div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1">Double Rewards!</h4>
                  <p className="text-sm text-gray-600">Give 10 Bytspot Points, Get 10 Bytspot Points</p>
                </div>
                
                <div className="bg-white/50 rounded-xl p-3">
                  <div className="flex items-center justify-between">
                    <div className="text-left">
                      <p className="text-xs text-gray-600">Your Referral Code</p>
                      <p className="font-mono font-bold text-gray-800">{referralCode}</p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      className="bg-white/50 border-gray-300 hover:bg-white/70"
                      onClick={handleCopyReferralCode}
                    >
                      {copiedReferral ? (
                        <Check className="w-4 h-4 text-green-600" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>
                
                <Button
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                  onClick={onInviteFriend}
                >
                  <Users className="w-4 h-4 mr-2" />
                  Invite Friends
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">{userProfile.friendsReferred}</div>
                <div className="text-sm text-gray-600">Friends Referred</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">{userProfile.friendsReferred * 20}</div>
                <div className="text-sm text-gray-600">Points Earned</div>
              </div>
            </div>
          </div>
        </Card>

        {/* Recent Social Activity */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="w-6 h-6 text-blue-500" />
            <h3 className="text-xl font-semibold text-gray-800">Recent Activity</h3>
          </div>
          
          <div className="space-y-3">
            {recentActivity.map((activity) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/30 rounded-xl p-4 border border-white/40"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      {activity.type === 'vibe_score' && <Star className="w-4 h-4 text-white" />}
                      {activity.type === 'achievement' && <Trophy className="w-4 h-4 text-white" />}
                      {activity.type === 'referral' && <Users className="w-4 h-4 text-white" />}
                      {activity.type === 'visit' && <MapPin className="w-4 h-4 text-white" />}
                    </div>
                    <div>
                      <p className="font-medium text-gray-800 text-sm">{activity.description}</p>
                      <p className="text-xs text-gray-500">
                        {activity.timestamp.toLocaleDateString()} • {activity.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-green-600 font-semibold text-sm">+{activity.points}</div>
                    {activity.shareableContent && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 px-2 text-xs"
                        onClick={() => setSelectedActivity(activity)}
                      >
                        <Share2 className="w-3 h-3 mr-1" />
                        Share
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>

        {/* Friend Tags & Social Features */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <div className="flex items-center gap-3 mb-4">
            <Camera className="w-6 h-6 text-pink-500" />
            <h3 className="text-xl font-semibold text-gray-800">Social Features</h3>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="outline"
              className="h-20 flex-col gap-2 bg-white/30 border-white/40 hover:bg-white/50"
            >
              <Users className="w-6 h-6 text-blue-500" />
              <span className="text-sm">Tag Friends</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-20 flex-col gap-2 bg-white/30 border-white/40 hover:bg-white/50"
            >
              <Camera className="w-6 h-6 text-pink-500" />
              <span className="text-sm">Story Stickers</span>
            </Button>
          </div>
        </Card>
      </div>

      {/* Share Activity Modal */}
      <AnimatePresence>
        {selectedActivity && selectedActivity.shareableContent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedActivity(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-2xl p-6 max-w-sm w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center space-y-4">
                <div className="text-4xl">🎉</div>
                <div>
                  <h3 className="text-xl font-bold mb-2">{selectedActivity.shareableContent.title}</h3>
                  <p className="text-gray-600">{selectedActivity.shareableContent.description}</p>
                </div>
                
                <div className="grid grid-cols-3 gap-2">
                  {['Instagram', 'Twitter', 'TikTok'].map((platform) => (
                    <Button
                      key={platform}
                      size="sm"
                      className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                      onClick={() => {
                        handleShareVibeScore(platform, selectedActivity);
                        setSelectedActivity(null);
                      }}
                    >
                      {platform}
                    </Button>
                  ))}
                </div>
                
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setSelectedActivity(null)}
                >
                  Cancel
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}