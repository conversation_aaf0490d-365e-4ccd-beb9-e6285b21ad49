import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Search, 
  MapPin, 
  Star, 
  Clock, 
  Users, 
  Music, 
  Coffee, 
  Wine, 
  Heart,
  X,
  Crown,
  Sparkles,
  Filter,
  Settings,
  ArrowRight,
  TrendingUp,
  Zap,
  Target,
  Lock,
  Check,
  Volume2,
  VolumeX,
  DollarSign,
  Car,
  Calendar,
  Sun,
  Moon,
  Edit2,
  ChevronDown,
  Volume1,
  Utensils,
  Timer,
  Eye,
  Camera,
  Share2,
  Navigation,
  Bookmark,
  Phone,
  ChevronRight,
  Activity,
  Flame,
  UserCheck,
  Gift,
  AlertCircle,
  Percent,
  MessageSquare,
  Image,
  Play
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Slider } from './ui/slider';
import { Switch } from './ui/switch';
import { Separator } from './ui/separator';

interface VenueInsiderProps {
  onVibePreferencesComplete?: (preferences: {
    atmosphere: string[];
    musicStyle: string[];
    crowdEnergy: number;
  }) => void;
}

type VibePreferenceStep = 'intro' | 'location' | 'atmosphere' | 'music-cuisine' | 'time' | 'review';

interface VibePreferences {
  // Location & Basics
  location: string;
  distance: number;
  minRating: number;
  priceRanges: {
    venue: [number, number];
    parking: [number, number];
    valet: [number, number];
  };
  
  // Atmosphere & Vibe
  atmosphere: string[];
  musicStyle: string[];
  crowdEnergy: number;
  noiseLevel: number;
  
  // Music & Cuisine
  cuisineTypes: string[];
  
  // Time Preferences
  timePreferences: string[];
  dayPreferences: string[];
  
  // Legacy
  activities: string[];
}

interface Venue {
  id: string;
  name: string;
  image: string;
  vibe: string;
  rating: number;
  distance: string;
  price: string;
  atmosphere: string[];
  features: string[];
  isPremium?: boolean;
  liveData: {
    currentCrowd: number;
    maxCapacity: number;
    averageWait: string;
    trending: string;
    lastUpdated: string;
    peakHours: string[];
    hotSpots: string[];
    liveEvents: string[];
    crowdAge: string;
    moodScore: number;
    checkIns: number;
    photos: number;
  };
  insights: {
    whyVisit: string;
    bestTime: string;
    insider: string;
    trending: string;
    friendActivity: string;
    offers: string[];
  };
}

const mockVenues: Venue[] = [
  {
    id: '1',
    name: 'Neon Nights',
    image: 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800',
    vibe: 'High Energy',
    rating: 4.8,
    distance: '0.3 mi',
    price: '$$',
    atmosphere: ['Energetic', 'Modern', 'Trendy'],
    features: ['Live DJ', 'Dance Floor', 'Rooftop'],
    liveData: {
      currentCrowd: 87,
      maxCapacity: 150,
      averageWait: '5 min',
      trending: '+23% vs last week',
      lastUpdated: 'Just now',
      peakHours: ['9-11 PM'],
      hotSpots: ['Rooftop Bar', 'Main Dance Floor'],
      liveEvents: ['DJ Marcus Tonight', 'Happy Hour 6-8 PM'],
      crowdAge: '25-35',
      moodScore: 8.4,
      checkIns: 45,
      photos: 127
    },
    insights: {
      whyVisit: 'Perfect high-energy atmosphere matching your preferences',
      bestTime: 'Visit before 9 PM to skip the line',
      insider: 'Ask for Marcus - he knows the best cocktails',
      trending: 'Most popular venue in your area this week',
      friendActivity: '3 friends visited this week',
      offers: ['20% off drinks before 8 PM', '🎉 Free entry with student ID']
    }
  },
  {
    id: '2',
    name: 'Whisper Lounge',
    image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
    vibe: 'Intimate',
    rating: 4.6,
    distance: '0.5 mi',
    price: '$$$',
    atmosphere: ['Cozy', 'Romantic', 'Upscale'],
    features: ['Wine Bar', 'Private Booths', 'Jazz Music'],
    liveData: {
      currentCrowd: 34,
      maxCapacity: 80,
      averageWait: '2 min',
      trending: 'Steady',
      lastUpdated: '2 mins ago',
      peakHours: ['7-9 PM'],
      hotSpots: ['Wine Cellar', 'Private Booths'],
      liveEvents: ['Jazz Trio 8-11 PM', 'Wine Tasting 6 PM'],
      crowdAge: '30-45',
      moodScore: 9.1,
      checkIns: 12,
      photos: 89
    },
    insights: {
      whyVisit: 'Perfect intimate setting for sophisticated evenings',
      bestTime: 'Reserve a booth for the jazz performance',
      insider: 'Try their signature wine flight - locally curated',
      trending: 'Hidden gem with consistently high ratings',
      friendActivity: '1 friend checked in yesterday',
      offers: ['Wine flight $25 (reg $35)', '🍷 Sommelier recommendations']
    }
  },
  {
    id: '3',
    name: 'Electric Garden',
    image: 'https://images.unsplash.com/photo-1559339352-11d035aa65de?w=800',
    vibe: 'Vibrant',
    rating: 4.9,
    distance: '0.7 mi',
    price: '$$',
    atmosphere: ['Colorful', 'Artistic', 'Unique'],
    features: ['Outdoor Space', 'Art Installations', 'Craft Cocktails'],
    isPremium: true,
    liveData: {
      currentCrowd: 62,
      maxCapacity: 120,
      averageWait: '8 min',
      trending: '+45% vs last week',
      lastUpdated: 'Just now',
      peakHours: ['8-10 PM'],
      hotSpots: ['Art Garden', 'Cocktail Lab'],
      liveEvents: ['Art Exhibition Opens Today', 'Mixology Class 7 PM'],
      crowdAge: '22-35',
      moodScore: 9.3,
      checkIns: 73,
      photos: 234
    },
    insights: {
      whyVisit: 'Unique artistic venue with Instagram-worthy installations',
      bestTime: 'Golden hour photos are incredible (6-7 PM)',
      insider: 'New art exhibition just opened - first week exclusive',
      trending: '#1 trending venue in your area',
      friendActivity: '5 friends want to visit this place',
      offers: ['Free mixology class with drink purchase', '🎨 Artist meet & greet tonight']
    }
  }
];

const atmosphereOptions = [
  { id: 'energetic', label: 'Energetic', emoji: '⚡', color: 'from-yellow-400 to-orange-500' },
  { id: 'romantic', label: 'Romantic', emoji: '💕', color: 'from-pink-400 to-red-500' },
  { id: 'chill', label: 'Chill', emoji: '😌', color: 'from-blue-400 to-cyan-500' },
  { id: 'sophisticated', label: 'Sophisticated', emoji: '🥂', color: 'from-purple-400 to-indigo-500' },
  { id: 'artsy', label: 'Artsy', emoji: '🎨', color: 'from-green-400 to-teal-500' },
  { id: 'trendy', label: 'Trendy', emoji: '✨', color: 'from-rose-400 to-pink-500' }
];

const musicOptions = [
  { id: 'electronic', label: 'Electronic', emoji: '🎧' },
  { id: 'jazz', label: 'Jazz', emoji: '🎷' },
  { id: 'indie', label: 'Indie', emoji: '🎸' },
  { id: 'hip-hop', label: 'Hip Hop', emoji: '🎤' },
  { id: 'classical', label: 'Classical', emoji: '🎼' },
  { id: 'ambient', label: 'Ambient', emoji: '🌙' }
];

const cuisineOptions = [
  { id: 'italian', label: 'Italian', emoji: '🍝', color: 'from-green-400 to-red-500' },
  { id: 'japanese', label: 'Japanese', emoji: '🍱', color: 'from-red-400 to-pink-500' },
  { id: 'mexican', label: 'Mexican', emoji: '🌮', color: 'from-yellow-400 to-red-500' },
  { id: 'american', label: 'American', emoji: '🍔', color: 'from-red-500 to-yellow-500' },
  { id: 'thai', label: 'Thai', emoji: '🍜', color: 'from-orange-400 to-red-500' },
  { id: 'mediterranean', label: 'Mediterranean', emoji: '🥙', color: 'from-blue-400 to-green-500' },
  { id: 'indian', label: 'Indian', emoji: '🍛', color: 'from-orange-500 to-red-600' },
  { id: 'french', label: 'French', emoji: '🥐', color: 'from-purple-400 to-pink-500' }
];

const timeOptions = [
  { id: 'morning', label: 'Morning', range: '6AM - 11AM', icon: Sun, color: 'from-yellow-400 to-orange-500' },
  { id: 'lunch', label: 'Lunch', range: '11AM - 3PM', icon: Coffee, color: 'from-orange-400 to-yellow-600' },
  { id: 'afternoon', label: 'Afternoon', range: '3PM - 6PM', icon: Sun, color: 'from-blue-400 to-cyan-500' },
  { id: 'dinner', label: 'Dinner', range: '6PM - 10PM', icon: Utensils, color: 'from-purple-400 to-pink-500' },
  { id: 'late-night', label: 'Late Night', range: '10PM - 2AM', icon: Moon, color: 'from-indigo-500 to-purple-600' }
];

const dayOptions = [
  { id: 'weekdays', label: 'Weekdays', description: 'Mon - Fri', color: 'from-blue-500 to-cyan-500' },
  { id: 'weekends', label: 'Weekends', description: 'Sat - Sun', color: 'from-purple-500 to-pink-500' },
  { id: 'both', label: 'Any Day', description: 'Flexible', color: 'from-green-500 to-teal-500' }
];

const crowdEnergyLevels = [
  { value: 20, label: 'Calm', emoji: '😌', description: 'Quiet & Peaceful', color: 'from-blue-400 to-cyan-500' },
  { value: 50, label: 'Medium', emoji: '🙂', description: 'Moderate Energy', color: 'from-green-400 to-blue-500' },
  { value: 80, label: 'High Energy', emoji: '🔥', description: 'Lively & Vibrant', color: 'from-orange-500 to-red-500' }
];

const noiseLevels = [
  { value: 25, icon: VolumeX, label: 'Quiet', description: 'Perfect for conversation' },
  { value: 50, icon: Volume1, label: 'Moderate', description: 'Comfortable background' },
  { value: 75, icon: Volume2, label: 'Lively', description: 'Energetic atmosphere' }
];

export function VenueInsider({ onVibePreferencesComplete }: VenueInsiderProps = {}) {
  const [isPremium, setIsPremium] = useState(false);
  const [showPremiumFlow, setShowPremiumFlow] = useState(false);
  const [currentStep, setCurrentStep] = useState<VibePreferenceStep>('intro');
  const [preferences, setPreferences] = useState<VibePreferences>({
    location: '',
    distance: 25,
    minRating: 3,
    priceRanges: {
      venue: [20, 100],
      parking: [20, 100],
      valet: [20, 100]
    },
    atmosphere: [],
    musicStyle: [],
    crowdEnergy: 50,
    noiseLevel: 50,
    cuisineTypes: [],
    timePreferences: [],
    dayPreferences: [],
    activities: []
  });

  const handleAtmosphereToggle = (atmosphereId: string) => {
    setPreferences(prev => ({
      ...prev,
      atmosphere: prev.atmosphere.includes(atmosphereId)
        ? prev.atmosphere.filter(id => id !== atmosphereId)
        : [...prev.atmosphere, atmosphereId]
    }));
  };

  const handleMusicToggle = (musicId: string) => {
    setPreferences(prev => ({
      ...prev,
      musicStyle: prev.musicStyle.includes(musicId)
        ? prev.musicStyle.filter(id => id !== musicId)
        : [...prev.musicStyle, musicId]
    }));
  };

  const handleCuisineToggle = (cuisineId: string) => {
    setPreferences(prev => ({
      ...prev,
      cuisineTypes: prev.cuisineTypes.includes(cuisineId)
        ? prev.cuisineTypes.filter(id => id !== cuisineId)
        : [...prev.cuisineTypes, cuisineId]
    }));
  };

  const handleTimeToggle = (timeId: string) => {
    setPreferences(prev => ({
      ...prev,
      timePreferences: prev.timePreferences.includes(timeId)
        ? prev.timePreferences.filter(id => id !== timeId)
        : [...prev.timePreferences, timeId]
    }));
  };

  const handleDayToggle = (dayId: string) => {
    setPreferences(prev => ({
      ...prev,
      dayPreferences: prev.dayPreferences.includes(dayId)
        ? prev.dayPreferences.filter(id => id !== dayId)
        : [...prev.dayPreferences, dayId]
    }));
  };

  const getVenueCount = (distance: number) => {
    // Mock venue count based on distance
    if (distance <= 10) return Math.floor(8 + distance * 2);
    if (distance <= 25) return Math.floor(12 + distance * 1.5);
    return Math.floor(15 + distance * 1.2);
  };

  const handleCompleteSetup = () => {
    if (onVibePreferencesComplete) {
      // Call the callback with properly structured preferences
      onVibePreferencesComplete({
        atmosphere: preferences.atmosphere,
        musicStyle: preferences.musicStyle,
        crowdEnergy: preferences.crowdEnergy
      });
    }
  };

  const renderPremiumCard = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative mb-6"
    >
      <Card className="relative overflow-hidden border-2 border-purple-200 bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-blue-600/10" />
        
        <div className="relative p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Vibe Premium
                </h3>
                <p className="text-sm text-gray-600">Unlock AI-powered discovery</p>
              </div>
            </div>
            <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0">
              <Sparkles className="w-3 h-3 mr-1" />
              50% OFF
            </Badge>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-6">
            {[
              { icon: Target, text: 'Smart Matching' },
              { icon: Zap, text: 'Instant Discovery' },
              { icon: Heart, text: 'Vibe Learning' },
              { icon: Lock, text: 'Exclusive Venues' }
            ].map((feature, index) => (
              <div key={index} className="flex items-center gap-2">
                <feature.icon className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-700">{feature.text}</span>
              </div>
            ))}
          </div>

          <Button
            onClick={() => setShowPremiumFlow(true)}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 h-12"
          >
            <Crown className="w-4 h-4 mr-2" />
            Start Premium Discovery - $4.99/month
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </Card>
    </motion.div>
  );

  const renderVibePreferenceFlow = () => {
    switch (currentStep) {
      case 'intro':
        return (
          <div className="text-center space-y-6">
            <div className="w-20 h-20 mx-auto rounded-3xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <Heart className="w-10 h-10 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800 mb-3">
                Let's Find Your Perfect Vibe
              </h2>
              <p className="text-gray-600 max-w-md mx-auto">
                We'll create your personalized discovery profile across 4 comprehensive sections.
              </p>
            </div>
            <Button
              onClick={() => setCurrentStep('location')}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 h-12"
            >
              Get Started
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        );

      case 'location':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-800 mb-2">
                Location & Basics
              </h2>
              <p className="text-gray-600">Set your discovery parameters</p>
            </div>

            {/* Location Display */}
            <Card className="p-4 border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <MapPin className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="font-medium text-gray-800">Current Location</p>
                    <p className="text-sm text-gray-600">San Francisco, CA</p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  <Edit2 className="w-4 h-4 mr-2" />
                  Change
                </Button>
              </div>
            </Card>

            {/* Distance Slider */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="font-medium text-gray-800">Search Radius</label>
                <Badge className="bg-blue-100 text-blue-800">
                  {getVenueCount(preferences.distance)} venues found
                </Badge>
              </div>
              <div className="px-2">
                <Slider
                  value={[preferences.distance]}
                  onValueChange={(value) => setPreferences(prev => ({ ...prev, distance: value[0] }))}
                  max={50}
                  min={5}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-2">
                  <span>5 km</span>
                  <span className="font-medium">{preferences.distance} km</span>
                  <span>50 km</span>
                </div>
              </div>
            </div>

            {/* Star Rating Selector */}
            <div className="space-y-4">
              <label className="font-medium text-gray-800">Minimum Rating</label>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <motion.button
                    key={rating}
                    onClick={() => setPreferences(prev => ({ ...prev, minRating: rating }))}
                    className={`flex-1 p-3 rounded-xl border-2 transition-all duration-200 ${
                      preferences.minRating >= rating
                        ? 'border-yellow-400 bg-yellow-50'
                        : 'border-gray-200 hover:border-yellow-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Star className={`w-6 h-6 mx-auto ${preferences.minRating >= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                    <span className="text-xs text-gray-600 mt-1 block">{rating}+</span>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Price Range Selectors */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Price Ranges</h3>
              
              {/* Venue Prices */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <span className="text-sm">🎉</span>
                  </div>
                  <span className="text-sm font-medium text-gray-700">Venues</span>
                </div>
                <div className="px-2">
                  <Slider
                    value={preferences.priceRanges.venue}
                    onValueChange={(value) => setPreferences(prev => ({
                      ...prev,
                      priceRanges: { ...prev.priceRanges, venue: value as [number, number] }
                    }))}
                    max={200}
                    min={10}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>${preferences.priceRanges.venue[0]}</span>
                    <span>${preferences.priceRanges.venue[1]}</span>
                  </div>
                </div>
              </div>

              {/* Parking Prices */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Car className="w-4 h-4 text-blue-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">Parking (per hour)</span>
                </div>
                <div className="px-2">
                  <Slider
                    value={preferences.priceRanges.parking}
                    onValueChange={(value) => setPreferences(prev => ({
                      ...prev,
                      priceRanges: { ...prev.priceRanges, parking: value as [number, number] }
                    }))}
                    max={50}
                    min={5}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>${preferences.priceRanges.parking[0]}</span>
                    <span>${preferences.priceRanges.parking[1]}</span>
                  </div>
                </div>
              </div>

              {/* Valet Prices */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <span className="text-sm">🔑</span>
                  </div>
                  <span className="text-sm font-medium text-gray-700">Valet Service</span>
                </div>
                <div className="px-2">
                  <Slider
                    value={preferences.priceRanges.valet}
                    onValueChange={(value) => setPreferences(prev => ({
                      ...prev,
                      priceRanges: { ...prev.priceRanges, valet: value as [number, number] }
                    }))}
                    max={100}
                    min={15}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>${preferences.priceRanges.valet[0]}</span>
                    <span>${preferences.priceRanges.valet[1]}</span>
                  </div>
                </div>
              </div>
            </div>

            <Button
              onClick={() => setCurrentStep('atmosphere')}
              className="w-full bg-purple-500 hover:bg-purple-600 h-12"
            >
              Continue
            </Button>
          </div>
        );

      case 'atmosphere':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-800 mb-2">
                Atmosphere & Vibe
              </h2>
              <p className="text-gray-600">Define your ideal environment</p>
            </div>

            {/* Atmosphere Selection */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Preferred Atmosphere</h3>
              <div className="grid grid-cols-2 gap-3">
                {atmosphereOptions.map((option) => (
                  <motion.button
                    key={option.id}
                    onClick={() => handleAtmosphereToggle(option.id)}
                    className={`p-4 rounded-2xl border-2 transition-all duration-200 ${
                      preferences.atmosphere.includes(option.id)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className={`w-10 h-10 mx-auto rounded-xl bg-gradient-to-r ${option.color} flex items-center justify-center mb-2`}>
                      <span className="text-lg">{option.emoji}</span>
                    </div>
                    <span className="text-sm font-medium text-gray-800">{option.label}</span>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Crowd Energy Selection */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Crowd Energy Level</h3>
              <div className="grid grid-cols-3 gap-2">
                {crowdEnergyLevels.map((level) => (
                  <motion.button
                    key={level.value}
                    onClick={() => setPreferences(prev => ({ ...prev, crowdEnergy: level.value }))}
                    className={`p-4 rounded-2xl border-2 transition-all duration-200 text-center ${
                      Math.abs(preferences.crowdEnergy - level.value) < 20
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className={`w-12 h-12 mx-auto rounded-xl bg-gradient-to-r ${level.color} flex items-center justify-center mb-2`}>
                      <span className="text-xl">{level.emoji}</span>
                    </div>
                    <div className="text-sm font-medium text-gray-800">{level.label}</div>
                    <div className="text-xs text-gray-600">{level.description}</div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Noise Level Selection */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Noise Level Preference</h3>
              <div className="grid grid-cols-3 gap-2">
                {noiseLevels.map((level) => (
                  <motion.button
                    key={level.value}
                    onClick={() => setPreferences(prev => ({ ...prev, noiseLevel: level.value }))}
                    className={`p-4 rounded-2xl border-2 transition-all duration-200 text-center ${
                      Math.abs(preferences.noiseLevel - level.value) < 15
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <level.icon className="w-6 h-6 mx-auto text-gray-600 mb-2" />
                    <div className="text-sm font-medium text-gray-800">{level.label}</div>
                    <div className="text-xs text-gray-600">{level.description}</div>
                  </motion.button>
                ))}
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setCurrentStep('location')}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={() => setCurrentStep('music-cuisine')}
                disabled={preferences.atmosphere.length === 0}
                className="flex-1 bg-purple-500 hover:bg-purple-600"
              >
                Continue
              </Button>
            </div>
          </div>
        );

      case 'music-cuisine':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-800 mb-2">
                Music & Cuisine
              </h2>
              <p className="text-gray-600">Your entertainment and dining preferences</p>
            </div>

            {/* Music Genres */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Music Genres</h3>
              <div className="grid grid-cols-3 gap-2">
                {musicOptions.map((option) => (
                  <motion.button
                    key={option.id}
                    onClick={() => handleMusicToggle(option.id)}
                    className={`p-3 rounded-xl border-2 transition-all duration-200 text-center ${
                      preferences.musicStyle.includes(option.id)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="text-2xl mb-1">{option.emoji}</div>
                    <div className="text-xs font-medium text-gray-800">{option.label}</div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Cuisine Types */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Cuisine Preferences</h3>
              <div className="grid grid-cols-2 gap-2">
                {cuisineOptions.map((cuisine) => (
                  <motion.button
                    key={cuisine.id}
                    onClick={() => handleCuisineToggle(cuisine.id)}
                    className={`p-3 rounded-xl border-2 transition-all duration-200 text-center ${
                      preferences.cuisineTypes.includes(cuisine.id)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="text-2xl mb-1">{cuisine.emoji}</div>
                    <div className="text-sm font-medium text-gray-800">{cuisine.label}</div>
                  </motion.button>
                ))}
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setCurrentStep('atmosphere')}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={() => setCurrentStep('time')}
                className="flex-1 bg-purple-500 hover:bg-purple-600"
              >
                Continue
              </Button>
            </div>
          </div>
        );

      case 'time':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-800 mb-2">
                Time Preferences
              </h2>
              <p className="text-gray-600">When do you like to explore?</p>
            </div>

            {/* Time of Day Selection */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Preferred Times</h3>
              <div className="space-y-2">
                {timeOptions.map((time) => (
                  <motion.button
                    key={time.id}
                    onClick={() => handleTimeToggle(time.id)}
                    className={`w-full p-4 rounded-xl border-2 transition-all duration-200 ${
                      preferences.timePreferences.includes(time.id)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${time.color} flex items-center justify-center`}>
                        <time.icon className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-medium text-gray-800">{time.label}</div>
                        <div className="text-sm text-gray-600">{time.range}</div>
                      </div>
                      {preferences.timePreferences.includes(time.id) && (
                        <Check className="w-5 h-5 text-purple-500" />
                      )}
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Day of Week Selection */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Day Preferences</h3>
              <div className="grid grid-cols-3 gap-2">
                {dayOptions.map((day) => (
                  <motion.button
                    key={day.id}
                    onClick={() => handleDayToggle(day.id)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 text-center ${
                      preferences.dayPreferences.includes(day.id)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className={`w-10 h-10 mx-auto rounded-xl bg-gradient-to-r ${day.color} flex items-center justify-center mb-2`}>
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    <div className="text-sm font-medium text-gray-800">{day.label}</div>
                    <div className="text-xs text-gray-600">{day.description}</div>
                  </motion.button>
                ))}
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setCurrentStep('music-cuisine')}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={() => setCurrentStep('review')}
                disabled={preferences.timePreferences.length === 0 || preferences.dayPreferences.length === 0}
                className="flex-1 bg-purple-500 hover:bg-purple-600"
              >
                Continue
              </Button>
            </div>
          </div>
        );

      case 'review':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-800 mb-2">
                Review Your Preferences
              </h2>
              <p className="text-gray-600">Confirm your vibe profile</p>
            </div>

            {/* Preferences Summary */}
            <div className="space-y-4">
              <Card className="p-4 border border-gray-200">
                <h4 className="font-medium text-gray-800 mb-3">Location & Basics</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div>🎯 Search within {preferences.distance} km</div>
                  <div>⭐ Minimum {preferences.minRating} star rating</div>
                  <div>💰 Venues: ${preferences.priceRanges.venue[0]}-${preferences.priceRanges.venue[1]}</div>
                </div>
              </Card>

              <Card className="p-4 border border-gray-200">
                <h4 className="font-medium text-gray-800 mb-3">Atmosphere & Vibe</h4>
                <div className="flex flex-wrap gap-1 mb-2">
                  {preferences.atmosphere.map((atm) => (
                    <Badge key={atm} className="text-xs bg-purple-100 text-purple-700">
                      {atm}
                    </Badge>
                  ))}
                </div>
                <div className="text-sm text-gray-600">
                  {preferences.crowdEnergy < 30 ? '😌 Calm energy' : preferences.crowdEnergy < 70 ? '🙂 Medium energy' : '🔥 High energy'}
                </div>
              </Card>

              <Card className="p-4 border border-gray-200">
                <h4 className="font-medium text-gray-800 mb-3">Music & Cuisine</h4>
                <div className="space-y-2">
                  <div className="flex flex-wrap gap-1">
                    {preferences.musicStyle.map((music) => (
                      <Badge key={music} className="text-xs bg-blue-100 text-blue-700">
                        🎵 {music}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {preferences.cuisineTypes.map((cuisine) => (
                      <Badge key={cuisine} className="text-xs bg-green-100 text-green-700">
                        🍽️ {cuisine}
                      </Badge>
                    ))}
                  </div>
                </div>
              </Card>

              <Card className="p-4 border border-gray-200">
                <h4 className="font-medium text-gray-800 mb-3">Time Preferences</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex flex-wrap gap-1">
                    {preferences.timePreferences.map((time) => (
                      <Badge key={time} className="text-xs bg-orange-100 text-orange-700">
                        🕐 {time}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {preferences.dayPreferences.map((day) => (
                      <Badge key={day} className="text-xs bg-indigo-100 text-indigo-700">
                        📅 {day}
                      </Badge>
                    ))}
                  </div>
                </div>
              </Card>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setCurrentStep('time')}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={() => handleCompleteSetup()}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
              >
                <Check className="w-4 h-4 mr-2" />
                Complete Setup
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const renderVenueCard = (venue: Venue) => (
    <motion.div
      key={venue.id}
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="relative"
    >
      <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 group">
        {venue.isPremium && (
          <div className="absolute top-4 right-4 z-10">
            <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0 shadow-lg">
              <Crown className="w-3 h-3 mr-1" />
              Premium
            </Badge>
          </div>
        )}
        
        {/* Main Image with Live Overlays */}
        <div className="relative h-56 overflow-hidden">
          <img
            src={venue.image}
            alt={venue.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
          
          {/* Live Status Indicators */}
          <div className="absolute top-4 left-4 flex flex-col gap-2">
            <Badge className="bg-red-500 text-white border-0 shadow-lg animate-pulse">
              <Activity className="w-3 h-3 mr-1" />
              LIVE
            </Badge>
            <Badge className={`text-white border-0 shadow-lg ${
              venue.liveData.trending.includes('+') ? 'bg-green-500' :
              venue.liveData.trending.includes('-') ? 'bg-blue-500' : 'bg-gray-500'
            }`}>
              <TrendingUp className="w-3 h-3 mr-1" />
              {venue.liveData.trending}
            </Badge>
          </div>

          {/* Crowd Level Indicator */}
          <div className="absolute top-4 right-4">
            <div className="flex items-center gap-2 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
              <Users className="w-4 h-4 text-white" />
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className={`w-1 h-4 rounded-full ${
                      i <= Math.ceil((venue.liveData.currentCrowd / venue.liveData.maxCapacity) * 5)
                        ? 'bg-white'
                        : 'bg-white/30'
                    }`}
                  />
                ))}
              </div>
              <span className="text-white text-xs font-medium">
                {Math.round((venue.liveData.currentCrowd / venue.liveData.maxCapacity) * 100)}%
              </span>
            </div>
          </div>

          {/* Photo/Video Indicators */}
          <div className="absolute bottom-16 right-4 flex gap-2">
            <div className="flex items-center gap-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
              <Camera className="w-3 h-3 text-white" />
              <span className="text-white text-xs">{venue.liveData.photos}</span>
            </div>
            <div className="bg-black/50 backdrop-blur-sm rounded-full p-2">
              <Play className="w-3 h-3 text-white" />
            </div>
          </div>
          
          {/* Venue Info Overlay */}
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center gap-2 mb-2">
              <Badge className="bg-white/20 text-white border-white/30 backdrop-blur-sm">
                {venue.vibe}
              </Badge>
              <Badge className={`text-white border-0 ${
                venue.liveData.averageWait === '2 min' ? 'bg-green-500' :
                venue.liveData.averageWait === '5 min' ? 'bg-yellow-500' : 'bg-red-500'
              }`}>
                <Timer className="w-3 h-3 mr-1" />
                {venue.liveData.averageWait} wait
              </Badge>
            </div>
            <h3 className="font-bold text-xl mb-1">{venue.name}</h3>
            <div className="flex items-center gap-3 text-sm">
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="font-medium">{venue.rating}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                {venue.distance}
              </div>
              <span>{venue.price}</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-5 space-y-4">
          {/* Live Events & Hot Spots */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Flame className="w-4 h-4 text-orange-500" />
              <span className="font-medium text-gray-800">Happening Now</span>
            </div>
            <div className="space-y-2">
              {venue.liveData.liveEvents.slice(0, 2).map((event, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-gray-700">{event}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Real-time Insights */}
          <div className="grid grid-cols-3 gap-3 p-3 bg-gray-50 rounded-xl">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-800">{venue.liveData.moodScore}</div>
              <div className="text-xs text-gray-600">Mood Score</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-800">{venue.liveData.checkIns}</div>
              <div className="text-xs text-gray-600">Check-ins</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-800">{venue.liveData.crowdAge}</div>
              <div className="text-xs text-gray-600">Crowd Age</div>
            </div>
          </div>

          {/* Personalized Insights */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-purple-600" />
              <span className="font-medium text-gray-800">Why You'll Love This</span>
            </div>
            <div className="space-y-2">
              <div className="p-3 bg-purple-50 rounded-lg border-l-4 border-purple-500">
                <p className="text-sm text-purple-800 font-medium">{venue.insights.whyVisit}</p>
              </div>
              <div className="flex items-start gap-2 text-sm text-gray-600">
                <AlertCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <span><strong>Best Time:</strong> {venue.insights.bestTime}</span>
              </div>
              <div className="flex items-start gap-2 text-sm text-gray-600">
                <Eye className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span><strong>Insider Tip:</strong> {venue.insights.insider}</span>
              </div>
            </div>
          </div>

          {/* Social Proof */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <UserCheck className="w-4 h-4 text-blue-600" />
              <span className="font-medium text-gray-800">Social Activity</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-3 h-3 text-green-600" />
                </div>
                <span>{venue.insights.trending}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="w-3 h-3 text-blue-600" />
                </div>
                <span>{venue.insights.friendActivity}</span>
              </div>
            </div>
          </div>

          {/* Special Offers */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Gift className="w-4 h-4 text-pink-600" />
              <span className="font-medium text-gray-800">Limited Time Offers</span>
            </div>
            <div className="space-y-2">
              {venue.insights.offers.map((offer, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg border border-pink-200">
                  <Percent className="w-4 h-4 text-pink-600" />
                  <span className="text-sm font-medium text-pink-800">{offer}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Hot Spots */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-yellow-600" />
              <span className="font-medium text-gray-800">Hot Spots Right Now</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {venue.liveData.hotSpots.map((spot, index) => (
                <Badge key={index} className="bg-yellow-100 text-yellow-800 border-yellow-300">
                  🔥 {spot}
                </Badge>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3 pt-4 border-t border-gray-100">
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
              <Navigation className="w-4 h-4 mr-2" />
              Get Directions
            </Button>
            <Button variant="outline" className="border-purple-300 text-purple-600 hover:bg-purple-50">
              <Phone className="w-4 h-4 mr-2" />
              Call Venue
            </Button>
          </div>

          {/* Secondary Actions */}
          <div className="flex items-center justify-between pt-2">
            <div className="flex gap-2">
              <Button size="sm" variant="ghost" className="text-gray-600 hover:text-purple-600">
                <Bookmark className="w-4 h-4 mr-1" />
                Save
              </Button>
              <Button size="sm" variant="ghost" className="text-gray-600 hover:text-purple-600">
                <Share2 className="w-4 h-4 mr-1" />
                Share
              </Button>
            </div>
            <Button size="sm" variant="ghost" className="text-purple-600 hover:text-purple-700">
              More Details
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );

  if (showPremiumFlow) {
    return (
      <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
        <div className="p-6 max-w-md mx-auto">
          <div className="flex items-center justify-between mb-8">
            <Button
              variant="ghost"
              onClick={() => setShowPremiumFlow(false)}
              className="hover:bg-white/30"
            >
              <X className="w-4 h-4 mr-2" />
              Close
            </Button>
            <div className="flex gap-2">
              {['intro', 'location', 'atmosphere', 'music-cuisine', 'time', 'review'].map((step, index) => (
                <div
                  key={step}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    ['intro', 'location', 'atmosphere', 'music-cuisine', 'time', 'review'].indexOf(currentStep) >= index
                      ? 'bg-purple-500'
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            <div className="w-16" />
          </div>

          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderVibePreferenceFlow()}
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-50 overflow-auto">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Venue Insider</h1>
            <p className="text-gray-600">Discover venues that match your vibe</p>
          </div>
          
          {isPremium && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPremiumFlow(true)}
              className="border-purple-300 text-purple-600 hover:bg-purple-50"
            >
              <Settings className="w-4 h-4 mr-2" />
              Preferences
            </Button>
          )}
        </div>

        {/* Premium Upgrade Card */}
        {!isPremium && renderPremiumCard()}

        {/* Search and Filters */}
        <div className="flex gap-3 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search venues, vibes, locations..."
              className="pl-10 bg-white"
            />
          </div>
          <Button variant="outline" size="icon" className="bg-white">
            <Filter className="w-4 h-4" />
          </Button>
        </div>

        {/* Premium Status Banner */}
        {isPremium && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-100 to-pink-100 rounded-2xl mb-6"
          >
            <Crown className="w-5 h-5 text-purple-600" />
            <div className="flex-1">
              <h3 className="font-semibold text-purple-800">Premium Active</h3>
              <p className="text-sm text-purple-600">
                Showing {preferences.atmosphere.length > 0 ? 'personalized' : 'curated'} venues based on your preferences
              </p>
            </div>
            <TrendingUp className="w-5 h-5 text-purple-600" />
          </motion.div>
        )}

        {/* Venue Grid */}
        <div className="grid gap-4 pb-20">
          {mockVenues.map((venue) => renderVenueCard(venue))}
        </div>
      </div>
    </div>
  );
}