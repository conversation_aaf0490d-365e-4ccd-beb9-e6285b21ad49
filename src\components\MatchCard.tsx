import { motion, useMotionValue, useTransform, PanInfo, AnimatePresence } from 'motion/react';
import { useState, useRef, useEffect } from 'react';
import { Heart, X, MapPin, Clock, Star, DollarSign, Crown, Sparkles, Users, TrendingUp, Lightbulb, Gift, Calendar, UserPlus } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

type MatchType = 'parking' | 'venue' | 'valet';

interface Match {
  id: string;
  type: MatchType;
  title: string;
  subtitle: string;
  distance: string;
  availability: string;
  rating?: number;
  price?: string;
  image: string;
  details: {
    description: string;
    features: string[];
    footprint?: number;
    vibe?: string;
    insights?: {
      whyMatch: string;
      liveData: {
        currentCrowd: number;
        maxCapacity: number;
        trending: string;
        moodScore: number;
        averageAge: string;
        peakTime: string;
      };
      socialProof: string;
      bestTime: string;
      insiderTip: string;
      offers: string[];
    };
  };
}

interface MatchCardProps {
  match: Match;
  onSwipe: (direction: 'left' | 'right') => void;
  onReservationRequest?: (request: {
    venueId: string;
    venueName: string;
    date: string;
    time: string;
    partySize: number;
  }) => void;
  onStartCollaborative?: (sessionName: string, selectedFriends: any[]) => void;
  friends?: any[];
}

const getMatchColor = (type: MatchType) => {
  switch (type) {
    case 'parking':
      return 'from-blue-500/20 to-cyan-500/20';
    case 'venue':
      return 'from-purple-500/20 to-pink-500/20';
    case 'valet':
      return 'from-green-500/20 to-emerald-500/20';
  }
};

const getMatchIcon = (type: MatchType) => {
  switch (type) {
    case 'parking':
      return '🚗';
    case 'venue':
      return '🎉';
    case 'valet':
      return '🔑';
  }
};

export function MatchCard({ match, onSwipe, onReservationRequest, onStartCollaborative, friends = [] }: MatchCardProps) {
  const x = useMotionValue(0);
  const rotate = useTransform(x, [-200, 200], [-25, 25]);
  const opacity = useTransform(x, [-200, -100, 0, 100, 200], [0, 1, 1, 1, 0]);

  const [exitX, setExitX] = useState(0);
  const [showVibePreview, setShowVibePreview] = useState(false);
  const [vibeAnimationPhase, setVibeAnimationPhase] = useState(0);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const vibeTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Check if this is a premium personalized match
  const isPremiumMatch = match.id.startsWith('p');

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
      if (vibeTimerRef.current) {
        clearTimeout(vibeTimerRef.current);
      }
    };
  }, []);

  const handleImageLongPressStart = () => {
    if (!isPremiumMatch) return;
    
    longPressTimerRef.current = setTimeout(() => {
      setShowVibePreview(true);
      setVibeAnimationPhase(0);
      
      // Start the 3-second animation sequence
      const phases = [0, 1, 2, 3];
      phases.forEach((phase, index) => {
        setTimeout(() => {
          setVibeAnimationPhase(phase);
        }, index * 750); // 750ms per phase = 3 seconds total
      });
      
      // Auto-close after 3 seconds
      vibeTimerRef.current = setTimeout(() => {
        setShowVibePreview(false);
        setVibeAnimationPhase(0);
      }, 3000);
    }, 1000); // 1 second long press to trigger
  };

  const handleImageLongPressEnd = () => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
  };

  const getVibeAnimationConfig = () => {
    const vibe = match.details.vibe?.toLowerCase() || 'energetic';
    
    switch (vibe) {
      case 'energetic':
      case 'high energy':
        return {
          colors: ['from-yellow-400 to-orange-600', 'from-red-500 to-pink-600', 'from-purple-500 to-blue-600'],
          effects: ['⚡', '🔥', '💥'],
          sounds: ['BASS DROP', 'CROWD ENERGY', 'ELECTRIC VIBES']
        };
      case 'sophisticated':
        return {
          colors: ['from-purple-600 to-indigo-800', 'from-gold-400 to-amber-600', 'from-slate-600 to-gray-800'],
          effects: ['🥂', '✨', '🎭'],
          sounds: ['SMOOTH JAZZ', 'ELEGANT AMBIANCE', 'REFINED ATMOSPHERE']
        };
      case 'romantic':
      case 'intimate':
        return {
          colors: ['from-pink-400 to-rose-600', 'from-red-400 to-pink-600', 'from-purple-400 to-pink-500'],
          effects: ['💕', '🌹', '💖'],
          sounds: ['SOFT MELODIES', 'INTIMATE LIGHTING', 'WARM ATMOSPHERE']
        };
      case 'chill':
        return {
          colors: ['from-blue-400 to-cyan-600', 'from-teal-400 to-blue-600', 'from-green-400 to-blue-500'],
          effects: ['😌', '🌊', '🍃'],
          sounds: ['AMBIENT SOUNDS', 'RELAXED VIBES', 'PEACEFUL ENERGY']
        };
      default:
        return {
          colors: ['from-purple-500 to-pink-600', 'from-blue-500 to-purple-600', 'from-pink-500 to-red-600'],
          effects: ['✨', '🎊', '🌟'],
          sounds: ['GREAT VIBES', 'PERFECT ATMOSPHERE', 'AMAZING ENERGY']
        };
    }
  };

  const vibeConfig = getVibeAnimationConfig();

  const renderVibePreview = () => (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ 
        background: `linear-gradient(45deg, ${vibeConfig.colors[vibeAnimationPhase] || vibeConfig.colors[0]})`,
      }}
    >
      {/* Background Image with Effects */}
      <div className="absolute inset-0">
        <img
          src={match.image}
          alt={match.title}
          className="w-full h-full object-cover"
          style={{
            filter: `blur(${2 - vibeAnimationPhase * 0.5}px) brightness(${1.2 + vibeAnimationPhase * 0.3}) contrast(${1.1 + vibeAnimationPhase * 0.2}) saturate(${1.3 + vibeAnimationPhase * 0.4})`
          }}
        />
        
        {/* Animated Overlay */}
        <motion.div
          className="absolute inset-0"
          style={{
            background: `linear-gradient(45deg, ${vibeConfig.colors[vibeAnimationPhase] || vibeConfig.colors[0]})`,
            mixBlendMode: 'overlay'
          }}
          animate={{
            opacity: [0.3, 0.6, 0.4, 0.7],
          }}
          transition={{
            duration: 0.75,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white">
        {/* Main Animation */}
        <motion.div
          key={vibeAnimationPhase}
          initial={{ scale: 0.5, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 1.5, opacity: 0, y: -50 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-8"
        >
          <div className="text-8xl mb-4">
            {vibeConfig.effects[vibeAnimationPhase] || vibeConfig.effects[0]}
          </div>
          
          <motion.h1
            className="text-4xl font-bold mb-2"
            animate={{
              scale: [1, 1.1, 1],
              textShadow: ["0 0 10px rgba(255,255,255,0.5)", "0 0 20px rgba(255,255,255,0.8)", "0 0 10px rgba(255,255,255,0.5)"]
            }}
            transition={{ duration: 0.75, repeat: Infinity }}
          >
            {match.title}
          </motion.h1>
          
          <motion.p
            className="text-xl opacity-90 mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {vibeConfig.sounds[vibeAnimationPhase] || vibeConfig.sounds[0]}
          </motion.p>

          {/* Venue Details with Animation */}
          <motion.div
            className="backdrop-blur-sm bg-white/10 rounded-2xl p-6 max-w-md mx-auto"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <div className="flex items-center justify-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-400 fill-current" />
                <span className="font-semibold">{match.rating}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                <span>{match.distance}</span>
              </div>
            </div>
            
            <p className="text-sm opacity-80 mb-4">{match.details.description}</p>
            
            <div className="flex flex-wrap gap-2 justify-center">
              {match.details.features.slice(0, 3).map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="bg-white/20 px-3 py-1 rounded-full text-xs"
                >
                  {feature}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>

        {/* Premium Badge */}
        <motion.div
          className="absolute top-8 right-8"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full">
            <Crown className="w-4 h-4" />
            <span className="text-sm font-semibold">PREMIUM VIBE</span>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-64 h-1 bg-white/20 rounded-full overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <motion.div
            className="h-full bg-white rounded-full"
            animate={{ width: `${(vibeAnimationPhase + 1) * 25}%` }}
            transition={{ duration: 0.3 }}
          />
        </motion.div>

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-2xl"
            style={{
              left: `${20 + i * 12}%`,
              top: `${30 + (i % 2) * 40}%`,
            }}
            animate={{
              y: [-20, -40, -20],
              opacity: [0.7, 1, 0.7],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.3,
            }}
          >
            {vibeConfig.effects[i % vibeConfig.effects.length]}
          </motion.div>
        ))}
      </div>

      {/* Close instruction */}
      <motion.div
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white/70 text-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        Tap anywhere to close
      </motion.div>
    </motion.div>
  );

  const handleDragEnd = (event: any, info: PanInfo) => {
    const threshold = 100;
    
    if (info.offset.x > threshold) {
      setExitX(200);
      onSwipe('right');
    } else if (info.offset.x < -threshold) {
      setExitX(-200);
      onSwipe('left');
    }
  };

  return (
    <>
      <div className="h-full flex items-center justify-center p-4">
        <motion.div
          className="w-full max-w-sm h-full max-h-[80vh] relative"
          style={{ x, rotate, opacity }}
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          onDragEnd={handleDragEnd}
          animate={exitX !== 0 ? { x: exitX } : {}}
          transition={{ duration: 0.3 }}
          whileDrag={{ scale: 1.05 }}
        >
          <div className={`h-full rounded-3xl overflow-hidden relative backdrop-blur-xl bg-gradient-to-br ${getMatchColor(match.type)} border border-white/30 shadow-2xl`}>
            {/* Premium Badge */}
            {isPremiumMatch && (
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="absolute top-4 left-4 z-20"
              >
                <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0 shadow-lg">
                  <Crown className="w-3 h-3 mr-1" />
                  Premium
                </Badge>
              </motion.div>
            )}

            {/* Long Press Hint for Premium */}
            {isPremiumMatch && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="absolute top-14 left-4 z-20"
              >
                <div className="bg-white/10 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                  <Sparkles className="w-3 h-3" />
                  Long press for vibe preview
                </div>
              </motion.div>
            )}

            {/* Background Image with Long Press */}
            <div 
              className="absolute inset-0 bg-cover bg-center opacity-40 cursor-pointer"
              style={{ backgroundImage: `url(${match.image})` }}
              onMouseDown={handleImageLongPressStart}
              onMouseUp={handleImageLongPressEnd}
              onMouseLeave={handleImageLongPressEnd}
              onTouchStart={handleImageLongPressStart}
              onTouchEnd={handleImageLongPressEnd}
            />
            
            {/* Glassmorphism Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20" />
            
            {/* Content */}
            <div className="relative h-full flex flex-col">
              {/* Header */}
              <div className="p-6 flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{getMatchIcon(match.type)}</span>
                  <Badge variant="secondary" className="backdrop-blur-sm bg-white/20">
                    {match.type.toUpperCase()}
                  </Badge>
                </div>
                
                {match.rating && (
                  <div className="flex items-center gap-1 backdrop-blur-sm bg-white/20 px-2 py-1 rounded-full">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-white text-sm font-medium">{match.rating}</span>
                  </div>
                )}
              </div>

              {/* Main Content */}
              <div className="flex-1 flex flex-col justify-end p-6">
                <div className="space-y-4">
                  <div>
                    <h1 className="text-3xl font-bold text-white mb-2">{match.title}</h1>
                    <p className="text-white/80 text-lg">{match.subtitle}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2 text-white/90">
                      <MapPin className="w-4 h-4" />
                      <span className="text-sm">{match.distance}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-white/90">
                      <Clock className="w-4 h-4" />
                      <span className="text-sm">{match.availability}</span>
                    </div>
                  </div>

                  {match.price && (
                    <div className="flex items-center gap-2 text-white">
                      <DollarSign className="w-5 h-5" />
                      <span className="text-xl font-semibold">{match.price}</span>
                    </div>
                  )}

                  {match.details.footprint && (
                    <div className="backdrop-blur-sm bg-white/10 rounded-2xl p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-white/80">Live Footprint</span>
                        <span className="text-white font-bold">{match.details.footprint} people</span>
                      </div>
                      <div className="w-full bg-white/20 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${Math.min(match.details.footprint / 2, 100)}%` }}
                        />
                      </div>
                      <p className="text-white/60 text-sm mt-1">Vibe: {match.details.vibe}</p>
                    </div>
                  )}

                  <div className="space-y-2">
                    <p className="text-white/80 text-sm">{match.details.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {match.details.features.map((feature, index) => (
                        <Badge key={index} variant="outline" className="bg-white/10 border-white/30 text-white text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced Insights Section for Premium Matches */}
                  {match.details.insights && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                      className="space-y-3"
                    >
                      {/* Why This Matches You */}
                      <div className="backdrop-blur-sm bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl p-4 border border-purple-400/20">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <Sparkles className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <h4 className="text-white font-semibold text-sm mb-1">Why This Is Perfect For You</h4>
                            <p className="text-white/80 text-xs">{match.details.insights.whyMatch}</p>
                          </div>
                        </div>
                      </div>

                      {/* Live Data & Social Proof Row */}
                      <div className="grid grid-cols-2 gap-3">
                        {/* Live Crowd Data */}
                        <div className="backdrop-blur-sm bg-white/10 rounded-xl p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <Users className="w-4 h-4 text-blue-400" />
                            <span className="text-white text-xs font-medium">Live Crowd</span>
                          </div>
                          <div className="text-white font-bold text-lg">
                            {match.details.insights.liveData.currentCrowd}/{match.details.insights.liveData.maxCapacity}
                          </div>
                          <div className="text-white/60 text-xs">
                            {match.details.insights.liveData.averageAge}
                          </div>
                        </div>

                        {/* Trending */}
                        <div className="backdrop-blur-sm bg-white/10 rounded-xl p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <TrendingUp className="w-4 h-4 text-green-400" />
                            <span className="text-white text-xs font-medium">Trending</span>
                          </div>
                          <div className="text-white font-bold text-sm">
                            {match.details.insights.liveData.trending}
                          </div>
                          <div className="flex items-center gap-1 mt-1">
                            <Star className="w-3 h-3 text-yellow-400 fill-current" />
                            <span className="text-white/80 text-xs">
                              {match.details.insights.liveData.moodScore}/10 vibe
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Social Proof */}
                      <div className="backdrop-blur-sm bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-xl p-3 border border-blue-400/20">
                        <div className="flex items-center gap-2 mb-1">
                          <Users className="w-4 h-4 text-cyan-400" />
                          <span className="text-cyan-300 text-xs font-medium">Social Buzz</span>
                        </div>
                        <p className="text-white/90 text-xs">{match.details.insights.socialProof}</p>
                      </div>

                      {/* Insider Tip & Best Time */}
                      <div className="grid grid-cols-1 gap-2">
                        <div className="backdrop-blur-sm bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-xl p-3 border border-yellow-400/20">
                          <div className="flex items-center gap-2 mb-1">
                            <Lightbulb className="w-4 h-4 text-yellow-400" />
                            <span className="text-yellow-300 text-xs font-medium">Insider Tip</span>
                          </div>
                          <p className="text-white/90 text-xs">{match.details.insights.insiderTip}</p>
                        </div>

                        {/* Best Time */}
                        <div className="backdrop-blur-sm bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-xl p-3 border border-emerald-400/20">
                          <div className="flex items-center gap-2 mb-1">
                            <Clock className="w-4 h-4 text-emerald-400" />
                            <span className="text-emerald-300 text-xs font-medium">Perfect Timing</span>
                          </div>
                          <p className="text-white/90 text-xs">{match.details.insights.bestTime}</p>
                        </div>
                      </div>

                      {/* Special Offers */}
                      {match.details.insights.offers && match.details.insights.offers.length > 0 && (
                        <div className="backdrop-blur-sm bg-gradient-to-r from-pink-500/10 to-red-500/10 rounded-xl p-3 border border-pink-400/20">
                          <div className="flex items-center gap-2 mb-2">
                            <Gift className="w-4 h-4 text-pink-400" />
                            <span className="text-pink-300 text-xs font-medium">Limited Offers</span>
                          </div>
                          <div className="space-y-1">
                            {match.details.insights.offers.slice(0, 2).map((offer, index) => (
                              <div key={index} className="text-white/90 text-xs flex items-start gap-2">
                                <span className="text-pink-400 text-xs">•</span>
                                <span>{offer}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {/* Action Buttons for Venues */}
                  {match.type === 'venue' && (onReservationRequest || onStartCollaborative) && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.7 }}
                      className="grid grid-cols-2 gap-3 pt-2"
                    >
                      {/* Reserve Button */}
                      {onReservationRequest && (
                        <Button
                          size="sm"
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white border-0 text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            onReservationRequest({
                              venueId: match.id,
                              venueName: match.title,
                              date: new Date().toISOString().split('T')[0],
                              time: '7:00 PM',
                              partySize: 2
                            });
                          }}
                        >
                          <Calendar className="w-3 h-3 mr-1" />
                          Reserve
                        </Button>
                      )}

                      {/* Collaborate Button */}
                      {onStartCollaborative && friends.length > 0 && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-white/10 border-white/30 text-white hover:bg-white/20 text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStartCollaborative(`${match.title} Discovery`, friends.slice(0, 3));
                          }}
                        >
                          <UserPlus className="w-3 h-3 mr-1" />
                          Decide Together
                        </Button>
                      )}
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Vibe Preview Overlay */}
      <AnimatePresence>
        {showVibePreview && (
          <motion.div
            onClick={() => {
              setShowVibePreview(false);
              setVibeAnimationPhase(0);
              if (vibeTimerRef.current) {
                clearTimeout(vibeTimerRef.current);
                vibeTimerRef.current = null;
              }
            }}
          >
            {renderVibePreview()}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}