import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { authService } from '../../../lib/auth/authService';
import { LoginCredentials, RegisterCredentials } from '../../../lib/auth/types';

// Mock fetch
global.fetch = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.clear();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('login', () => {
    const mockCredentials: LoginCredentials = {
      email: '<EMAIL>',
      password: 'password123',
      rememberMe: true,
    };

    const mockAuthResponse = {
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'consumer',
        permissions: [],
        preferences: {
          permissions: {
            gps: true,
            wifi: true,
            bluetooth: true,
            camera: true,
            notifications: true,
            imu: true,
          },
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      tokens: {
        accessToken: 'access-token-123',
        refreshToken: 'refresh-token-123',
        expiresAt: Date.now() + 3600000,
      },
    };

    it('should login successfully with valid credentials', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAuthResponse,
      });

      const result = await authService.login(mockCredentials);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/login'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(mockCredentials),
        })
      );

      expect(result).toEqual(mockAuthResponse);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'bytspot_tokens',
        JSON.stringify(mockAuthResponse.tokens)
      );
    });

    it('should handle login failure', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        json: async () => ({ message: 'Invalid credentials' }),
      });

      await expect(authService.login(mockCredentials)).rejects.toThrow('Invalid credentials');
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });

    it('should handle network errors', async () => {
      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      await expect(authService.login(mockCredentials)).rejects.toThrow('Network error');
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });
  });

  describe('register', () => {
    const mockCredentials: RegisterCredentials = {
      email: '<EMAIL>',
      password: 'password123',
      name: 'New User',
      role: 'consumer',
      address: '123 Test St',
    };

    it('should register successfully', async () => {
      const mockAuthResponse = {
        user: {
          id: 'user-456',
          email: mockCredentials.email,
          name: mockCredentials.name,
          role: mockCredentials.role,
          permissions: [],
          preferences: {
            permissions: {
              gps: false,
              wifi: false,
              bluetooth: false,
              camera: false,
              notifications: false,
              imu: false,
            },
          },
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        tokens: {
          accessToken: 'access-token-456',
          refreshToken: 'refresh-token-456',
          expiresAt: Date.now() + 3600000,
        },
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAuthResponse,
      });

      const result = await authService.register(mockCredentials);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/register'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(mockCredentials),
        })
      );

      expect(result).toEqual(mockAuthResponse);
    });
  });

  describe('token management', () => {
    it('should load tokens from localStorage on initialization', () => {
      const mockTokens = {
        accessToken: 'stored-token',
        refreshToken: 'stored-refresh',
        expiresAt: Date.now() + 3600000,
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockTokens));
      
      expect(authService.isAuthenticated()).toBe(true);
      expect(authService.getAccessToken()).toBe('stored-token');
    });

    it('should clear expired tokens', () => {
      const expiredTokens = {
        accessToken: 'expired-token',
        refreshToken: 'expired-refresh',
        expiresAt: Date.now() - 3600000, // 1 hour ago
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredTokens));
      
      expect(authService.isAuthenticated()).toBe(false);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('bytspot_tokens');
    });

    it('should refresh tokens before expiry', async () => {
      const mockTokens = {
        accessToken: 'old-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 4 * 60 * 1000, // 4 minutes (less than 5 minute threshold)
      };

      const mockRefreshedTokens = {
        accessToken: 'new-token',
        refreshToken: 'new-refresh-token',
        expiresAt: Date.now() + 3600000,
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockTokens));
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockRefreshedTokens,
      });

      const result = await authService.refreshToken();

      expect(result).toEqual(mockRefreshedTokens);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'bytspot_tokens',
        JSON.stringify(mockRefreshedTokens)
      );
    });
  });

  describe('logout', () => {
    it('should logout and clear tokens', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
      });

      await authService.logout();

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/logout'),
        expect.objectContaining({
          method: 'POST',
        })
      );

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('bytspot_tokens');
    });

    it('should clear tokens even if server logout fails', async () => {
      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      await authService.logout();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('bytspot_tokens');
    });
  });

  describe('getCurrentUser', () => {
    it('should return user data when authenticated', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'consumer',
        permissions: [],
        preferences: {},
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      // Mock authenticated state
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        accessToken: 'valid-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000,
      }));

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser,
      });

      const result = await authService.getCurrentUser();
      expect(result).toEqual(mockUser);
    });

    it('should return null when not authenticated', async () => {
      localStorageMock.getItem.mockReturnValue(null);

      const result = await authService.getCurrentUser();
      expect(result).toBeNull();
    });

    it('should clear tokens on 401 response', async () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        accessToken: 'invalid-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000,
      }));

      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
      });

      const result = await authService.getCurrentUser();
      
      expect(result).toBeNull();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('bytspot_tokens');
    });
  });

  describe('permissions', () => {
    it('should check authentication status', () => {
      // Not authenticated
      localStorageMock.getItem.mockReturnValue(null);
      expect(authService.isAuthenticated()).toBe(false);

      // Authenticated with valid token
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        accessToken: 'valid-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000,
      }));
      expect(authService.isAuthenticated()).toBe(true);

      // Authenticated with expired token
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        accessToken: 'expired-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() - 1000,
      }));
      expect(authService.isAuthenticated()).toBe(false);
    });

    it('should return access token when authenticated', () => {
      const mockTokens = {
        accessToken: 'test-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000,
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockTokens));
      
      expect(authService.getAccessToken()).toBe('test-token');
    });

    it('should return null access token when not authenticated', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      expect(authService.getAccessToken()).toBeNull();
    });
  });
});