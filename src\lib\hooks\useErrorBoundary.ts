import { useCallback } from 'react';
import { errorService } from '../monitoring/errorService';

interface ErrorBoundaryHookOptions {
  feature?: string;
  onError?: (error: Error, errorInfo: any) => void;
}

export function useErrorBoundary(options: ErrorBoundaryHookOptions = {}) {
  const captureError = useCallback((error: Error, errorInfo?: any) => {
    errorService.captureError(error, {
      feature: options.feature,
      action: 'component_error',
      metadata: errorInfo,
    });
    
    options.onError?.(error, errorInfo);
  }, [options]);

  const captureException = useCallback((error: Error) => {
    captureError(error);
  }, [captureError]);

  const wrapAsync = useCallback(async <T>(
    asyncOperation: () => Promise<T>,
    context?: string
  ): Promise<T> => {
    try {
      return await asyncOperation();
    } catch (error) {
      captureError(error as Error, { context });
      throw error;
    }
  }, [captureError]);

  const wrapSync = useCallback(<T>(
    syncOperation: () => T,
    context?: string
  ): T => {
    try {
      return syncOperation();
    } catch (error) {
      captureError(error as Error, { context });
      throw error;
    }
  }, [captureError]);

  return {
    captureError,
    captureException,
    wrapAsync,
    wrapSync,
  };
}