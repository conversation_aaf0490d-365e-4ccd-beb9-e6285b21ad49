import { test, expect } from '@playwright/test';

test.describe('Authentication Integration Tests', () => {
  test.describe('Complete Login Flow', () => {
    test('should complete full login to protected route workflow', async ({ page }) => {
      // Step 1: Start at root, should redirect to login
      await page.goto('/');
      await expect(page).toHaveURL(/.*login/);

      // Step 2: Verify login form is present and accessible
      await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
      await expect(page.getByLabel(/email/i)).toBeVisible();
      await expect(page.getByLabel(/password/i)).toBeVisible();
      await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();

      // Step 3: Mock successful authentication
      await page.route('**/auth/login', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            user: {
              id: 'test-user-123',
              email: '<EMAIL>',
              name: 'Integration Test User',
              role: 'consumer',
              permissions: [],
              preferences: {
                permissions: {
                  gps: true,
                  wifi: true,
                  bluetooth: true,
                  camera: true,
                  notifications: true,
                  imu: true,
                },
              },
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            tokens: {
              accessToken: 'integration-test-token',
              refreshToken: 'integration-refresh-token',
              expiresAt: Date.now() + 3600000,
            },
          }),
        });
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-user-123',
            email: '<EMAIL>',
            name: 'Integration Test User',
            role: 'consumer',
            permissions: [],
            preferences: {
              permissions: {
                gps: true,
                wifi: true,
                bluetooth: true,
                camera: true,
                notifications: true,
                imu: true,
              },
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      // Step 4: Fill out and submit login form
      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('password123');
      await page.getByRole('button', { name: /sign in/i }).click();

      // Step 5: Should redirect to app and show authenticated state
      await expect(page).toHaveURL(/.*app\/matches/);
      
      // Step 6: Verify user can navigate through protected routes
      await page.getByRole('button', { name: /map/i }).click();
      await expect(page).toHaveURL(/.*app\/map/);
      
      await page.getByRole('button', { name: /profile/i }).click();
      await expect(page).toHaveURL(/.*app\/profile/);

      // Step 7: Verify navigation maintains authentication
      await page.goto('/app/social');
      await expect(page).toHaveURL(/.*app\/social/);
      
      // Should not redirect to login
      await expect(page).not.toHaveURL(/.*login/);
    });

    test('should handle invalid credentials gracefully', async ({ page }) => {
      await page.goto('/login');

      // Mock failed authentication
      await page.route('**/auth/login', async route => {
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({
            message: 'Invalid email or password',
          }),
        });
      });

      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('wrongpassword');
      await page.getByRole('button', { name: /sign in/i }).click();

      // Should show error message
      await expect(page.getByText('Invalid email or password')).toBeVisible();
      
      // Should remain on login page
      await expect(page).toHaveURL(/.*login/);
      
      // Form should be re-enabled for retry
      await expect(page.getByRole('button', { name: /sign in/i })).toBeEnabled();
    });
  });

  test.describe('Registration to Login Flow', () => {
    test('should complete registration and automatic login', async ({ page }) => {
      await page.goto('/register');

      // Mock successful registration with auto-login
      await page.route('**/auth/register', async route => {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            user: {
              id: 'new-user-123',
              email: '<EMAIL>',
              name: 'New User',
              role: 'consumer',
              permissions: [],
              preferences: {
                permissions: {
                  gps: false,
                  wifi: false,
                  bluetooth: false,
                  camera: false,
                  notifications: false,
                  imu: false,
                },
              },
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            tokens: {
              accessToken: 'new-user-token',
              refreshToken: 'new-user-refresh-token',
              expiresAt: Date.now() + 3600000,
            },
          }),
        });
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'new-user-123',
            email: '<EMAIL>',
            name: 'New User',
            role: 'consumer',
            permissions: [],
            preferences: {
              permissions: {
                gps: false,
                wifi: false,
                bluetooth: false,
                camera: false,
                notifications: false,
                imu: false,
              },
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      // Fill registration form
      await page.getByLabel(/name/i).fill('New User');
      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('newpassword123');
      await page.getByRole('button', { name: /create account/i }).click();

      // Should redirect to app after successful registration
      await expect(page).toHaveURL(/.*app\/matches/);
    });
  });

  test.describe('Session Persistence', () => {
    test('should maintain session across page reloads', async ({ page }) => {
      // Set up authenticated session
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'persistent-token',
          refreshToken: 'persistent-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'persistent-user',
            email: '<EMAIL>',
            name: 'Persistent User',
            role: 'consumer',
            permissions: [],
            preferences: {
              permissions: {
                gps: true,
                wifi: true,
                bluetooth: true,
                camera: true,
                notifications: true,
                imu: true,
              },
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      // Go to app
      await page.goto('/app');
      await expect(page).toHaveURL(/.*app\/matches/);

      // Reload page
      await page.reload();
      
      // Should still be authenticated
      await expect(page).toHaveURL(/.*app\/matches/);
      await expect(page).not.toHaveURL(/.*login/);
    });

    test('should handle expired tokens on page reload', async ({ page }) => {
      // Set up expired token
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'expired-token',
          refreshToken: 'expired-refresh-token',
          expiresAt: Date.now() - 3600000, // Expired 1 hour ago
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Token expired' }),
        });
      });

      await page.goto('/app');
      
      // Should redirect to login due to expired token
      await expect(page).toHaveURL(/.*login/);
    });
  });

  test.describe('Business Role Access Flow', () => {
    test('should allow host user to access business features', async ({ page }) => {
      await page.goto('/login');

      // Mock host user login
      await page.route('**/auth/login', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            user: {
              id: 'host-user-123',
              email: '<EMAIL>',
              name: 'Host User',
              role: 'host',
              permissions: [
                { resource: 'venue', actions: ['read', 'write', 'manage'] },
                { resource: 'analytics', actions: ['read'] },
                { resource: 'reservations', actions: ['read', 'write'] },
              ],
              preferences: {
                permissions: {
                  gps: true,
                  wifi: true,
                  bluetooth: true,
                  camera: true,
                  notifications: true,
                  imu: true,
                },
              },
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            tokens: {
              accessToken: 'host-token',
              refreshToken: 'host-refresh-token',
              expiresAt: Date.now() + 3600000,
            },
          }),
        });
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'host-user-123',
            email: '<EMAIL>',
            name: 'Host User',
            role: 'host',
            permissions: [
              { resource: 'venue', actions: ['read', 'write', 'manage'] },
              { resource: 'analytics', actions: ['read'] },
              { resource: 'reservations', actions: ['read', 'write'] },
            ],
            preferences: {
              permissions: {
                gps: true,
                wifi: true,
                bluetooth: true,
                camera: true,
                notifications: true,
                imu: true,
              },
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      // Login as host
      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('hostpassword');
      await page.getByRole('button', { name: /sign in/i }).click();

      // Should redirect to app
      await expect(page).toHaveURL(/.*app\/matches/);

      // Should show business toggle
      await expect(page.getByRole('button', { name: /business/i })).toBeVisible();

      // Access business features
      await page.goto('/business');
      await expect(page).toHaveURL(/.*business\/dashboard/);

      await page.goto('/business/analytics');
      await expect(page).toHaveURL(/.*business\/analytics/);

      // Should still be able to access consumer features
      await page.goto('/app');
      await expect(page).toHaveURL(/.*app\/matches/);
    });

    test('should deny consumer user access to business features', async ({ page }) => {
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'consumer-token',
          refreshToken: 'consumer-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'consumer-user-123',
            email: '<EMAIL>',
            name: 'Consumer User',
            role: 'consumer',
            permissions: [],
            preferences: {
              permissions: {
                gps: true,
                wifi: true,
                bluetooth: true,
                camera: true,
                notifications: true,
                imu: true,
              },
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      // Try to access business routes
      await page.goto('/business');
      await expect(page).toHaveURL(/.*unauthorized/);

      await page.goto('/business/dashboard');
      await expect(page).toHaveURL(/.*unauthorized/);

      // Should show unauthorized page with navigation options
      await expect(page.getByRole('heading', { name: /403/i })).toBeVisible();
      await expect(page.getByText(/permission/i)).toBeVisible();
      await expect(page.getByRole('button', { name: /go to app/i })).toBeVisible();

      // Should be able to return to app
      await page.getByRole('button', { name: /go to app/i }).click();
      await expect(page).toHaveURL(/.*app\/matches/);
    });
  });

  test.describe('Logout Flow', () => {
    test('should complete logout and redirect to login', async ({ page }) => {
      // Set up authenticated session
      await page.addInitScript(() => {
        localStorage.setItem('bytspot_tokens', JSON.stringify({
          accessToken: 'logout-test-token',
          refreshToken: 'logout-refresh-token',
          expiresAt: Date.now() + 3600000,
        }));
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'logout-user',
            email: '<EMAIL>',
            name: 'Logout User',
            role: 'consumer',
            permissions: [],
            preferences: {
              permissions: {
                gps: true,
                wifi: true,
                bluetooth: true,
                camera: true,
                notifications: true,
                imu: true,
              },
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      await page.route('**/auth/logout', async route => {
        await route.fulfill({ status: 200, body: 'OK' });
      });

      await page.goto('/app/profile');
      await expect(page).toHaveURL(/.*app\/profile/);

      // Logout
      await page.getByRole('button', { name: /logout/i }).click();

      // Should redirect to login
      await expect(page).toHaveURL(/.*login/);

      // Try to access protected route
      await page.goto('/app');
      await expect(page).toHaveURL(/.*login/);

      // Local storage should be cleared
      const tokens = await page.evaluate(() => localStorage.getItem('bytspot_tokens'));
      expect(tokens).toBeNull();
    });
  });

  test.describe('Error Recovery', () => {
    test('should recover from network errors during authentication', async ({ page, context }) => {
      await page.goto('/login');

      // Simulate network failure
      await context.setOffline(true);

      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('recoverypassword');
      await page.getByRole('button', { name: /sign in/i }).click();

      // Should show network error
      await expect(page.getByText(/network/i)).toBeVisible();

      // Re-enable network
      await context.setOffline(false);

      // Mock successful authentication
      await page.route('**/auth/login', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            user: {
              id: 'recovery-user',
              email: '<EMAIL>',
              name: 'Recovery User',
              role: 'consumer',
              permissions: [],
              preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            tokens: {
              accessToken: 'recovery-token',
              refreshToken: 'recovery-refresh-token',
              expiresAt: Date.now() + 3600000,
            },
          }),
        });
      });

      await page.route('**/auth/me', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'recovery-user',
            email: '<EMAIL>',
            name: 'Recovery User',
            role: 'consumer',
            permissions: [],
            preferences: { permissions: { gps: true, wifi: true, bluetooth: true, camera: true, notifications: true, imu: true } },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
        });
      });

      // Retry login
      await page.getByRole('button', { name: /sign in/i }).click();

      // Should succeed and redirect to app
      await expect(page).toHaveURL(/.*app\/matches/);
    });
  });
});