import { motion } from 'motion/react';
import { MapPin, TrendingUp, Car, Users, Home, Menu, Video, ChevronLeft, Info, Star, Clock, Navigation as NavIcon, Loader2 } from 'lucide-react';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { LiveVibeCamera } from './LiveVibeCamera';
import { GooglePlacesService, type PlaceDetails, type BytspotMapData } from './services/GooglePlacesService';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAnalytics } from '../lib/hooks/useAnalytics';

// Mock data structure matching the Google Maps API response format
interface MapPin {
  place_id: string;
  latitude: number;
  longitude: number;
  status: 'trending' | 'prime' | 'available' | 'busy';
  vibe: 'energetic' | 'chill' | 'sophisticated' | 'artsy' | 'quiet';
  type: 'venue' | 'parking' | 'valet';
  name: string;
  rating?: number;
  price_level?: number;
  current_crowd?: number;
  max_capacity?: number;
  wait_time?: string;
}

// Mock API response simulating real-time data from Bytspot backend
const mockMapPinsResponse = {
  map_pins: [
    {
      place_id: "ChIJj61dQgK6j4AR4GeTYWZsKWw",
      latitude: 37.7849,
      longitude: -122.4094,
      status: "trending" as const,
      vibe: "energetic" as const,
      type: "venue" as const,
      name: "Neon Pulse",
      rating: 4.9,
      current_crowd: 85,
      max_capacity: 120,
      wait_time: "5 mins"
    },
    {
      place_id: "ChIJC-K3s0r-j4ART4e-B-zN430",
      latitude: 37.7749,
      longitude: -122.4194,
      status: "prime" as const,
      vibe: "sophisticated" as const,
      type: "venue" as const,
      name: "Velvet & Vine",
      rating: 4.8,
      current_crowd: 42,
      max_capacity: 80,
      wait_time: "No wait"
    },
    {
      place_id: "ChIJK-1234567890abcdefg",
      latitude: 37.7949,
      longitude: -122.3994,
      status: "available" as const,
      vibe: "quiet" as const,
      type: "parking" as const,
      name: "Premium Garage",
      price_level: 3
    },
    {
      place_id: "ChIJX-9876543210zyxwvu",
      latitude: 37.7649,
      longitude: -122.4294,
      status: "busy" as const,
      vibe: "chill" as const,
      type: "venue" as const,
      name: "Garden Drift",
      rating: 4.7,
      current_crowd: 68,
      max_capacity: 100,
      wait_time: "15 mins"
    },
    {
      place_id: "ChIJV-5555666677778888",
      latitude: 37.7549,
      longitude: -122.4394,
      status: "available" as const,
      vibe: "quiet" as const,
      type: "valet" as const,
      name: "Premium Valet",
      wait_time: "3 mins"
    }
  ]
};

interface MapViewProps {
  onBackToHome?: () => void;
}

export function MapView({ onBackToHome }: MapViewProps) {
  const navigate = useNavigate();
  const { track } = useAnalytics();
  const [showMenu, setShowMenu] = useState(false);
  const [showLiveVibeCamera, setShowLiveVibeCamera] = useState(false);
  const [selectedPin, setSelectedPin] = useState<MapPin | null>(null);
  const [mapPins, setMapPins] = useState<MapPin[]>(mockMapPinsResponse.map_pins);
  const [currentLocation] = useState({
    name: 'Downtown Core',
    type: 'area' as const,
    address: 'San Francisco Financial District'
  });

  // Simulate real-time updates
  useEffect(() => {
    const updateInterval = setInterval(() => {
      setMapPins(prevPins => 
        prevPins.map(pin => ({
          ...pin,
          current_crowd: pin.current_crowd ? 
            Math.max(5, Math.min(pin.max_capacity || 100, pin.current_crowd + Math.floor(Math.random() * 10) - 5)) :
            undefined
        }))
      );
    }, 30000); // Update every 30 seconds

    return () => clearInterval(updateInterval);
  }, []);

  const getMarkerStyle = (pin: MapPin) => {
    const baseClasses = "w-6 h-6 rounded-full border-2 border-white shadow-lg relative z-10 cursor-pointer";
    
    switch (pin.status) {
      case 'trending':
        return `${baseClasses} bg-gradient-to-r from-purple-500 to-pink-500 trending-marker`;
      case 'prime':
        return `${baseClasses} bg-gradient-to-r from-yellow-400 to-orange-500 prime-marker`;
      case 'busy':
        return `${baseClasses} bg-gradient-to-r from-red-500 to-red-600`;
      case 'available':
      default:
        return `${baseClasses} ${
          pin.type === 'venue' ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
          pin.type === 'parking' ? 'bg-gradient-to-r from-green-500 to-green-600' :
          'bg-gradient-to-r from-indigo-500 to-indigo-600'
        }`;
    }
  };

  const getPulseStyle = (pin: MapPin) => {
    const baseSize = pin.status === 'trending' ? '80px' : pin.status === 'prime' ? '60px' : '40px';
    const color = pin.status === 'trending' ? 'rgb(168 85 247 / 0.4)' : 
                 pin.status === 'prime' ? 'rgb(251 146 60 / 0.4)' : 
                 'rgb(59 130 246 / 0.4)';
    
    return {
      width: baseSize,
      height: baseSize,
      backgroundColor: color
    };
  };

  const handlePinClick = (pin: MapPin) => {
    track('map_pin_clicked', {
      pin_id: pin.place_id,
      pin_name: pin.name,
      pin_type: pin.type,
      pin_status: pin.status,
    });
    setSelectedPin(pin);
  };

  const handleViewDetails = (pin: MapPin) => {
    track('venue_detail_from_map', {
      venue_id: pin.place_id,
      venue_name: pin.name,
      venue_status: pin.status,
      source: 'map_view',
    });
    
    if (pin.type === 'venue') {
      navigate(`/app/venue/${pin.place_id}`);
    } else if (pin.type === 'parking') {
      navigate(`/app/parking/${pin.place_id}`);
    } else if (pin.type === 'valet') {
      navigate(`/app/valet/${pin.place_id}`);
    }
  };

  const handleCloseDetail = () => {
    setSelectedPin(null);
  };

  const renderVenueDetail = () => {
    if (!selectedPin) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 100 }}
        className="absolute bottom-32 left-4 right-4 z-40"
      >
        <Card className="backdrop-blur-xl bg-white/95 border-white/40 p-6 shadow-2xl">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-semibold text-gray-800">{selectedPin.name}</h3>
                <Badge 
                  className={`${
                    selectedPin.status === 'trending' ? 'bg-purple-100 text-purple-800' :
                    selectedPin.status === 'prime' ? 'bg-yellow-100 text-yellow-800' :
                    selectedPin.status === 'busy' ? 'bg-red-100 text-red-800' :
                    'bg-green-100 text-green-800'
                  }`}
                >
                  {selectedPin.status}
                </Badge>
              </div>
              
              {selectedPin.rating && (
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{selectedPin.rating}</span>
                  </div>
                  <span className="text-gray-500 text-sm">•</span>
                  <span className="text-gray-600 text-sm capitalize">{selectedPin.vibe} vibe</span>
                </div>
              )}

              {selectedPin.current_crowd && (
                <div className="flex items-center gap-4 mb-3">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">
                      {selectedPin.current_crowd}/{selectedPin.max_capacity} capacity
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">{selectedPin.wait_time}</span>
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={() => handleViewDetails(selectedPin)}
                >
                  View Details
                </Button>
                <Button size="sm" variant="outline">
                  <NavIcon className="w-4 h-4 mr-2" />
                  Directions
                </Button>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCloseDetail}
              className="w-8 h-8 p-0"
            >
              ✕
            </Button>
          </div>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4 relative">
      {/* CSS Animations for Pulsating Markers */}
      <style>{`
        @keyframes pulse-trending {
          0% { box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.7); }
          70% { box-shadow: 0 0 0 12px rgba(168, 85, 247, 0); }
          100% { box-shadow: 0 0 0 0 rgba(168, 85, 247, 0); }
        }
        
        @keyframes pulse-prime {
          0% { box-shadow: 0 0 0 0 rgba(251, 146, 60, 0.7); }
          70% { box-shadow: 0 0 0 10px rgba(251, 146, 60, 0); }
          100% { box-shadow: 0 0 0 0 rgba(251, 146, 60, 0); }
        }
        
        .trending-marker {
          animation: pulse-trending 2s infinite;
        }
        
        .prime-marker {
          animation: pulse-prime 2s infinite;
        }
        
        .map-container {
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
          position: relative;
          overflow: hidden;
        }
        
        .map-overlay {
          background: 
            radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, rgba(243, 244, 246, 0.8) 0%, rgba(249, 250, 251, 0.9) 100%);
        }
      `}</style>
      
      <div className="h-full backdrop-blur-xl bg-white/20 rounded-3xl border border-white/30 overflow-hidden relative">
        {/* Left Navigation */}
        <div className="absolute top-6 left-6 z-30">
          {showMenu ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="backdrop-blur-xl bg-white/90 rounded-2xl border border-white/40 p-3 space-y-2 min-w-48 shadow-xl"
            >
              <Button
                variant="ghost"
                onClick={() => setShowMenu(false)}
                className="w-full justify-start gap-3 text-gray-700 hover:bg-gray-100"
              >
                <ChevronLeft className="w-4 h-4" />
                Close Menu
              </Button>
              
              {onBackToHome && (
                <Button
                  variant="ghost"
                  onClick={() => {
                    onBackToHome();
                    setShowMenu(false);
                  }}
                  className="w-full justify-start gap-3 text-gray-700 hover:bg-gray-100"
                >
                  <Home className="w-4 h-4" />
                  Back to Home
                </Button>
              )}
              
              <Button
                variant="ghost"
                className="w-full justify-start gap-3 text-gray-700 hover:bg-gray-100"
                onClick={() => {
                  setShowMenu(false);
                }}
              >
                <MapPin className="w-4 h-4" />
                My Location
              </Button>
            </motion.div>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMenu(true)}
              className="backdrop-blur-xl bg-white/80 border-white/40 hover:bg-white/90 w-12 h-12 p-0 rounded-full shadow-lg"
            >
              <Menu className="w-5 h-5" />
            </Button>
          )}
        </div>

        {/* Header */}
        <div className="absolute top-0 left-0 right-0 z-20 p-6">
          <div className="flex items-center justify-between">
            <div className="ml-16"> {/* Space for left navigation */}
              <h2 className="text-2xl font-bold text-gray-800">Live City View</h2>
              <p className="text-gray-600">Real-time hotspots powered by Bytspot AI</p>
            </div>
            <Badge variant="outline" className="bg-green-100 border-green-300 text-green-700">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
              Live
            </Badge>
          </div>
        </div>

        {/* Custom Styled Map Area (Simulating Google Maps with custom styling) */}
        <div className="absolute inset-0 pt-24 p-6">
          <div className="relative w-full h-full map-container rounded-2xl overflow-hidden shadow-inner">
            {/* Map Overlay for Snap Map effect */}
            <div className="absolute inset-0 map-overlay" />
            
            {/* Street Grid Pattern (Simulating simplified roads) */}
            <svg className="absolute inset-0 w-full h-full opacity-20" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(156, 163, 175, 0.5)" strokeWidth="0.5"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
            
            {/* Dynamic Map Pins from API */}
            {mapPins.map((pin) => {
              // Convert lat/lng to screen coordinates (simplified conversion)
              const x = ((pin.longitude + 122.4394) / 0.04) * 100;
              const y = ((37.7949 - pin.latitude) / 0.04) * 100;
              
              return (
                <motion.div
                  key={pin.place_id}
                  className="absolute"
                  style={{ 
                    left: `${Math.max(5, Math.min(95, x))}%`, 
                    top: `${Math.max(5, Math.min(95, y))}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                  whileHover={{ scale: 1.2 }}
                  onClick={() => handlePinClick(pin)}
                >
                  {/* Pulsating Glow Effect */}
                  {(pin.status === 'trending' || pin.status === 'prime') && (
                    <motion.div
                      className="absolute inset-0 rounded-full opacity-30"
                      style={getPulseStyle(pin)}
                      animate={{
                        scale: [1, 1.6, 1],
                        opacity: [0.4, 0.1, 0.4],
                      }}
                      transition={{
                        duration: pin.status === 'trending' ? 1.5 : 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                  )}
                  
                  {/* Custom Marker */}
                  <div className={getMarkerStyle(pin)}>
                    {/* Icon based on type */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      {pin.type === 'venue' ? 
                        <Users className="w-3 h-3 text-white" /> :
                        pin.type === 'parking' ? 
                        <Car className="w-3 h-3 text-white" /> :
                        <TrendingUp className="w-3 h-3 text-white" />
                      }
                    </div>
                  </div>
                  
                  {/* Status Label */}
                  {(pin.status === 'trending' || pin.status === 'prime') && (
                    <motion.div
                      className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <div className="backdrop-blur-sm bg-white/90 px-2 py-1 rounded-full shadow-md border border-white/60">
                        <span className="text-xs font-medium text-gray-700">{pin.name}</span>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Enhanced Legend with Real-time Status */}
        <div className="absolute bottom-6 left-6 right-20 z-30"> {/* Space for FAB */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="backdrop-blur-xl bg-white/90 rounded-2xl p-4 border border-white/40 shadow-lg"
          >
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full trending-marker" />
                  <span className="font-medium text-gray-700">Trending</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full prime-marker" />
                  <span className="font-medium text-gray-700">Prime</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full" />
                  <span className="font-medium text-gray-700">Available</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gradient-to-r from-red-500 to-red-600 rounded-full" />
                  <span className="font-medium text-gray-700">Busy</span>
                </div>
              </div>
            </div>
            
            {/* Live Update Indicator */}
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="flex items-center gap-2 justify-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span className="text-xs text-gray-600">Updated {new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Floating Action Button (FAB) for Live Vibe Capture */}
        <motion.div
          className="absolute bottom-24 right-6 z-40" // Above navigation bar
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.8, type: 'spring', stiffness: 300 }}
        >
          <motion.button
            onClick={() => setShowLiveVibeCamera(true)}
            className="relative w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full shadow-2xl flex items-center justify-center border-4 border-white/40"
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
            transition={{ type: 'spring', stiffness: 400 }}
          >
            <Video className="w-7 h-7 text-white relative z-10" />
            
            {/* Continuous Pulse Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
              animate={{
                scale: [1, 1.4, 1],
                opacity: [0.3, 0, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            
            {/* Capture Hint */}
            <motion.div
              className="absolute -top-12 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1.5 }}
            >
              <div className="backdrop-blur-sm bg-black/70 text-white px-3 py-1 rounded-full text-xs">
                📹 Capture Live Vibe
              </div>
            </motion.div>
          </motion.button>
        </motion.div>

        {/* Venue Detail Overlay */}
        {renderVenueDetail()}

        {/* Live Vibe Camera Modal */}
        <LiveVibeCamera
          isOpen={showLiveVibeCamera}
          onClose={() => setShowLiveVibeCamera(false)}
          currentLocation={currentLocation}
        />
      </div>
    </div>
  );
}