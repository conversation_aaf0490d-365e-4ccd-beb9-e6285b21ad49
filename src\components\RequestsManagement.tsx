import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Clock, 
  User, 
  MapPin, 
  Phone, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Users,
  Calendar,
  Star,
  MessageSquare,
  Filter,
  Search,
  MoreHorizontal,
  ArrowRight,
  Zap
} from 'lucide-react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Progress } from './ui/progress';

interface Request {
  id: string;
  type: 'reservation' | 'valet' | 'upgrade' | 'special';
  customer: {
    name: string;
    avatar?: string;
    tier: 'standard' | 'premium' | 'vip';
    rating: number;
  };
  details: {
    service: string;
    time: string;
    party?: number;
    notes?: string;
    urgency: 'low' | 'medium' | 'high';
  };
  status: 'pending' | 'assigned' | 'in-progress' | 'completed' | 'declined';
  assignedTo?: string;
  createdAt: string;
  estimatedDuration?: number;
}

interface StaffMember {
  id: string;
  name: string;
  avatar?: string;
  role: string;
  status: 'available' | 'busy' | 'break' | 'offline';
  activeRequests: number;
  completedToday: number;
  rating: number;
  shift: string;
}

export function RequestsManagement() {
  const [selectedTab, setSelectedTab] = useState('requests');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for requests
  const [requests, setRequests] = useState<Request[]>([
    {
      id: '1',
      type: 'reservation',
      customer: {
        name: 'Sarah Mitchell',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b812b8c5?w=150&h=150&fit=crop&crop=face',
        tier: 'premium',
        rating: 4.9
      },
      details: {
        service: 'VIP Table Reservation',
        time: '8:30 PM',
        party: 4,
        notes: 'Anniversary celebration, prefer rooftop section',
        urgency: 'medium'
      },
      status: 'pending',
      createdAt: '2 min ago'
    },
    {
      id: '2',
      type: 'valet',
      customer: {
        name: 'Michael Chen',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        tier: 'vip',
        rating: 5.0
      },
      details: {
        service: 'Premium Valet Service',
        time: 'Now',
        notes: 'BMW M5, license plate: VIP-123',
        urgency: 'high'
      },
      status: 'assigned',
      assignedTo: 'James Rodriguez',
      createdAt: '5 min ago',
      estimatedDuration: 15
    },
    {
      id: '3',
      type: 'upgrade',
      customer: {
        name: 'Emma Thompson',
        tier: 'standard',
        rating: 4.6
      },
      details: {
        service: 'Table Upgrade Request',
        time: '9:00 PM',
        party: 2,
        notes: 'Currently at table 12, requesting window seat',
        urgency: 'low'
      },
      status: 'in-progress',
      assignedTo: 'Lisa Park',
      createdAt: '12 min ago'
    },
    {
      id: '4',
      type: 'special',
      customer: {
        name: 'David Kim',
        tier: 'premium',
        rating: 4.8
      },
      details: {
        service: 'Special Dietary Request',
        time: '7:45 PM',
        party: 3,
        notes: 'Gluten-free menu needed, severe allergy',
        urgency: 'high'
      },
      status: 'completed',
      assignedTo: 'Chef Maria',
      createdAt: '25 min ago'
    }
  ]);

  // Mock data for staff
  const [staff, setStaff] = useState<StaffMember[]>([
    {
      id: '1',
      name: 'James Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      role: 'Valet Manager',
      status: 'busy',
      activeRequests: 2,
      completedToday: 12,
      rating: 4.9,
      shift: '6 PM - 2 AM'
    },
    {
      id: '2',
      name: 'Lisa Park',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      role: 'Floor Manager',
      status: 'available',
      activeRequests: 1,
      completedToday: 8,
      rating: 4.8,
      shift: '5 PM - 1 AM'
    },
    {
      id: '3',
      name: 'Carlos Martinez',
      role: 'Server',
      status: 'available',
      activeRequests: 0,
      completedToday: 15,
      rating: 4.7,
      shift: '4 PM - 12 AM'
    },
    {
      id: '4',
      name: 'Sophie Anderson',
      avatar: 'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=150&h=150&fit=crop&crop=face',
      role: 'Hostess',
      status: 'busy',
      activeRequests: 3,
      completedToday: 6,
      rating: 4.9,
      shift: '6 PM - 2 AM'
    },
    {
      id: '5',
      name: 'Maria Santos',
      role: 'Chef',
      status: 'available',
      activeRequests: 1,
      completedToday: 9,
      rating: 5.0,
      shift: '3 PM - 11 PM'
    }
  ]);

  const handleRequestAction = (requestId: string, action: 'accept' | 'decline' | 'assign') => {
    setRequests(prev => prev.map(req => {
      if (req.id === requestId) {
        if (action === 'accept') return { ...req, status: 'assigned' };
        if (action === 'decline') return { ...req, status: 'declined' };
      }
      return req;
    }));
  };

  const assignRequest = (requestId: string, staffId: string, staffName: string) => {
    setRequests(prev => prev.map(req => 
      req.id === requestId 
        ? { ...req, status: 'assigned', assignedTo: staffName }
        : req
    ));
    
    setStaff(prev => prev.map(member => 
      member.id === staffId 
        ? { ...member, activeRequests: member.activeRequests + 1 }
        : member
    ));
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800 border-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 border-green-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-orange-100 text-orange-800 border-orange-300';
      case 'assigned': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'in-progress': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'completed': return 'bg-green-100 text-green-800 border-green-300';
      case 'declined': return 'bg-red-100 text-red-800 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'vip': return 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white';
      case 'premium': return 'bg-gradient-to-r from-purple-500 to-blue-500 text-white';
      case 'standard': return 'bg-gray-200 text-gray-800';
      default: return 'bg-gray-200 text-gray-800';
    }
  };

  const getStaffStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'busy': return 'bg-orange-500';
      case 'break': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const filteredRequests = requests.filter(req => {
    const matchesSearch = req.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         req.details.service.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || req.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const activeRequests = requests.filter(req => ['pending', 'assigned', 'in-progress'].includes(req.status));
  const completedToday = requests.filter(req => req.status === 'completed').length;
  const avgResponseTime = 8; // minutes

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Requests & Staff</h1>
            <p className="text-gray-600">Manage incoming requests and coordinate your team</p>
          </div>
          <div className="flex items-center gap-3">
            <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white">
              <Zap className="w-4 h-4 mr-2" />
              Auto-Assign
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{activeRequests.length}</p>
                <p className="text-sm text-gray-600">Active Requests</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{completedToday}</p>
                <p className="text-sm text-gray-600">Completed Today</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{staff.filter(s => s.status === 'available').length}</p>
                <p className="text-sm text-gray-600">Staff Available</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{avgResponseTime}m</p>
                <p className="text-sm text-gray-600">Avg Response</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-white/20 backdrop-blur-sm">
            <TabsTrigger value="requests">Incoming Requests</TabsTrigger>
            <TabsTrigger value="staff">Staff Management</TabsTrigger>
          </TabsList>

          <TabsContent value="requests" className="space-y-6">
            {/* Search and Filter */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input 
                  placeholder="Search requests..." 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/50 border-white/60 backdrop-blur-sm"
                />
              </div>
              <Button 
                variant="outline" 
                className="bg-white/20 border-white/30 hover:bg-white/30"
                onClick={() => setFilterStatus(filterStatus === 'all' ? 'pending' : 'all')}
              >
                <Filter className="w-4 h-4 mr-2" />
                {filterStatus === 'all' ? 'All' : 'Pending'}
              </Button>
            </div>

            {/* Requests List */}
            <div className="space-y-4">
              <AnimatePresence>
                {filteredRequests.map((request) => (
                  <motion.div
                    key={request.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="group"
                  >
                    <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6 hover:bg-white/30 transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4 flex-1">
                          {/* Customer Info */}
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={request.customer.avatar} />
                            <AvatarFallback>{request.customer.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 space-y-3">
                            <div className="flex items-center gap-3">
                              <h3 className="font-semibold text-gray-800">{request.customer.name}</h3>
                              <Badge className={getTierColor(request.customer.tier)}>
                                {request.customer.tier.toUpperCase()}
                              </Badge>
                              <div className="flex items-center gap-1">
                                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                                <span className="text-xs text-gray-600">{request.customer.rating}</span>
                              </div>
                            </div>
                            
                            <div className="space-y-2">
                              <div className="flex items-center gap-4">
                                <span className="font-medium text-gray-700">{request.details.service}</span>
                                <div className="flex items-center gap-2 text-gray-600">
                                  <Clock className="w-4 h-4" />
                                  <span>{request.details.time}</span>
                                </div>
                                {request.details.party && (
                                  <div className="flex items-center gap-2 text-gray-600">
                                    <Users className="w-4 h-4" />
                                    <span>{request.details.party} people</span>
                                  </div>
                                )}
                              </div>
                              
                              {request.details.notes && (
                                <p className="text-sm text-gray-600 bg-white/30 p-2 rounded-lg">
                                  {request.details.notes}
                                </p>
                              )}
                              
                              <div className="flex items-center gap-3">
                                <Badge variant="outline" className={getUrgencyColor(request.details.urgency)}>
                                  {request.details.urgency} priority
                                </Badge>
                                <Badge variant="outline" className={getStatusColor(request.status)}>
                                  {request.status}
                                </Badge>
                                {request.assignedTo && (
                                  <span className="text-sm text-gray-600">
                                    Assigned to: <span className="font-medium">{request.assignedTo}</span>
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          {request.status === 'pending' && (
                            <>
                              <Button 
                                size="sm" 
                                className="bg-green-600 hover:bg-green-700 text-white"
                                onClick={() => handleRequestAction(request.id, 'accept')}
                              >
                                <CheckCircle className="w-4 h-4 mr-1" />
                                Accept
                              </Button>
                              <Button 
                                size="sm" 
                                variant="outline"
                                className="border-red-300 text-red-600 hover:bg-red-50"
                                onClick={() => handleRequestAction(request.id, 'decline')}
                              >
                                <XCircle className="w-4 h-4 mr-1" />
                                Decline
                              </Button>
                            </>
                          )}
                          
                          {request.status === 'assigned' && request.estimatedDuration && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Clock className="w-4 h-4" />
                              <span>{request.estimatedDuration}m left</span>
                            </div>
                          )}
                          
                          <Button size="sm" variant="ghost">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="mt-4 pt-4 border-t border-white/30 flex items-center justify-between text-sm text-gray-600">
                        <span>Received {request.createdAt}</span>
                        {request.type === 'valet' && (
                          <div className="flex items-center gap-1">
                            <MapPin className="w-4 h-4" />
                            <span>Vehicle pickup required</span>
                          </div>
                        )}
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </TabsContent>

          <TabsContent value="staff" className="space-y-6">
            {/* Staff Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {['available', 'busy', 'break', 'offline'].map((status) => (
                <Card key={status} className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${getStaffStatusColor(status)}`} />
                    <div>
                      <p className="font-medium text-gray-800 capitalize">{status}</p>
                      <p className="text-2xl font-bold text-gray-800">
                        {staff.filter(s => s.status === status).length}
                      </p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Staff List */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {staff.map((member) => (
                <Card key={member.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStaffStatusColor(member.status)}`} />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800">{member.name}</h3>
                        <p className="text-sm text-gray-600">{member.role}</p>
                        <div className="flex items-center gap-1 mt-1">
                          <Star className="w-3 h-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-600">{member.rating}</span>
                        </div>
                      </div>
                    </div>
                    <Badge variant="outline" className={`${
                      member.status === 'available' ? 'bg-green-100 text-green-800 border-green-300' :
                      member.status === 'busy' ? 'bg-orange-100 text-orange-800 border-orange-300' :
                      'bg-gray-100 text-gray-800 border-gray-300'
                    }`}>
                      {member.status}
                    </Badge>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Active Requests</p>
                        <p className="font-semibold text-gray-800">{member.activeRequests}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Completed Today</p>
                        <p className="font-semibold text-gray-800">{member.completedToday}</p>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-600">Workload</span>
                        <span className="text-gray-800">{member.activeRequests}/5</span>
                      </div>
                      <Progress value={(member.activeRequests / 5) * 100} className="h-2" />
                    </div>
                    
                    <div className="flex items-center justify-between pt-2 border-t border-white/30">
                      <span className="text-sm text-gray-600">{member.shift}</span>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
                          <MessageSquare className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
                          <Phone className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}