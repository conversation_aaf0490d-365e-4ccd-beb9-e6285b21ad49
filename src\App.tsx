import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoginForm } from './components/auth/LoginForm';
import { MatchCardPage } from './components/MatchCardPage';
import { MapView } from './components/MapView';
import { Navigation } from './components/Navigation';
import { VenueDetail } from './components/VenueDetail';
import { ParkingDetail } from './components/ParkingDetail';
import { ValetDetail } from './components/ValetDetail';
import { Profile } from './components/Profile';
import { ProfileSelection } from './components/ProfileSelection';
import { Registration } from './components/Registration';
import { HostOnboarding } from './components/HostOnboarding';
import { VenueManagerDashboard } from './components/VenueManagerDashboard';
import { VenueAnalytics } from './components/VenueAnalytics';
import { RequestsManagement } from './components/RequestsManagement';
import { ReservationFlow } from './components/ReservationFlow';
import { CollaborativeDiscovery } from './components/CollaborativeDiscovery';
import { FriendNotifications } from './components/FriendNotifications';
import { SocialHub } from './components/SocialHub';
import { ServiceStatus } from './components/ServiceStatus';
import { motion } from 'motion/react';
// Import the safe store instead of the problematic one
import { useAppStore } from './lib/store/safeStore';

// Simple loading component
function LoadingApp() {
  return (
    <div className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center space-y-4"
      >
        <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto flex items-center justify-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-8 h-8 border-2 border-white border-t-transparent rounded-full"
          />
        </div>
        <h2>Loading Bytspot...</h2>
        <p className="text-gray-600">Preparing your personalized experience</p>
      </motion.div>
    </div>
  );
}

// 404 page
function NotFound() {
  return (
    <div className="h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
        <p className="text-gray-600 mb-4">Page not found</p>
        <button 
          onClick={() => window.history.back()}
          className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md"
        >
          Go Back
        </button>
      </div>
    </div>
  );
}

// Simple unauthorized component
function Unauthorized() {
  return (
    <div className="h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">403</h1>
        <p className="text-gray-600 mb-4">You don't have permission to access this page</p>
        <div className="space-x-4">
          <button 
            onClick={() => window.location.href = '/app'}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md"
          >
            Go to App
          </button>
          <button 
            onClick={() => window.history.back()}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md"
          >
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
}

// Consumer app content
function AppContent() {
  // Mock user for testing
  const user = { id: '1', name: 'Test User', role: 'consumer' };
  const navigate = useNavigate();
  const location = useLocation();
  
  const [hasNewSocialActivity, setHasNewSocialActivity] = useState(false);
  const [userSocialProfile, setUserSocialProfile] = useState({
    name: 'Alex Chen',
    vibeScore: 9,
    visitedSpots: 7,
    friendsReferred: 2,
    points: 2450,
    tier: 'Gold',
    achievements: []
  });

  // Determine current view from pathname
  const getCurrentView = (): 'matches' | 'map' | 'insider' | 'concierge' | 'profile' | 'social' => {
    const path = location.pathname.split('/')[2]; // Get the part after /app/
    switch (path) {
      case 'matches':
        return 'matches';
      case 'map':
        return 'map';
      case 'social':
        return 'social';
      case 'profile':
        return 'profile';
      case 'collaborative':
      case 'insider':
        return 'insider';
      default:
        return 'matches';
    }
  };

  const currentView = getCurrentView();

  const handleNavigationChange = (view: 'matches' | 'map' | 'insider' | 'concierge' | 'profile' | 'social') => {
    // Clear social activity indicator when switching to social
    if (view === 'social') {
      setHasNewSocialActivity(false);
    }

    // Navigate to the appropriate route
    switch (view) {
      case 'matches':
        navigate('/app/matches');
        break;
      case 'map':
        navigate('/app/map');
        break;
      case 'insider':
        navigate('/app/collaborative');
        break;
      case 'social':
        navigate('/app/social');
        break;
      case 'profile':
        navigate('/app/profile');
        break;
      default:
        navigate('/app/matches');
    }
  };

  const handleVibeScoreEarned = async (venueData: any) => {
    console.log('Vibe score earned:', venueData);
  };

  const handleShareVibeScore = async (platform: string) => {
    console.log('Share vibe score on:', platform);
  };

  const handleStartChallenge = async (challenge: any) => {
    console.log('Starting challenge:', challenge);
  };

  const handleInviteFriend = async () => {
    console.log('Inviting friend');
  };

  const handleViewProfile = () => {
    console.log('Viewing profile');
    navigate('/app/profile');
  };

  return (
    <div className="h-full">
      <Routes>
        <Route path="matches" element={<MatchCardPage />} />
        <Route path="map" element={<MapView />} />
        <Route 
          path="venue/:venueId" 
          element={<VenueDetail onVibeScoreEarned={handleVibeScoreEarned} />} 
        />
        <Route path="parking/:parkingId" element={<ParkingDetail />} />
        <Route path="valet/:valetId" element={<ValetDetail />} />
        <Route path="profile" element={<Profile />} />
        <Route path="reservation" element={<ReservationFlow />} />
        <Route path="collaborative" element={<CollaborativeDiscovery />} />
        <Route 
          path="social" 
          element={
            <SocialHub 
              userProfile={userSocialProfile}
              onShareVibeScore={handleShareVibeScore}
              onStartChallenge={handleStartChallenge}
              onInviteFriend={handleInviteFriend}
              onViewProfile={handleViewProfile}
            />
          } 
        />
        <Route index element={<Navigate to="matches" replace />} />
      </Routes>
      
      {/* Global components */}
      <FriendNotifications />
      <Navigation 
        currentView={currentView}
        hasNewSocialActivity={hasNewSocialActivity}
        onViewChange={handleNavigationChange}
      />
      <ServiceStatus />
    </div>
  );
}

// Business dashboard content
function BusinessContent() {
  return (
    <div className="h-full">
      <Routes>
        <Route path="dashboard" element={<VenueManagerDashboard venueName="Rooftop Lounge" />} />
        <Route path="analytics" element={<VenueAnalytics />} />
        <Route path="requests" element={<RequestsManagement />} />
        <Route index element={<Navigate to="dashboard" replace />} />
      </Routes>
    </div>
  );
}

// Routes component that can safely use navigation hooks
function AppRoutes({ isAuthenticated, user }: { isAuthenticated: boolean; user: any }) {
  const navigate = useNavigate();
  
  return (
    <Routes>
      {/* Public routes */}
      <Route 
        path="/login" 
        element={
          isAuthenticated ? (
            <Navigate to="/app" replace />
          ) : (
            <div className="h-screen flex items-center justify-center p-4">
              <LoginForm
                onSuccess={() => navigate('/app')}
                onSwitchToRegister={() => navigate('/register')}
                onForgotPassword={() => navigate('/forgot-password')}
              />
            </div>
          )
        } 
      />
      
      {/* Registration route */}
      <Route 
        path="/register" 
        element={
          isAuthenticated ? (
            <Navigate to="/app" replace />
          ) : (
            <div className="h-screen flex items-center justify-center p-4">
              <Registration onSuccess={() => navigate('/app')} />
            </div>
          )
        } 
      />

      {/* Profile selection route */}
      <Route 
        path="/profile-selection" 
        element={
          <div className="h-screen flex items-center justify-center p-4">
            <ProfileSelection onComplete={() => navigate('/app')} />
          </div>
        } 
      />

      {/* Host onboarding route */}
      <Route 
        path="/host-onboarding" 
        element={
          <ProtectedRoute>
            <HostOnboarding onComplete={() => navigate('/business')} />
          </ProtectedRoute>
        } 
      />
      
      {/* Forgot password placeholder */}
      <Route 
        path="/forgot-password" 
        element={
          <div className="h-screen flex items-center justify-center p-4">
            <div className="text-center space-y-4">
              <h2>Password Reset</h2>
              <p className="text-gray-600">Password reset functionality coming soon</p>
              <button 
                onClick={() => navigate('/login')}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md"
              >
                Back to Login
              </button>
            </div>
          </div>
        } 
      />
      
      {/* Protected routes */}
      <Route 
        path="/app/*" 
        element={
          <ProtectedRoute>
            <AppContent />
          </ProtectedRoute>
        } 
      />
      
      {/* Business routes */}
      <Route 
        path="/business/*" 
        element={
          <ProtectedRoute requiredRole="host">
            <BusinessContent />
          </ProtectedRoute>
        } 
      />
      
      {/* Default redirect */}
      <Route 
        path="/" 
        element={
          <Navigate to={isAuthenticated ? "/app" : "/login"} replace />
        } 
      />
      
      {/* 404 and error pages */}
      <Route path="/unauthorized" element={<Unauthorized />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

// Root App component - this is called by main.tsx which wraps it with AppProviders
export default function App() {
  try {
    // Mock authentication for testing
    const isAuthenticated = true;
    const isLoading = false;
    const user = { id: '1', name: 'Test User', role: 'consumer' };

    // Show loading state while checking authentication
    if (isLoading) {
      return <LoadingApp />;
    }

    return (
      <div className="h-screen bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100 overflow-hidden">
        <AppRoutes isAuthenticated={isAuthenticated} user={user} />
      </div>
    );
  } catch (error) {
    console.error('App component error:', error);
    
    // Fallback UI for critical errors
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
        <div className="text-center space-y-4 p-8 bg-white/80 backdrop-blur-sm rounded-lg shadow-lg max-w-md">
          <h2 className="text-xl font-semibold text-red-600">Application Error</h2>
          <p className="text-gray-600">Sorry, something went wrong while loading the app.</p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md"
          >
            Reload App
          </button>
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-sm text-gray-500">Error Details</summary>
            <pre className="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded overflow-auto max-h-32">
              {error instanceof Error ? error.message : 'Unknown error'}
            </pre>
          </details>
        </div>
      </div>
    );
  }
}