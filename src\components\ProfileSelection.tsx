import { useState } from 'react';
import { motion } from 'motion/react';
import { ArrowLeft, User, Building2, Check, Users, Car, MapPin, Star, Shield } from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';

interface ProfileSelectionProps {
  onBack: () => void;
  onProfileSelect: (profileType: 'individual' | 'business', context?: 'host' | 'feature' | 'search') => void;
  context?: 'host' | 'feature' | 'search';
  selectedFeature?: string;
}

type ProfileType = 'individual' | 'business';

export function ProfileSelection({ onBack, onProfileSelect, context, selectedFeature }: ProfileSelectionProps) {
  const [selectedProfile, setSelectedProfile] = useState<ProfileType | null>(null);

  const getContextMessage = () => {
    switch (context) {
      case 'host':
        return {
          title: 'Choose Your Profile Type',
          subtitle: 'Select how you want to use Bytspot as a host',
          description: 'You can always change this later in your account settings'
        };
      case 'feature':
        return {
          title: 'Create Your Account',
          subtitle: `Get started with ${selectedFeature} on Bytspot`,
          description: 'Choose your profile type to personalize your experience'
        };
      case 'search':
        return {
          title: 'Welcome to Bytspot!',
          subtitle: 'Create your account to start discovering amazing spots',
          description: 'Choose your profile type to get personalized recommendations'
        };
      default:
        return {
          title: 'Create Your Account',
          subtitle: 'Choose how you want to use Bytspot',
          description: 'Select the profile type that best fits your needs'
        };
    }
  };

  const contextInfo = getContextMessage();

  const profileTypes = [
    {
      type: 'individual' as ProfileType,
      title: 'Individual Profile',
      description: 'For personal use - discover and book amazing spots',
      icon: User,
      color: 'from-purple-500 to-pink-500',
      benefits: [
        'Personalized recommendations',
        'Easy booking and payments',
        'Loyalty rewards and points',
        'Exclusive member discounts',
        'Social sharing features',
        'Favorite spots collection'
      ],
      badge: 'Most Popular'
    },
    {
      type: 'business' as ProfileType,
      title: 'Business Profile',
      description: 'For organizations and companies managing multiple bookings',
      icon: Building2,
      color: 'from-blue-500 to-cyan-500',
      benefits: [
        'Team booking management',
        'Corporate billing & reports',
        'Advanced analytics dashboard',
        'Bulk booking discounts',
        'Priority customer support',
        'Custom approval workflows'
      ],
      badge: context === 'host' ? 'Recommended for Hosts' : 'Enterprise'
    }
  ];

  const handleContinue = () => {
    if (selectedProfile) {
      onProfileSelect(selectedProfile, context);
    }
  };

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="ghost"
            onClick={onBack}
            className="hover:bg-white/30"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800">{contextInfo.title}</h1>
          </div>
          <div className="w-20"></div> {/* Spacer */}
        </div>

        {/* Welcome Message */}
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="relative mb-6">
            <motion.div
              className="w-20 h-20 mx-auto bg-gradient-to-br from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-2xl"
              whileHover={{ scale: 1.05 }}
              animate={{
                boxShadow: [
                  '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
                  '0 25px 50px -12px rgba(167, 139, 250, 0.25)',
                  '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
                ]
              }}
              transition={{
                boxShadow: { duration: 3, repeat: Infinity, ease: "easeInOut" }
              }}
            >
              <Star className="w-10 h-10 text-white" />
            </motion.div>
          </div>
          
          <h2 className="text-3xl font-bold text-gray-800 mb-3">
            {contextInfo.subtitle}
          </h2>
          <p className="text-gray-600 text-lg max-w-md mx-auto">
            {contextInfo.description}
          </p>
        </motion.div>

        {/* Profile Type Cards */}
        <div className="grid gap-6 mb-8">
          {profileTypes.map((profile, index) => {
            const Icon = profile.icon;
            const isSelected = selectedProfile === profile.type;
            
            return (
              <motion.div
                key={profile.type}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
              >
                <Card 
                  className={`p-6 cursor-pointer transition-all duration-200 border-2 ${
                    isSelected 
                      ? 'border-purple-500 bg-purple-50 shadow-lg' 
                      : 'border-gray-200 hover:border-purple-300 hover:shadow-md'
                  }`}
                  onClick={() => setSelectedProfile(profile.type)}
                >
                  <div className="flex items-start gap-6">
                    {/* Icon */}
                    <div className={`p-4 rounded-2xl bg-gradient-to-r ${profile.color} text-white flex-shrink-0`}>
                      <Icon className="w-8 h-8" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-3">
                        <h3 className="text-xl font-semibold text-gray-800">
                          {profile.title}
                        </h3>
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${
                            profile.type === 'individual' 
                              ? 'bg-purple-100 text-purple-800' 
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {profile.badge}
                        </Badge>
                        {isSelected && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="ml-auto"
                          >
                            <Check className="w-6 h-6 text-purple-600" />
                          </motion.div>
                        )}
                      </div>
                      
                      <p className="text-gray-600 mb-4">
                        {profile.description}
                      </p>

                      {/* Benefits */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {profile.benefits.map((benefit) => (
                          <div key={benefit} className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${profile.color}`} />
                            <span className="text-sm text-gray-700">{benefit}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Continue Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Button
            onClick={handleContinue}
            disabled={!selectedProfile}
            className="w-full py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 rounded-2xl shadow-xl h-14"
          >
            <span className="font-semibold text-lg">
              Continue with {selectedProfile === 'individual' ? 'Individual' : 'Business'} Profile
            </span>
          </Button>
        </motion.div>

        {/* Security Note */}
        <motion.div
          className="mt-6 p-4 bg-white/50 border border-white/30 rounded-xl backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
        >
          <div className="flex items-center gap-3">
            <Shield className="w-5 h-5 text-gray-500" />
            <div>
              <p className="text-sm text-gray-700">
                <strong>Your data is secure.</strong> We use industry-standard encryption and never share your personal information with third parties.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Context-specific Information */}
        {context === 'host' && (
          <motion.div
            className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <div className="flex items-start gap-3">
              <Building2 className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">Hosting with Bytspot</h4>
                <p className="text-blue-800 text-sm">
                  After creating your profile, you'll go through our host onboarding process to verify your location and set up your services.
                  Business profiles get additional management tools and analytics.
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}