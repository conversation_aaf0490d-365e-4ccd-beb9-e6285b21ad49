import React, { useState } from 'react';
import { MatchCard } from './MatchCard';
import { motion, AnimatePresence } from 'motion/react';

// Mock match data
const mockMatches = [
  {
    id: 'p1',
    type: 'venue' as const,
    title: 'Rooftop Lounge',
    subtitle: 'Sophisticated cocktails & city views',
    distance: '0.3 miles',
    availability: 'Open now',
    rating: 4.8,
    price: '$$$',
    image: 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=400&h=600&fit=crop',
    details: {
      description: 'Upscale rooftop bar with panoramic city views, craft cocktails, and sophisticated atmosphere.',
      features: ['City Views', 'Craft Cocktails', 'Upscale', 'Rooftop'],
      footprint: 85,
      vibe: 'sophisticated',
      insights: {
        whyMatch: 'Perfect for your preference for upscale venues with great views and sophisticated atmosphere.',
        liveData: {
          currentCrowd: 85,
          maxCapacity: 120,
          trending: 'Hot spot tonight',
          moodScore: 8.5,
          averageAge: '25-35',
          peakTime: '9-11 PM'
        },
        socialProof: '3 of your friends were here this week and rated it highly!',
        bestTime: 'Visit between 7-9 PM for the best sunset views and less crowded atmosphere',
        insiderTip: 'Ask for the secret menu - they have amazing off-menu cocktails',
        offers: [
          'Happy hour 5-7 PM: 30% off cocktails',
          'Free appetizer with any bottle purchase'
        ]
      }
    }
  },
  {
    id: '2',
    type: 'parking' as const,
    title: 'Downtown Garage',
    subtitle: 'Secure covered parking',
    distance: '0.1 miles',
    availability: '24/7',
    rating: 4.2,
    price: '$12/hr',
    image: 'https://images.unsplash.com/photo-1590674899484-d5640e854abe?w=400&h=600&fit=crop',
    details: {
      description: 'Convenient downtown parking garage with 24/7 access and security.',
      features: ['Covered', 'Secure', '24/7', 'EV Charging'],
      footprint: 45
    }
  },
  {
    id: '3',
    type: 'valet' as const,
    title: 'Premium Valet',
    subtitle: 'Luxury car service',
    distance: '0.2 miles',
    availability: 'Available',
    rating: 4.9,
    price: '$25',
    image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=600&fit=crop',
    details: {
      description: 'Professional valet service with luxury vehicle handling and insurance coverage.',
      features: ['Luxury Service', 'Insured', 'Professional', 'Fast'],
      footprint: 12
    }
  }
];

export function MatchCardPage() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [matchedItems, setMatchedItems] = useState<string[]>([]);
  const [passedItems, setPassedItems] = useState<string[]>([]);

  const currentMatch = mockMatches[currentIndex];

  const handleSwipe = (direction: 'left' | 'right') => {
    if (!currentMatch) return;

    if (direction === 'right') {
      setMatchedItems(prev => [...prev, currentMatch.id]);
    } else {
      setPassedItems(prev => [...prev, currentMatch.id]);
    }

    // Move to next match
    setCurrentIndex(prev => prev + 1);
  };

  const handleReservationRequest = (request: {
    venueId: string;
    venueName: string;
    date: string;
    time: string;
    partySize: number;
  }) => {
    console.log('Reservation requested:', request);
    // Here you would typically navigate to a reservation flow or show a confirmation
  };

  const handleStartCollaborative = (sessionName: string, selectedFriends: any[]) => {
    console.log('Starting collaborative session:', sessionName, selectedFriends);
    // Here you would typically start a collaborative decision session
  };

  // Mock friends data
  const mockFriends = [
    { id: '1', name: 'Sarah Chen', avatar: 'SC' },
    { id: '2', name: 'Mike Rodriguez', avatar: 'MR' },
    { id: '3', name: 'Emma Wilson', avatar: 'EW' },
  ];

  if (currentIndex >= mockMatches.length) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-4"
        >
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto flex items-center justify-center">
            <span className="text-2xl">🎉</span>
          </div>
          <h2 className="text-2xl font-bold text-gray-800">All caught up!</h2>
          <p className="text-gray-600">You've seen all available matches in your area.</p>
          <div className="space-y-2 text-sm text-gray-500">
            <p>Matched: {matchedItems.length} items</p>
            <p>Passed: {passedItems.length} items</p>
          </div>
          <button 
            onClick={() => {
              setCurrentIndex(0);
              setMatchedItems([]);
              setPassedItems([]);
            }}
            className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-full"
          >
            Start Over
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="h-full relative">
      <AnimatePresence mode="wait">
        {currentMatch && (
          <motion.div
            key={currentMatch.id}
            initial={{ opacity: 0, scale: 0.8, rotateY: 90 }}
            animate={{ opacity: 1, scale: 1, rotateY: 0 }}
            exit={{ opacity: 0, scale: 0.8, rotateY: -90 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            <MatchCard
              match={currentMatch}
              onSwipe={handleSwipe}
              onReservationRequest={handleReservationRequest}
              onStartCollaborative={handleStartCollaborative}
              friends={mockFriends}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Progress indicator */}
      <div className="absolute top-4 left-4 right-4 z-10">
        <div className="flex gap-1">
          {mockMatches.map((_, index) => (
            <div
              key={index}
              className={`h-1 flex-1 rounded-full transition-all duration-300 ${
                index < currentIndex
                  ? 'bg-green-400'
                  : index === currentIndex
                  ? 'bg-white'
                  : 'bg-white/30'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Swipe instructions */}
      {currentIndex === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center text-white/80 z-10"
        >
          <p className="text-sm">Swipe right to match, left to pass</p>
          <div className="flex items-center justify-center gap-4 mt-2">
            <div className="flex items-center gap-1">
              <span className="text-red-400">←</span>
              <span className="text-xs">Pass</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-green-400">→</span>
              <span className="text-xs">Match</span>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}