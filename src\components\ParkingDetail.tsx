import { useState } from 'react';
import { motion } from 'motion/react';
import { ArrowLeft, MapPin, Car, Shield, Zap, Clock, Star, Navigation, CheckCircle, AlertCircle, CreditCard, Phone, Camera } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { PaymentFlow } from './PaymentFlow';

interface ParkingDetailProps {
  match: {
    id: string;
    title: string;
    subtitle: string;
    distance: string;
    availability: string;
    price?: string;
    image: string;
    details: {
      description: string;
      features: string[];
    };
  };
  onBack: () => void;
}

export function ParkingDetail({ match, onBack }: ParkingDetailProps) {
  const [selectedDuration, setSelectedDuration] = useState('2');
  const [showPayment, setShowPayment] = useState(false);
  
  const durations = [
    { value: '1', label: '1 Hour', price: '$12' },
    { value: '2', label: '2 Hours', price: '$22' },
    { value: '4', label: '4 Hours', price: '$40' },
    { value: '8', label: '8 Hours', price: '$65' },
    { value: 'day', label: 'All Day', price: '$85' }
  ];

  const getFeatureIcon = (feature: string) => {
    if (feature.includes('Security')) return <Shield className="w-4 h-4 text-blue-500" />;
    if (feature.includes('EV') || feature.includes('Charging')) return <Zap className="w-4 h-4 text-green-500" />;
    if (feature.includes('Covered')) return <Car className="w-4 h-4 text-purple-500" />;
    if (feature.includes('Camera')) return <Camera className="w-4 h-4 text-orange-500" />;
    return <Car className="w-4 h-4 text-gray-500" />;
  };

  const selectedPrice = durations.find(d => d.value === selectedDuration)?.price || '$22';

  const handlePaymentComplete = (paymentDetails: any) => {
    console.log('Payment completed:', paymentDetails);
    setShowPayment(false);
    onBack();
  };

  const handleReserveNow = () => {
    setShowPayment(true);
  };

  if (showPayment) {
    return (
      <PaymentFlow
        service={{
          id: match.id,
          type: 'parking',
          title: match.title,
          subtitle: match.subtitle,
          basePrice: parseFloat(selectedPrice.replace('$', '')),
          location: match.distance,
          features: match.details.features
        }}
        onBack={() => setShowPayment(false)}
        onComplete={handlePaymentComplete}
      />
    );
  }

  return (
    <div className="h-full bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="h-full overflow-auto"
      >
        {/* Header Image */}
        <div className="relative h-48 bg-cover bg-center" style={{ backgroundImage: `url(${match.image})` }}>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          
          {/* Header Controls */}
          <div className="absolute top-4 left-4 right-4 flex justify-between items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="backdrop-blur-sm bg-white/20 text-white border-white/30 hover:bg-white/30"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="backdrop-blur-sm bg-white/20 text-white border-white/30 hover:bg-white/30"
              >
                <Navigation className="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="backdrop-blur-sm bg-white/20 text-white border-white/30 hover:bg-white/30"
              >
                <Phone className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Title Overlay */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge className="bg-green-500 text-white">Available Now</Badge>
              <Badge variant="outline" className="bg-white/20 border-white/30 text-white">
                {match.distance}
              </Badge>
            </div>
            <h1 className="text-2xl font-bold text-white">{match.title}</h1>
            <p className="text-white/80">{match.subtitle}</p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Quick Info */}
          <div className="grid grid-cols-2 gap-4">
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium">Availability</span>
              </div>
              <p className="font-bold">{match.availability}</p>
            </div>
            
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
              <div className="flex items-center gap-2 mb-2">
                <Car className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium">Starting at</span>
              </div>
              <p className="font-bold">{match.price}</p>
            </div>
          </div>

          {/* Features */}
          <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
            <h3 className="text-lg font-semibold mb-4">Features</h3>
            <div className="grid grid-cols-2 gap-3">
              {match.details.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  {getFeatureIcon(feature)}
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Duration Selection */}
          <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
            <h3 className="text-lg font-semibold mb-4">Select Duration</h3>
            <div className="grid grid-cols-2 gap-3">
              {durations.map((duration) => (
                <Button
                  key={duration.value}
                  variant={selectedDuration === duration.value ? "default" : "outline"}
                  className={`h-auto p-4 flex flex-col items-center gap-1 ${
                    selectedDuration === duration.value 
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' 
                      : 'backdrop-blur-sm bg-white/10 border-white/30 hover:bg-white/20'
                  }`}
                  onClick={() => setSelectedDuration(duration.value)}
                >
                  <span className="text-sm font-medium">{duration.label}</span>
                  <span className="text-lg font-bold">{duration.price}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Description */}
          <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
            <h3 className="text-lg font-semibold mb-3">About This Spot</h3>
            <p className="text-gray-700">{match.details.description}</p>
          </div>

          {/* Location & Access */}
          <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30">
            <h3 className="text-lg font-semibold mb-4">Access Information</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-sm">1</span>
                </div>
                <div>
                  <p className="font-medium">Park in spot B-15</p>
                  <p className="text-sm text-gray-600">Level B, Section 2</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-sm">2</span>
                </div>
                <div>
                  <p className="font-medium">Use access code</p>
                  <p className="text-sm text-gray-600">Code will be sent after reservation</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-sm">3</span>
                </div>
                <div>
                  <p className="font-medium">Take ticket</p>
                  <p className="text-sm text-gray-600">Keep ticket for exit validation</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Reserve Button */}
        <div className="sticky bottom-0 p-6 bg-gradient-to-t from-white via-white/95 to-transparent backdrop-blur-sm">
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => {/* Handle directions */}}
            >
              <Navigation className="w-4 h-4 mr-2" />
              Directions
            </Button>
            
            <Button
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              onClick={handleReserveNow}
            >
              Reserve Now • {selectedPrice}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}