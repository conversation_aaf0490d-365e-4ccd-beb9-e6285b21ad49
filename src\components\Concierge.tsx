import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Bo<PERSON>, Send, MapPin, Clock, Star, Users, Sparkles } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  recommendations?: Recommendation[];
}

interface Recommendation {
  id: string;
  type: 'venue' | 'parking' | 'event';
  title: string;
  subtitle: string;
  rating?: number;
  distance: string;
  image: string;
  matchScore: number;
}

const mockRecommendations: Recommendation[] = [
  {
    id: '1',
    type: 'venue',
    title: 'Skyline Rooftop',
    subtitle: 'Perfect for date night • Romantic atmosphere',
    rating: 4.9,
    distance: '0.3 miles',
    image: 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=400',
    matchScore: 95,
  },
  {
    id: '2',
    type: 'venue', 
    title: 'The Wine Cellar',
    subtitle: 'Intimate setting • Extensive wine list',
    rating: 4.7,
    distance: '0.5 miles',
    image: 'https://images.unsplash.com/photo-1510812431401-41d2bd2722f3?w=400',
    matchScore: 88,
  },
];

export function Concierge() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: "Hi! I'm your AI concierge. I can help you find the perfect spot based on your preferences, mood, and current context. What are you looking for today?",
      timestamp: new Date(),
    }
  ]);
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: "Based on your preference for a romantic evening, I've found some perfect matches nearby. These venues have the right ambiance and are currently not too crowded.",
        timestamp: new Date(),
        recommendations: mockRecommendations,
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 2000);
  };

  return (
    <div className="h-full bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 flex flex-col">
      {/* Header */}
      <div className="backdrop-blur-xl bg-white/30 border-b border-white/40 p-6">
        <div className="flex items-center gap-4">
          <div className="relative">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <motion.div
              className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 2 }}
            />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-800">AI Concierge</h2>
            <p className="text-gray-600">Your personalized recommendation engine</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`
                max-w-[80%] rounded-3xl p-4 backdrop-blur-sm border
                ${message.type === 'user' 
                  ? 'bg-purple-500/80 text-white border-purple-400/50 ml-4' 
                  : 'bg-white/50 text-gray-800 border-white/60 mr-4'
                }
              `}>
                <p className="mb-2">{message.content}</p>
                
                {message.recommendations && (
                  <div className="space-y-3 mt-4">
                    {message.recommendations.map((rec) => (
                      <motion.div
                        key={rec.id}
                        className="backdrop-blur-sm bg-white/40 rounded-2xl p-4 border border-white/50"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex gap-3">
                          <img
                            src={rec.image}
                            alt={rec.title}
                            className="w-16 h-16 rounded-xl object-cover"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="font-semibold text-gray-800 truncate">{rec.title}</h4>
                              <Badge 
                                variant="outline" 
                                className="bg-green-100 border-green-300 text-green-700 ml-2"
                              >
                                <Sparkles className="w-3 h-3 mr-1" />
                                {rec.matchScore}%
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{rec.subtitle}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              {rec.rating && (
                                <div className="flex items-center gap-1">
                                  <Star className="w-3 h-3 text-yellow-500 fill-current" />
                                  <span>{rec.rating}</span>
                                </div>
                              )}
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                <span>{rec.distance}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="bg-white/50 backdrop-blur-sm border border-white/60 rounded-3xl p-4 mr-4">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  {[0, 1, 2].map((i) => (
                    <motion.div
                      key={i}
                      className="w-2 h-2 bg-purple-500 rounded-full"
                      animate={{ y: [0, -8, 0] }}
                      transition={{
                        duration: 0.6,
                        repeat: Infinity,
                        delay: i * 0.2,
                      }}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">AI is thinking...</span>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Input */}
      <div className="backdrop-blur-xl bg-white/30 border-t border-white/40 p-4">
        <div className="flex gap-3">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything... 'Find a romantic dinner spot'"
            className="flex-1 bg-white/50 border-white/60 backdrop-blur-sm"
            onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!input.trim() || isTyping}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        
        {/* Quick Suggestions */}
        <div className="flex gap-2 mt-3 overflow-x-auto">
          {[
            "Find parking near me",
            "Best date night spots",
            "Trending venues now",
            "Need a valet service"
          ].map((suggestion) => (
            <Button
              key={suggestion}
              variant="outline"
              size="sm"
              onClick={() => setInput(suggestion)}
              className="whitespace-nowrap bg-white/30 border-white/50 backdrop-blur-sm hover:bg-white/40"
            >
              {suggestion}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}