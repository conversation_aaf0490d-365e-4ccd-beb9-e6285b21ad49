import { useState } from 'react';
import { motion } from 'motion/react';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Filter,
  Download,
  Users,
  DollarSign,
  Clock,
  Star
} from 'lucide-react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';

export function VenueAnalytics() {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  // Mock data for charts
  const revenueData = [
    { date: 'Mon', revenue: 8500, customers: 120, avgSpend: 71 },
    { date: 'Tue', revenue: 12200, customers: 156, avgSpend: 78 },
    { date: 'Wed', revenue: 9800, customers: 134, avgSpend: 73 },
    { date: 'Thu', revenue: 15600, customers: 189, avgSpend: 83 },
    { date: 'Fri', revenue: 22400, customers: 267, avgSpend: 84 },
    { date: 'Sat', revenue: 28900, customers: 312, avgSpend: 93 },
    { date: 'Sun', revenue: 25100, customers: 289, avgSpend: 87 }
  ];

  const occupancyData = [
    { time: '6 PM', occupancy: 45 },
    { time: '7 PM', occupancy: 78 },
    { time: '8 PM', occupancy: 112 },
    { time: '9 PM', occupancy: 145 },
    { time: '10 PM', occupancy: 167 },
    { time: '11 PM', occupancy: 189 },
    { time: '12 AM', occupancy: 156 },
    { time: '1 AM', occupancy: 123 },
    { time: '2 AM', occupancy: 67 }
  ];

  const demographicsData = [
    { name: '25-34', value: 35, color: '#8B5CF6' },
    { name: '35-44', value: 28, color: '#06B6D4' },
    { name: '18-24', value: 20, color: '#10B981' },
    { name: '45-54', value: 12, color: '#F59E0B' },
    { name: '55+', value: 5, color: '#EF4444' }
  ];

  const satisfactionData = [
    { week: 'Week 1', satisfaction: 4.2, reviews: 45 },
    { week: 'Week 2', satisfaction: 4.4, reviews: 52 },
    { week: 'Week 3', satisfaction: 4.6, reviews: 67 },
    { week: 'Week 4', satisfaction: 4.8, reviews: 78 }
  ];

  // Key metrics calculations
  const totalRevenue = revenueData.reduce((sum, day) => sum + day.revenue, 0);
  const avgCustomersPerDay = Math.round(revenueData.reduce((sum, day) => sum + day.customers, 0) / revenueData.length);
  const revenueGrowth = ((revenueData[6].revenue - revenueData[0].revenue) / revenueData[0].revenue * 100);
  const peakOccupancy = Math.max(...occupancyData.map(d => d.occupancy));

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Analytics Dashboard</h1>
            <p className="text-gray-600">Data insights and performance metrics</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Period Selector */}
        <div className="flex items-center gap-2">
          {['24h', '7d', '30d', '90d'].map((period) => (
            <Button
              key={period}
              variant={selectedPeriod === period ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedPeriod(period)}
              className={selectedPeriod === period 
                ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white" 
                : "bg-white/20 border-white/30 hover:bg-white/30"
              }
            >
              {period}
            </Button>
          ))}
        </div>

        {/* Key Metrics Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                <span className="font-medium text-gray-700">Total Revenue</span>
              </div>
              <TrendingUp className="w-4 h-4 text-green-500" />
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-800">${totalRevenue.toLocaleString()}</p>
              <p className="text-sm text-green-600">+{revenueGrowth.toFixed(1)}% vs last week</p>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Users className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-gray-700">Avg Daily Customers</span>
              </div>
              <TrendingUp className="w-4 h-4 text-green-500" />
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-800">{avgCustomersPerDay}</p>
              <p className="text-sm text-green-600">+12% vs last week</p>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-gray-700">Peak Occupancy</span>
              </div>
              <Badge className="bg-orange-100 text-orange-800 border-0">
                11 PM
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-800">{peakOccupancy}</p>
              <p className="text-sm text-gray-600">people at peak</p>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-600" />
                <span className="font-medium text-gray-700">Satisfaction Trend</span>
              </div>
              <TrendingUp className="w-4 h-4 text-green-500" />
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-800">4.8</p>
              <p className="text-sm text-green-600">+0.6 this month</p>
            </div>
          </Card>
        </div>

        {/* Charts Section */}
        <Tabs defaultValue="revenue" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-white/20 backdrop-blur-sm">
            <TabsTrigger value="revenue">Revenue & Traffic</TabsTrigger>
            <TabsTrigger value="occupancy">Occupancy Patterns</TabsTrigger>
            <TabsTrigger value="demographics">Demographics</TabsTrigger>
            <TabsTrigger value="satisfaction">Satisfaction</TabsTrigger>
          </TabsList>

          <TabsContent value="revenue" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Chart */}
              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Daily Revenue</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                    <XAxis dataKey="date" stroke="#6B7280" />
                    <YAxis stroke="#6B7280" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(255,255,255,0.9)', 
                        border: 'none', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                      }} 
                    />
                    <Bar dataKey="revenue" fill="url(#revenueGradient)" radius={[4, 4, 0, 0]} />
                    <defs>
                      <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#8B5CF6" />
                        <stop offset="100%" stopColor="#06B6D4" />
                      </linearGradient>
                    </defs>
                  </BarChart>
                </ResponsiveContainer>
              </Card>

              {/* Customer Traffic */}
              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Customer Traffic</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                    <XAxis dataKey="date" stroke="#6B7280" />
                    <YAxis stroke="#6B7280" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(255,255,255,0.9)', 
                        border: 'none', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                      }} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="customers" 
                      stroke="#10B981" 
                      fill="url(#customerGradient)" 
                      strokeWidth={2}
                    />
                    <defs>
                      <linearGradient id="customerGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#10B981" stopOpacity={0.6} />
                        <stop offset="100%" stopColor="#10B981" stopOpacity={0.1} />
                      </linearGradient>
                    </defs>
                  </AreaChart>
                </ResponsiveContainer>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="occupancy">
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">Hourly Occupancy Pattern</h3>
                <Badge className="bg-blue-100 text-blue-800 border-0">
                  Average Week
                </Badge>
              </div>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={occupancyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                  <XAxis dataKey="time" stroke="#6B7280" />
                  <YAxis stroke="#6B7280" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'rgba(255,255,255,0.9)', 
                      border: 'none', 
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                    }} 
                  />
                  <Line 
                    type="monotone" 
                    dataKey="occupancy" 
                    stroke="#8B5CF6" 
                    strokeWidth={3}
                    dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 6 }}
                    activeDot={{ r: 8, fill: '#8B5CF6' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Card>
          </TabsContent>

          <TabsContent value="demographics">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Customer Age Groups</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={demographicsData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {demographicsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      formatter={(value) => [`${value}%`, 'Percentage']}
                      contentStyle={{ 
                        backgroundColor: 'rgba(255,255,255,0.9)', 
                        border: 'none', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                      }} 
                    />
                  </PieChart>
                </ResponsiveContainer>
              </Card>

              <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Demographics Breakdown</h3>
                <div className="space-y-4">
                  {demographicsData.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-4 h-4 rounded-full" 
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="font-medium text-gray-700">{item.name} years</span>
                      </div>
                      <div className="text-right">
                        <span className="font-bold text-gray-800">{item.value}%</span>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-6 p-4 bg-white/30 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">Key Insights</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 25-34 age group is your primary demographic</li>
                    <li>• Strong millennial and Gen X presence</li>
                    <li>• Consider targeted marketing for 18-24 segment</li>
                  </ul>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="satisfaction">
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Customer Satisfaction Trend</h3>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={satisfactionData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                  <XAxis dataKey="week" stroke="#6B7280" />
                  <YAxis domain={[3.5, 5]} stroke="#6B7280" />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'satisfaction' ? `${value}/5.0` : value,
                      name === 'satisfaction' ? 'Rating' : 'Reviews'
                    ]}
                    contentStyle={{ 
                      backgroundColor: 'rgba(255,255,255,0.9)', 
                      border: 'none', 
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                    }} 
                  />
                  <Line 
                    type="monotone" 
                    dataKey="satisfaction" 
                    stroke="#F59E0B" 
                    strokeWidth={3}
                    dot={{ fill: '#F59E0B', strokeWidth: 2, r: 6 }}
                    activeDot={{ r: 8, fill: '#F59E0B' }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="reviews" 
                    stroke="#06B6D4" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Predictions & Insights */}
        <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">AI Insights & Predictions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <span className="font-medium text-green-800">Revenue Forecast</span>
              </div>
              <p className="text-2xl font-bold text-green-800">+23%</p>
              <p className="text-sm text-green-700">Expected growth next month</p>
            </div>
            
            <div className="p-4 bg-gradient-to-r from-blue-100 to-cyan-100 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Users className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-blue-800">Peak Times</span>
              </div>
              <p className="text-2xl font-bold text-blue-800">Fri-Sat</p>
              <p className="text-sm text-blue-700">9-11 PM optimal staffing</p>
            </div>
            
            <div className="p-4 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Star className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-purple-800">Optimization</span>
              </div>
              <p className="text-2xl font-bold text-purple-800">Menu</p>
              <p className="text-sm text-purple-700">High-margin items trending</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}