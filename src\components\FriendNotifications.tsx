import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { X, Users, Calendar, MapPin, Check } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

interface Notification {
  id: string;
  type: 'friend_at_venue' | 'collaborative_invite' | 'reservation_confirmed';
  title: string;
  message: string;
  timestamp: string;
  actionLabel?: string;
  venueId?: string;
  friendId?: string;
  collaborativeSessionId?: string;
}

interface FriendNotificationsProps {
  notifications: Notification[];
  onJoinFriend: (friendId: string, venueId: string) => void;
  onDismiss: (notificationId: string) => void;
}

interface DismissingNotification {
  id: string;
  isSuccess: boolean;
}

export function FriendNotifications({ notifications, onJoinFriend, onDismiss }: FriendNotificationsProps) {
  const [dismissingNotifications, setDismissingNotifications] = useState<DismissingNotification[]>([]);

  const handleJoinFriend = (friendId: string, venueId: string, notificationId: string) => {
    // Mark notification as successful
    setDismissingNotifications(prev => [...prev, { id: notificationId, isSuccess: true }]);
    
    // Call the original handler
    onJoinFriend(friendId, venueId);
    
    // Auto-dismiss after showing success state
    setTimeout(() => {
      onDismiss(notificationId);
      setDismissingNotifications(prev => prev.filter(n => n.id !== notificationId));
    }, 2000);
  };

  const handleDismiss = (notificationId: string) => {
    // Immediately dismiss - no delay for manual close button
    onDismiss(notificationId);
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'friend_at_venue':
        return <Users className="w-5 h-5 text-blue-400" />;
      case 'collaborative_invite':
        return <Users className="w-5 h-5 text-purple-400" />;
      case 'reservation_confirmed':
        return <Calendar className="w-5 h-5 text-green-400" />;
      default:
        return <Users className="w-5 h-5 text-gray-400" />;
    }
  };

  const getNotificationColor = (type: Notification['type'], isSuccess?: boolean) => {
    if (isSuccess) {
      return 'from-green-500/30 to-emerald-500/30 border-green-400/50';
    }
    
    switch (type) {
      case 'friend_at_venue':
        return 'from-blue-500/20 to-cyan-500/20 border-blue-400/30';
      case 'collaborative_invite':
        return 'from-purple-500/20 to-pink-500/20 border-purple-400/30';
      case 'reservation_confirmed':
        return 'from-green-500/20 to-emerald-500/20 border-green-400/30';
      default:
        return 'from-gray-500/20 to-gray-500/20 border-gray-400/30';
    }
  };

  if (notifications.length === 0) return null;

  return (
    <div className="absolute top-4 left-4 right-4 z-50 space-y-3">
      <AnimatePresence>
        {notifications.map((notification, index) => {
          const dismissingState = dismissingNotifications.find(d => d.id === notification.id);
          const isDismissing = !!dismissingState;
          const isSuccess = dismissingState?.isSuccess;
          
          return (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, y: -50, scale: 0.9 }}
              animate={{ 
                opacity: isDismissing ? 0.8 : 1, 
                y: 0, 
                scale: isSuccess ? 1.02 : 1,
                x: isDismissing && !isSuccess ? -20 : 0
              }}
              exit={{ opacity: 0, y: -50, scale: 0.9, x: -100 }}
              transition={{ 
                duration: isDismissing ? 0.3 : 0.4, 
                delay: isDismissing ? 0 : index * 0.1,
                type: isSuccess ? "spring" : "tween",
                bounce: 0.3
              }}
              className={`
                relative backdrop-blur-xl bg-gradient-to-r ${getNotificationColor(notification.type, isSuccess)}
                border rounded-2xl p-4 shadow-2xl
                ${isDismissing ? 'pointer-events-none' : ''}
              `}
            >
              {/* Success overlay */}
              {isSuccess && (
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute inset-0 bg-green-500/20 rounded-2xl flex items-center justify-center z-10"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: [0, 1.2, 1] }}
                    transition={{ duration: 0.5 }}
                    className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <Check className="w-6 h-6 text-white" />
                  </motion.div>
                </motion.div>
              )}

              {/* Mobile-optimized Dismiss Button */}
              <button
                className="absolute top-2 right-2 w-11 h-11 rounded-full bg-black/20 hover:bg-black/30 active:bg-black/40 flex items-center justify-center transition-colors duration-200 z-20 touch-manipulation"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDismiss(notification.id);
                }}
                disabled={isSuccess}
                style={{
                  minWidth: '44px',
                  minHeight: '44px',
                  WebkitTapHighlightColor: 'transparent'
                }}
                aria-label="Close notification"
              >
                <X className="w-5 h-5 text-white/80" />
              </button>

              <div className={`flex items-start gap-3 pr-12 ${isSuccess ? 'opacity-20' : ''}`}>
                {/* Icon */}
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  {getNotificationIcon(notification.type)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="text-white font-semibold text-sm">{notification.title}</h4>
                    {notification.type === 'friend_at_venue' && (
                      <Badge 
                        variant="secondary" 
                        className="bg-white/20 text-white border-0 text-xs px-2 py-0"
                      >
                        LIVE
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-white/80 text-sm mb-3 leading-relaxed">
                    {notification.message}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-white/60 text-xs">{notification.timestamp}</span>
                    
                    {notification.actionLabel && notification.friendId && notification.venueId && (
                      <motion.div
                        initial={{ scale: 0.9 }}
                        animate={{ scale: 1 }}
                        whileHover={{ scale: isDismissing ? 1 : 1.05 }}
                        whileTap={{ scale: isDismissing ? 1 : 0.95 }}
                      >
                        <button
                          className="bg-white/20 hover:bg-white/30 active:bg-white/40 text-white border-0 text-xs px-4 py-2.5 rounded-lg font-medium disabled:opacity-50 transition-colors duration-200 touch-manipulation min-h-11"
                          onClick={() => handleJoinFriend(notification.friendId!, notification.venueId!, notification.id)}
                          disabled={isDismissing}
                          style={{
                            minHeight: '44px',
                            WebkitTapHighlightColor: 'transparent'
                          }}
                        >
                          <MapPin className="w-3 h-3 mr-1 inline" />
                          {notification.actionLabel}
                        </button>
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>

              {/* Pulse animation for live notifications */}
              {notification.type === 'friend_at_venue' && !isDismissing && (
                <motion.div
                  className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-400/20 to-cyan-400/20 pointer-events-none"
                  animate={{
                    opacity: [0.3, 0.6, 0.3],
                    scale: [1, 1.02, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              )}

              {/* Success pulse */}
              {isSuccess && (
                <motion.div
                  className="absolute inset-0 rounded-2xl bg-green-400/30 pointer-events-none"
                  animate={{
                    opacity: [0.5, 0.8, 0.5],
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 1,
                    repeat: 2,
                    ease: "easeInOut"
                  }}
                />
              )}
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
}