// Mobile detection and device-specific utilities
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isSafari: boolean;
  isChrome: boolean;
  isInAppBrowser: boolean;
  isInstagramBrowser: boolean;
  isTikTokBrowser: boolean;
  isTwitterBrowser: boolean;
  canInstallPWA: boolean;
  supportsWebShare: boolean;
  supportsClipboard: boolean;
  supportsVibration: boolean;
  orientation: 'portrait' | 'landscape';
}

class MobileDetectionService {
  private deviceInfo: DeviceInfo;

  constructor() {
    this.deviceInfo = this.detectDevice();
    
    // Listen for orientation changes
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.deviceInfo.orientation = this.getOrientation();
      }, 100);
    });
  }

  private detectDevice(): DeviceInfo {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform?.toLowerCase() || '';
    
    // Basic device detection
    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    const isTablet = /ipad|tablet/i.test(userAgent) || 
                    (isMobile && window.innerWidth > 768);
    const isDesktop = !isMobile && !isTablet;
    
    // OS detection
    const isIOS = /iphone|ipad|ipod/i.test(userAgent) || 
                 (platform.includes('mac') && 'ontouchend' in document);
    const isAndroid = /android/i.test(userAgent);
    
    // Browser detection
    const isSafari = /safari/i.test(userAgent) && !/chrome|chromium|edg/i.test(userAgent);
    const isChrome = /chrome|chromium/i.test(userAgent) && !/edg/i.test(userAgent);
    
    // In-app browser detection
    const isInAppBrowser = this.detectInAppBrowser(userAgent);
    const isInstagramBrowser = /instagram/i.test(userAgent);
    const isTikTokBrowser = /tiktok/i.test(userAgent) || /musical_ly/i.test(userAgent);
    const isTwitterBrowser = /twitter/i.test(userAgent);
    
    // Feature detection
    const canInstallPWA = 'serviceWorker' in navigator && 
                         'beforeinstallprompt' in window;
    const supportsWebShare = 'share' in navigator && 'canShare' in navigator;
    const supportsClipboard = 'clipboard' in navigator && 
                             'writeText' in navigator.clipboard;
    const supportsVibration = 'vibrate' in navigator;
    
    return {
      isMobile,
      isTablet,
      isDesktop,
      isIOS,
      isAndroid,
      isSafari,
      isChrome,
      isInAppBrowser,
      isInstagramBrowser,
      isTikTokBrowser,
      isTwitterBrowser,
      canInstallPWA,
      supportsWebShare,
      supportsClipboard,
      supportsVibration,
      orientation: this.getOrientation()
    };
  }

  private detectInAppBrowser(userAgent: string): boolean {
    const inAppBrowserPatterns = [
      /fbav|fbios|fban/i, // Facebook
      /instagram/i,       // Instagram
      /twitter/i,         // Twitter
      /tiktok|musical_ly/i, // TikTok
      /snapchat/i,        // Snapchat
      /linkedin/i,        // LinkedIn
      /whatsapp/i,        // WhatsApp
      /telegram/i,        // Telegram
      /wechat|micromessenger/i, // WeChat
      /line/i,            // Line
      /pinterest/i,       // Pinterest
      /reddit/i,          // Reddit
      /discord/i          // Discord
    ];
    
    return inAppBrowserPatterns.some(pattern => pattern.test(userAgent));
  }

  private getOrientation(): 'portrait' | 'landscape' {
    if ('orientation' in screen) {
      return Math.abs((screen.orientation as any).angle) === 90 ? 'landscape' : 'portrait';
    }
    return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
  }

  // Public methods
  getDeviceInfo(): DeviceInfo {
    return { ...this.deviceInfo };
  }

  isMobile(): boolean {
    return this.deviceInfo.isMobile;
  }

  isTablet(): boolean {
    return this.deviceInfo.isTablet;
  }

  isDesktop(): boolean {
    return this.deviceInfo.isDesktop;
  }

  isIOS(): boolean {
    return this.deviceInfo.isIOS;
  }

  isAndroid(): boolean {
    return this.deviceInfo.isAndroid;
  }

  isInAppBrowser(): boolean {
    return this.deviceInfo.isInAppBrowser;
  }

  canUseNativeShare(): boolean {
    return this.deviceInfo.supportsWebShare && !this.deviceInfo.isInAppBrowser;
  }

  canUseClipboard(): boolean {
    return this.deviceInfo.supportsClipboard;
  }

  // App-specific detection
  isInstagramApp(): boolean {
    return this.deviceInfo.isInstagramBrowser;
  }

  isTikTokApp(): boolean {
    return this.deviceInfo.isTikTokBrowser;
  }

  isTwitterApp(): boolean {
    return this.deviceInfo.isTwitterBrowser;
  }

  // Feature detection for sharing
  getOptimalSharingStrategy(platform: string): 'native' | 'deeplink' | 'fallback' {
    if (this.canUseNativeShare() && platform === 'native') {
      return 'native';
    }
    
    if (this.isMobile() && !this.isInAppBrowser()) {
      return 'deeplink';
    }
    
    return 'fallback';
  }

  // Deep link generation
  generateDeepLink(platform: string, content: any): string | null {
    const { text, url, hashtags } = content;
    
    switch (platform) {
      case 'instagram':
        if (this.isIOS()) {
          return 'instagram://app';
        } else if (this.isAndroid()) {
          return 'intent://instagram.com/#Intent;package=com.instagram.android;scheme=https;end';
        }
        return null;
      
      case 'tiktok':
        if (this.isIOS()) {
          return 'tiktok://';
        } else if (this.isAndroid()) {
          return 'intent://www.tiktok.com/#Intent;package=com.zhiliaoapp.musically;scheme=https;end';
        }
        return null;
      
      case 'twitter':
        const twitterText = encodeURIComponent(text);
        const twitterUrl = url ? encodeURIComponent(url) : '';
        const twitterHashtags = hashtags ? hashtags.join(',') : '';
        
        if (this.isIOS()) {
          return `twitterrific:///post?message=${twitterText}&url=${twitterUrl}`;
        } else if (this.isAndroid()) {
          return `intent://twitter.com/intent/tweet?text=${twitterText}&url=${twitterUrl}&hashtags=${twitterHashtags}#Intent;package=com.twitter.android;scheme=https;end`;
        }
        return `https://twitter.com/intent/tweet?text=${twitterText}&url=${twitterUrl}&hashtags=${twitterHashtags}`;
      
      case 'whatsapp':
        const whatsappText = encodeURIComponent(`${text} ${url || ''}`);
        return `whatsapp://send?text=${whatsappText}`;
      
      case 'telegram':
        const telegramText = encodeURIComponent(text);
        const telegramUrl = url ? encodeURIComponent(url) : '';
        return `tg://msg_url?url=${telegramUrl}&text=${telegramText}`;
      
      default:
        return null;
    }
  }

  // Haptic feedback
  vibrate(pattern?: number | number[]): boolean {
    if (!this.deviceInfo.supportsVibration) {
      return false;
    }
    
    try {
      const vibrationPattern = pattern || [100];
      navigator.vibrate(vibrationPattern);
      return true;
    } catch (error) {
      console.warn('Vibration failed:', error);
      return false;
    }
  }

  // Screen utilities
  enterFullscreen(element?: Element): Promise<void> {
    const target = element || document.documentElement;
    
    if (target.requestFullscreen) {
      return target.requestFullscreen();
    } else if ((target as any).webkitRequestFullscreen) {
      return (target as any).webkitRequestFullscreen();
    } else if ((target as any).mozRequestFullScreen) {
      return (target as any).mozRequestFullScreen();
    } else if ((target as any).msRequestFullscreen) {
      return (target as any).msRequestFullscreen();
    }
    
    return Promise.reject(new Error('Fullscreen not supported'));
  }

  exitFullscreen(): Promise<void> {
    if (document.exitFullscreen) {
      return document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      return (document as any).webkitExitFullscreen();
    } else if ((document as any).mozCancelFullScreen) {
      return (document as any).mozCancelFullScreen();
    } else if ((document as any).msExitFullscreen) {
      return (document as any).msExitFullscreen();
    }
    
    return Promise.reject(new Error('Exit fullscreen not supported'));
  }

  // Network detection
  getNetworkInfo(): { type: string; effectiveType: string; downlink: number } | null {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;
    
    if (connection) {
      return {
        type: connection.type || 'unknown',
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0
      };
    }
    
    return null;
  }

  // Memory info
  getMemoryInfo(): { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } | null {
    const memory = (performance as any).memory;
    
    if (memory) {
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }
    
    return null;
  }

  // Device capabilities check
  hasCamera(): boolean {
    return 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
  }

  hasGeolocation(): boolean {
    return 'geolocation' in navigator;
  }

  hasNotificationSupport(): boolean {
    return 'Notification' in window;
  }

  hasPushNotificationSupport(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window;
  }

  // App install prompt
  async promptInstall(): Promise<boolean> {
    const deferredPrompt = (window as any).deferredPrompt;
    
    if (!deferredPrompt) {
      return false;
    }
    
    try {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      (window as any).deferredPrompt = null;
      
      return outcome === 'accepted';
    } catch (error) {
      console.error('Install prompt failed:', error);
      return false;
    }
  }
}

export const mobileDetectionService = new MobileDetectionService();
export default mobileDetectionService;