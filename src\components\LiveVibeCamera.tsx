import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  X, 
  Video, 
  StopCircle, 
  Camera, 
  MapPin, 
  Hash, 
  Send,
  RotateCcw,
  Sparkles,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings,
  RefreshCw,
  Shield
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Badge } from './ui/badge';
import { Card } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';

interface LiveVibeCameraProps {
  isOpen: boolean;
  onClose: () => void;
  currentLocation?: {
    name: string;
    type: 'venue' | 'area';
    address?: string;
  };
}

type RecordingState = 'idle' | 'recording' | 'recorded' | 'uploading' | 'uploaded';
type CameraState = 'requesting' | 'granted' | 'denied' | 'unavailable' | 'error';

interface VibeVideo {
  id: string;
  blob: Blob;
  duration: number;
  timestamp: string;
}

const nearbyVenues = [
  { id: 'v1', name: 'Rooftop Lounge', distance: '50m' },
  { id: 'v2', name: 'Neon Pulse', distance: '120m' },
  { id: 'v3', name: 'Garden Drift', distance: '200m' },
  { id: 'v4', name: 'Downtown Garage', distance: '80m' }
];

const trendingTags = ['#VibesCheck', '#NightOut', '#WeekendEnergy', '#BytspotLive', '#CityLife'];

export function LiveVibeCamera({ isOpen, onClose, currentLocation }: LiveVibeCameraProps) {
  const [recordingState, setRecordingState] = useState<RecordingState>('idle');
  const [cameraState, setCameraState] = useState<CameraState>('requesting');
  const [recordedVideo, setRecordedVideo] = useState<VibeVideo | null>(null);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [caption, setCaption] = useState('');
  const [selectedVenue, setSelectedVenue] = useState(currentLocation?.name || '');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState('');
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize camera when modal opens
  useEffect(() => {
    if (isOpen) {
      initializeCamera();
    } else {
      cleanupCamera();
    }
    
    return cleanupCamera;
  }, [isOpen]);

  // Recording timer
  useEffect(() => {
    if (recordingState === 'recording') {
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    } else {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    }

    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
  }, [recordingState]);

  const initializeCamera = async () => {
    try {
      setCameraState('requesting');
      setErrorMessage('');
      
      // Check if mediaDevices is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setCameraState('unavailable');
        setErrorMessage('Camera not supported on this device');
        return;
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: 'user',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: true
      });
      
      streamRef.current = stream;
      setCameraState('granted');
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error: any) {
      console.error('Error accessing camera:', error);
      
      if (error.name === 'NotAllowedError') {
        setCameraState('denied');
        setErrorMessage('Camera access denied. Please enable camera permissions to capture live vibes.');
      } else if (error.name === 'NotFoundError') {
        setCameraState('unavailable');
        setErrorMessage('No camera found on this device.');
      } else if (error.name === 'NotReadableError') {
        setCameraState('error');
        setErrorMessage('Camera is being used by another application.');
      } else {
        setCameraState('error');
        setErrorMessage('Unable to access camera. Please try again.');
      }
    }
  };

  const cleanupCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
    
    setRecordingState('idle');
    setRecordingDuration(0);
    setRecordedVideo(null);
    setCaption('');
    setSelectedTags([]);
    setCameraState('requesting');
    setErrorMessage('');
  };

  const startRecording = () => {
    if (!streamRef.current || cameraState !== 'granted') return;

    const chunks: BlobPart[] = [];
    const mediaRecorder = new MediaRecorder(streamRef.current);
    
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.push(event.data);
      }
    };
    
    mediaRecorder.onstop = () => {
      const blob = new Blob(chunks, { type: 'video/webm' });
      const video: VibeVideo = {
        id: Date.now().toString(),
        blob,
        duration: recordingDuration,
        timestamp: new Date().toISOString()
      };
      
      setRecordedVideo(video);
      setRecordingState('recorded');
    };
    
    mediaRecorderRef.current = mediaRecorder;
    mediaRecorder.start();
    setRecordingState('recording');
    setRecordingDuration(0);
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && recordingState === 'recording') {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current = null;
    }
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const handleUpload = async () => {
    if (!recordedVideo) return;
    
    setRecordingState('uploading');
    
    // Simulate upload process
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setRecordingState('uploaded');
    
    // Show success briefly then close
    setTimeout(() => {
      onClose();
    }, 1500);
  };

  const handleRetake = () => {
    setRecordedVideo(null);
    setRecordingState('idle');
    setRecordingDuration(0);
    setCaption('');
  };

  const handleRetryCamera = () => {
    initializeCamera();
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderCameraError = () => (
    <div className="flex flex-col items-center justify-center h-full p-6 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="w-20 h-20 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mb-6"
      >
        {cameraState === 'denied' ? (
          <Shield className="w-10 h-10 text-white" />
        ) : cameraState === 'unavailable' ? (
          <Camera className="w-10 h-10 text-white" />
        ) : (
          <AlertCircle className="w-10 h-10 text-white" />
        )}
      </motion.div>
      
      <h3 className="text-xl font-semibold text-white mb-2">
        {cameraState === 'denied' ? 'Camera Access Needed' :
         cameraState === 'unavailable' ? 'Camera Unavailable' :
         'Camera Error'}
      </h3>
      
      <p className="text-white/80 mb-6 max-w-sm">
        {errorMessage}
      </p>

      {cameraState === 'denied' && (
        <Alert className="mb-6 bg-white/10 border-white/20 text-white max-w-md">
          <Settings className="h-4 w-4" />
          <AlertDescription className="text-white/90">
            To enable camera access:
            <br />1. Click the camera icon in your browser's address bar
            <br />2. Select "Allow" for camera permissions
            <br />3. Refresh this page
          </AlertDescription>
        </Alert>
      )}

      <div className="flex gap-3">
        {(cameraState === 'denied' || cameraState === 'error') && (
          <Button
            onClick={handleRetryCamera}
            variant="outline"
            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        )}
        
        <Button
          onClick={onClose}
          className="bg-white text-gray-800 hover:bg-white/90"
        >
          Close
        </Button>
      </div>
    </div>
  );

  const renderCameraPermissionRequest = () => (
    <div className="flex flex-col items-center justify-center h-full p-6 text-center">
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-6"
      >
        <Camera className="w-8 h-8 text-white" />
      </motion.div>
      
      <h3 className="text-xl font-semibold text-white mb-2">Requesting Camera Access</h3>
      <p className="text-white/80 mb-4">
        Please allow camera access to capture live vibes
      </p>
      
      <div className="text-sm text-white/60">
        Look for the permission prompt in your browser
      </div>
    </div>
  );

  const renderCameraView = () => (
    <div className="relative w-full h-full">
      {/* Video Preview */}
      <video
        ref={videoRef}
        autoPlay
        muted
        playsInline
        className="w-full h-full object-cover"
      />
      
      {/* Recording Overlay */}
      {recordingState === 'recording' && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Recording Indicator */}
          <div className="absolute top-6 left-6">
            <div className="flex items-center gap-2 bg-red-500 text-white px-3 py-2 rounded-full">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse" />
              <span className="font-medium">REC</span>
              <span className="font-mono">{formatDuration(recordingDuration)}</span>
            </div>
          </div>
          
          {/* Recording Frame */}
          <div className="absolute inset-4 border-2 border-red-500 rounded-3xl animate-pulse" />
        </div>
      )}
      
      {/* Location Overlay */}
      {selectedVenue && (
        <div className="absolute top-6 right-6">
          <div className="backdrop-blur-sm bg-black/20 text-white px-3 py-2 rounded-full flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            <span className="text-sm font-medium">{selectedVenue}</span>
          </div>
        </div>
      )}
      
      {/* Controls */}
      <div className="absolute bottom-0 left-0 right-0 p-6">
        <div className="flex items-center justify-center">
          {recordingState === 'idle' ? (
            <motion.button
              onTap={startRecording}
              className="w-20 h-20 bg-red-500 rounded-full border-4 border-white shadow-2xl flex items-center justify-center"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Video className="w-8 h-8 text-white" />
            </motion.button>
          ) : (
            <motion.button
              onTap={stopRecording}
              className="w-20 h-20 bg-red-600 rounded-full border-4 border-white shadow-2xl flex items-center justify-center"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <StopCircle className="w-10 h-10 text-white" />
            </motion.button>
          )}
        </div>
      </div>
    </div>
  );

  const renderVideoPreview = () => (
    <div className="relative w-full h-full">
      {/* Video Playback */}
      <video
        src={recordedVideo ? URL.createObjectURL(recordedVideo.blob) : undefined}
        autoPlay
        loop
        muted
        className="w-full h-full object-cover"
      />
      
      {/* Duration Badge */}
      <div className="absolute top-6 left-6">
        <Badge className="bg-black/50 text-white border-0">
          <Clock className="w-3 h-3 mr-1" />
          {formatDuration(recordedVideo?.duration || 0)}
        </Badge>
      </div>
      
      {/* Controls */}
      <div className="absolute bottom-6 left-6 right-6 space-y-4">
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleRetake}
            className="flex-1 bg-black/20 border-white/30 text-white hover:bg-black/30"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Retake
          </Button>
          <Button
            onClick={() => setRecordingState('recorded')}
            className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Use This
          </Button>
        </div>
      </div>
    </div>
  );

  const renderEditingView = () => (
    <div className="p-6 space-y-6 max-h-full overflow-y-auto">
      {/* Video Thumbnail */}
      <div className="relative">
        <video
          src={recordedVideo ? URL.createObjectURL(recordedVideo.blob) : undefined}
          className="w-full h-48 object-cover rounded-2xl"
          poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300'%3E%3Crect width='100%25' height='100%25' fill='%23f3f4f6'/%3E%3C/svg%3E"
        />
        <div className="absolute bottom-3 right-3">
          <Badge className="bg-black/50 text-white border-0">
            <Clock className="w-3 h-3 mr-1" />
            {formatDuration(recordedVideo?.duration || 0)}
          </Badge>
        </div>
      </div>

      {/* Caption Input */}
      <div className="space-y-2">
        <label className="font-medium text-gray-800">Add a caption</label>
        <Textarea
          placeholder="What's the vibe at this spot? Tell others what to expect..."
          value={caption}
          onChange={(e) => setCaption(e.target.value)}
          className="resize-none h-20"
        />
        <p className="text-sm text-gray-500">{caption.length}/280</p>
      </div>

      {/* Venue Selection */}
      <div className="space-y-3">
        <label className="font-medium text-gray-800">Tag a location</label>
        <div className="space-y-2">
          {nearbyVenues.map((venue) => (
            <button
              key={venue.id}
              onClick={() => setSelectedVenue(venue.name)}
              className={`w-full p-3 rounded-xl border-2 transition-all text-left ${
                selectedVenue === venue.name
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 bg-white hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span className="font-medium text-gray-800">{venue.name}</span>
                </div>
                <span className="text-sm text-gray-500">{venue.distance}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Trending Tags */}
      <div className="space-y-3">
        <label className="font-medium text-gray-800">Add tags</label>
        <div className="flex flex-wrap gap-2">
          {trendingTags.map((tag) => (
            <motion.button
              key={tag}
              onClick={() => handleTagToggle(tag)}
              className={`px-3 py-2 rounded-full text-sm font-medium transition-all ${
                selectedTags.includes(tag)
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Hash className="w-3 h-3 inline mr-1" />
              {tag.slice(1)}
            </motion.button>
          ))}
        </div>
        <Input
          placeholder="Add custom tag..."
          className="text-sm"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              const customTag = `#${e.currentTarget.value}`;
              if (customTag.length > 1 && !selectedTags.includes(customTag)) {
                setSelectedTags(prev => [...prev, customTag]);
                e.currentTarget.value = '';
              }
            }
          }}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={handleRetake}
          className="flex-1"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          Retake
        </Button>
        <Button
          onClick={handleUpload}
          disabled={!selectedVenue || recordingState === 'uploading'}
          className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
        >
          {recordingState === 'uploading' ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles className="w-4 h-4 mr-2" />
            </motion.div>
          ) : (
            <Send className="w-4 h-4 mr-2" />
          )}
          {recordingState === 'uploading' ? 'Sharing...' : 'Share Vibe'}
        </Button>
      </div>
    </div>
  );

  const renderUploadedView = () => (
    <div className="flex flex-col items-center justify-center h-full p-6 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mb-6"
      >
        <CheckCircle className="w-10 h-10 text-white" />
      </motion.div>
      
      <h3 className="text-xl font-semibold text-gray-800 mb-2">Vibe Shared! 🎉</h3>
      <p className="text-gray-600 mb-4">
        Your live vibe video has been shared with the Bytspot community
      </p>
      
      <div className="space-y-2 text-sm text-gray-500">
        <p>📍 {selectedVenue}</p>
        <p>🏷️ {selectedTags.join(' ')}</p>
        {caption && <p>💭 "{caption}"</p>}
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black"
      >
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 z-10 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white hover:bg-white/20 w-10 h-10 p-0 rounded-full"
              >
                <X className="w-5 h-5" />
              </Button>
              <div>
                <h2 className="font-semibold text-white">Live Vibe Capture</h2>
                <p className="text-sm text-white/70">Share what's happening right now</p>
              </div>
            </div>
            
            {recordingState === 'recording' && (
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="flex items-center gap-2 text-red-400"
              >
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                <span className="text-sm font-medium">LIVE</span>
              </motion.div>
            )}
          </div>
        </div>

        {/* Content */}
        <motion.div
          key={`${cameraState}-${recordingState}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="h-full"
        >
          {cameraState === 'requesting' ? (
            renderCameraPermissionRequest()
          ) : cameraState === 'denied' || cameraState === 'unavailable' || cameraState === 'error' ? (
            renderCameraError()
          ) : recordingState === 'idle' || recordingState === 'recording' ? (
            renderCameraView()
          ) : recordingState === 'recorded' ? (
            renderVideoPreview()
          ) : recordingState === 'uploading' || recordingState === 'uploaded' ? (
            <div className="relative h-full">
              {/* Background Video */}
              <video
                src={recordedVideo ? URL.createObjectURL(recordedVideo.blob) : undefined}
                autoPlay
                loop
                muted
                className="w-full h-full object-cover opacity-50"
              />
              
              {/* Overlay Content */}
              <div className="absolute inset-0 bg-black/50">
                {recordingState === 'uploading' ? (
                  <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-6"
                    >
                      <Sparkles className="w-8 h-8 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-semibold text-white mb-2">Sharing the vibe...</h3>
                    <p className="text-white/70">Your video is being uploaded to the community</p>
                  </div>
                ) : (
                  renderUploadedView()
                )}
              </div>
            </div>
          ) : null}
        </motion.div>

        {/* Bottom Sheet for Editing */}
        {recordingState === 'recorded' && cameraState === 'granted' && (
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', damping: 30, stiffness: 300 }}
            className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-[60%] overflow-hidden"
          >
            {renderEditingView()}
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}