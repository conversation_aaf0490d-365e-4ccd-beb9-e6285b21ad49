interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  userId?: string;
  timestamp?: number;
}

interface UserProperties {
  userId: string;
  email?: string;
  name?: string;
  role?: string;
  signupDate?: string;
  [key: string]: any;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

class AnalyticsService {
  private static instance: AnalyticsService;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;
  private eventQueue: AnalyticsEvent[] = [];
  private userId: string | null = null;

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    // Prevent multiple concurrent initializations
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._performInitialization();
    return this.initializationPromise;
  }

  private async _performInitialization(): Promise<void> {
    try {
      // Initialize third-party analytics
      this.initializeGoogleAnalytics();
      this.initializeMixpanel();
      this.initializeAmplitude();
      
      // Set up performance monitoring
      this.setupPerformanceMonitoring();
      
      // Process queued events
      this.processEventQueue();
      
      this.isInitialized = true;
      console.log('Analytics service initialized');
    } catch (error) {
      console.error('Failed to initialize analytics service:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  private initializeGoogleAnalytics(): void {
    const gaId = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_GA_MEASUREMENT_ID) || null;
    if (!gaId) return;

    // Load Google Analytics
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    document.head.appendChild(script);

    // Initialize gtag
    (window as any).dataLayer = (window as any).dataLayer || [];
    function gtag(...args: any[]) {
      (window as any).dataLayer.push(args);
    }
    (window as any).gtag = gtag;

    gtag('js', new Date());
    gtag('config', gaId, {
      send_page_view: false, // We'll handle page views manually
    });
  }

  private initializeMixpanel(): void {
    const mixpanelToken = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_MIXPANEL_TOKEN) || null;
    if (!mixpanelToken) return;

    // Load Mixpanel
    (function(f: any, b: any) {
      if (!b.__SV) {
        const e = b.createElement('script');
        e.type = 'text/javascript';
        e.async = true;
        e.src = 'https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js';
        const g = b.getElementsByTagName('script')[0];
        g.parentNode.insertBefore(e, g);
        b.__SV = 1.2;
      }
    })(document, document);

    // Initialize when loaded
    const checkMixpanel = () => {
      if ((window as any).mixpanel) {
        (window as any).mixpanel.init(mixpanelToken, {
          debug: (typeof import.meta !== 'undefined' && import.meta.env?.DEV) || false,
          track_pageview: false,
        });
      } else {
        setTimeout(checkMixpanel, 100);
      }
    };
    checkMixpanel();
  }

  private initializeAmplitude(): void {
    const amplitudeKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_AMPLITUDE_API_KEY) || null;
    if (!amplitudeKey) return;

    // Load Amplitude
    (function(e: any, t: any) {
      const n = e.amplitude || { _q: [], _iq: {} };
      const r = t.createElement('script');
      r.type = 'text/javascript';
      r.integrity = 'sha384-+EO59vL/X7v6VE2s6/F4HxfHlK0nDUVWKVg8K9oUlvffAeeaShVBmbORTC2D3UF+';
      r.crossOrigin = 'anonymous';
      r.async = true;
      r.src = 'https://cdn.amplitude.com/libs/amplitude-8.21.9-min.gz.js';
      r.onload = function() {
        if (!e.amplitude.runQueuedFunctions) {
          console.log('[Amplitude] Error: could not load SDK');
        }
      };
      const s = t.getElementsByTagName('script')[0];
      s.parentNode.insertBefore(r, s);
      
      function i(e: any, t: any) {
        e.prototype[t] = function() {
          this._q.push([t].concat(Array.prototype.slice.call(arguments, 0)));
          return this;
        };
      }
      
      const o = function() { return this._q = [], this; };
      const a = ['add', 'append', 'clearAll', 'prepend', 'set', 'setOnce', 'unset', 'preInsert', 'postInsert', 'remove'];
      for (let c = 0; c < a.length; c++) {
        i(o, a[c]);
      }
      n.Identify = o;
      
      const u = function() {
        this._q = [];
        return this;
      };
      const l = ['setProductId', 'setQuantity', 'setPrice', 'setRevenueType', 'setEventProperties'];
      for (let p = 0; p < l.length; p++) {
        i(u, l[p]);
      }
      n.Revenue = u;
      
      const d = ['init', 'logEvent', 'logRevenue', 'setUserId', 'setUserProperties', 'setOptOut', 'setVersionName', 'setDomain', 'setDeviceId', 'enableTracking', 'setGlobalUserProperties', 'identify', 'clearUserProperties', 'setGroup', 'logRevenueV2', 'regenerateDeviceId', 'groupIdentify', 'onInit', 'logEventWithTimestamp', 'logEventWithGroups', 'setSessionId', 'resetSessionId'];
      
      function v(e: any) {
        function t(t: any) {
          e[t] = function() {
            e._q.push([t].concat(Array.prototype.slice.call(arguments, 0)));
          };
        }
        for (let n = 0; n < d.length; n++) {
          t(d[n]);
        }
      }
      
      v(n);
      n.getInstance = function(e: any) {
        e = (!e || e.length === 0 ? '$default_instance' : e).toLowerCase();
        if (!Object.prototype.hasOwnProperty.call(n._iq, e)) {
          n._iq[e] = { _q: [] };
          v(n._iq[e]);
        }
        return n._iq[e];
      };
      
      e.amplitude = n;
    })(window, document);

    // Initialize Amplitude
    (window as any).amplitude.getInstance().init(amplitudeKey);
  }

  private setupPerformanceMonitoring(): void {
    // Monitor Core Web Vitals
    if ('web-vital' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(this.reportWebVital.bind(this));
        getFID(this.reportWebVital.bind(this));
        getFCP(this.reportWebVital.bind(this));
        getLCP(this.reportWebVital.bind(this));
        getTTFB(this.reportWebVital.bind(this));
      });
    }

    // Monitor custom performance metrics
    this.monitorCustomMetrics();
  }

  private reportWebVital(metric: any): void {
    this.trackPerformanceMetric({
      name: metric.name,
      value: metric.value,
      unit: 'ms',
      timestamp: Date.now(),
      metadata: {
        id: metric.id,
        rating: metric.rating,
      },
    });
  }

  private monitorCustomMetrics(): void {
    // Monitor page load time
    window.addEventListener('load', () => {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      this.trackPerformanceMetric({
        name: 'page_load_time',
        value: loadTime,
        unit: 'ms',
        timestamp: Date.now(),
      });
    });

    // Monitor API response times
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = Date.now();
      try {
        const response = await originalFetch(...args);
        const endTime = Date.now();
        
        this.trackPerformanceMetric({
          name: 'api_response_time',
          value: endTime - startTime,
          unit: 'ms',
          timestamp: endTime,
          metadata: {
            url: args[0] as string,
            status: response.status,
            ok: response.ok,
          },
        });
        
        return response;
      } catch (error) {
        const endTime = Date.now();
        
        this.trackPerformanceMetric({
          name: 'api_response_time',
          value: endTime - startTime,
          unit: 'ms',
          timestamp: endTime,
          metadata: {
            url: args[0] as string,
            error: true,
          },
        });
        
        throw error;
      }
    };
  }

  track(eventName: string, properties?: Record<string, any>): void {
    const event: AnalyticsEvent = {
      name: eventName,
      properties: {
        ...properties,
        timestamp: Date.now(),
        url: window.location.href,
        referrer: document.referrer,
        user_agent: navigator.userAgent,
      },
      userId: this.userId || undefined,
      timestamp: Date.now(),
    };

    if (!this.isInitialized) {
      this.eventQueue.push(event);
      return;
    }

    this.sendEvent(event);
  }

  identify(userId: string, properties?: UserProperties): void {
    this.userId = userId;

    if (!this.isInitialized) {
      this.eventQueue.push({
        name: 'identify',
        properties,
        userId,
      });
      return;
    }

    // Send to all analytics platforms
    if ((window as any).gtag) {
      const gaId = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_GA_MEASUREMENT_ID) || null;
      if (gaId) {
        (window as any).gtag('config', gaId, {
          user_id: userId,
        });
      }
    }

    if ((window as any).mixpanel) {
      (window as any).mixpanel.identify(userId);
      if (properties) {
        (window as any).mixpanel.people.set(properties);
      }
    }

    if ((window as any).amplitude) {
      (window as any).amplitude.getInstance().setUserId(userId);
      if (properties) {
        (window as any).amplitude.getInstance().setUserProperties(properties);
      }
    }
  }

  page(pageName: string, properties?: Record<string, any>): void {
    this.track('page_view', {
      page_name: pageName,
      ...properties,
    });

    // Send to Google Analytics
    if ((window as any).gtag) {
      (window as any).gtag('event', 'page_view', {
        page_title: pageName,
        page_location: window.location.href,
        ...properties,
      });
    }
  }

  trackPerformanceMetric(metric: PerformanceMetric): void {
    this.track('performance_metric', {
      metric_name: metric.name,
      metric_value: metric.value,
      metric_unit: metric.unit,
      ...metric.metadata,
    });
  }

  reset(): void {
    this.userId = null;

    if ((window as any).mixpanel) {
      (window as any).mixpanel.reset();
    }

    if ((window as any).amplitude) {
      (window as any).amplitude.getInstance().setUserId(null);
      (window as any).amplitude.getInstance().clearUserProperties();
    }
  }

  private sendEvent(event: AnalyticsEvent): void {
    // Send to Google Analytics
    if ((window as any).gtag) {
      (window as any).gtag('event', event.name, event.properties);
    }

    // Send to Mixpanel
    if ((window as any).mixpanel) {
      (window as any).mixpanel.track(event.name, event.properties);
    }

    // Send to Amplitude
    if ((window as any).amplitude) {
      (window as any).amplitude.getInstance().logEvent(event.name, event.properties);
    }

    // Send to custom endpoint
    this.sendToCustomEndpoint(event);
  }

  private async sendToCustomEndpoint(event: AnalyticsEvent): Promise<void> {
    const endpoint = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_ANALYTICS_ENDPOINT) || null;
    if (!endpoint) return;

    try {
      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
      });
    } catch (error) {
      console.warn('Failed to send analytics event to custom endpoint:', error);
    }
  }

  private processEventQueue(): void {
    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift();
      if (event) {
        if (event.name === 'identify') {
          this.identify(event.userId!, event.properties as UserProperties);
        } else {
          this.sendEvent(event);
        }
      }
    }
  }
}

export const analyticsService = AnalyticsService.getInstance();
export default analyticsService;