import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Camera, 
  X, 
  Check, 
  AlertCircle, 
  Receipt, 
  Users, 
  Calendar, 
  MapPin,
  DollarSign,
  Clock,
  User,
  Phone,
  Mail,
  FileText,
  Download,
  Share2
} from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';

interface ScannedReservation {
  id: string;
  venue: {
    id: string;
    name: string;
    address: string;
  };
  customer: {
    name: string;
    phone: string;
    email: string;
  };
  booking: {
    date: string;
    time: string;
    partySize: number;
    specialRequests?: string;
  };
  payment: {
    deposit: number;
    currency: string;
    status: string;
  };
  metadata: {
    platform: string;
    bookingType: string;
    confirmationTime: string;
    qrVersion: string;
  };
}

interface QRScannerProps {
  onClose: () => void;
  onReservationProcessed?: (reservation: ScannedReservation, receipt: any) => void;
}

export function QRScanner({ onClose, onReservationProcessed }: QRScannerProps) {
  const [isScanning, setIsScanning] = useState(true);
  const [scannedData, setScannedData] = useState<ScannedReservation | null>(null);
  const [scanError, setScanError] = useState<string>('');
  const [generatedReceipt, setGeneratedReceipt] = useState<any>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Simulate QR code scanning for demo
  const simulateQRScan = () => {
    setTimeout(() => {
      const mockScannedReservation: ScannedReservation = {
        id: 'RES-' + Date.now().toString(36).toUpperCase(),
        venue: {
          id: 'venue-123',
          name: 'Rooftop Lounge',
          address: '123 Main Street, Downtown'
        },
        customer: {
          name: 'Alex Johnson',
          phone: '+****************',
          email: '<EMAIL>'
        },
        booking: {
          date: new Date().toISOString().split('T')[0],
          time: '7:30 PM',
          partySize: 4,
          specialRequests: 'Celebrating anniversary, window seat preferred'
        },
        payment: {
          deposit: 25.00,
          currency: 'USD',
          status: 'confirmed'
        },
        metadata: {
          platform: 'Bytspot',
          bookingType: 'venue_reservation',
          confirmationTime: new Date().toISOString(),
          qrVersion: '1.0'
        }
      };
      
      processScannedReservation(mockScannedReservation);
    }, 2000);
  };

  const processScannedReservation = (reservation: ScannedReservation) => {
    setScannedData(reservation);
    setIsScanning(false);
    
    // Generate receipt data for AI training
    const receipt = generateReceipt(reservation);
    setGeneratedReceipt(receipt);
    
    if (onReservationProcessed) {
      onReservationProcessed(reservation, receipt);
    }
  };

  const generateReceipt = (reservation: ScannedReservation) => {
    const currentTime = new Date();
    const receiptId = 'RCP-' + Date.now().toString(36).toUpperCase();
    
    return {
      receiptId,
      reservationId: reservation.id,
      timestamp: currentTime.toISOString(),
      venue: {
        name: reservation.venue.name,
        address: reservation.venue.address,
        id: reservation.venue.id
      },
      customer: reservation.customer,
      booking: reservation.booking,
      charges: [
        {
          item: 'Table Reservation Deposit',
          quantity: 1,
          unitPrice: reservation.payment.deposit,
          total: reservation.payment.deposit
        }
      ],
      subtotal: reservation.payment.deposit,
      tax: reservation.payment.deposit * 0.0875, // 8.75% tax
      total: reservation.payment.deposit * 1.0875,
      paymentMethod: 'Credit Card',
      paymentStatus: 'Processed',
      notes: 'Deposit will be applied to final bill upon dining completion',
      aiTrainingData: {
        scanTime: currentTime.toISOString(),
        processingDuration: '1.2s',
        confidence: 0.98,
        dataPoints: [
          'customer_preferences',
          'booking_patterns',
          'payment_behavior',
          'venue_popularity'
        ]
      }
    };
  };

  const downloadReceipt = () => {
    if (!generatedReceipt) return;
    
    const receiptText = JSON.stringify(generatedReceipt, null, 2);
    const blob = new Blob([receiptText], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.download = `bytspot-receipt-${generatedReceipt.receiptId}.json`;
    link.href = url;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  const shareReceipt = async () => {
    if (!generatedReceipt || !scannedData) return;
    
    const receiptSummary = `
Bytspot Receipt
${scannedData.venue.name}
${scannedData.booking.date} at ${scannedData.booking.time}
Party of ${scannedData.booking.partySize}
Total: $${generatedReceipt.total.toFixed(2)}
Receipt ID: ${generatedReceipt.receiptId}
    `.trim();
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Bytspot Receipt',
          text: receiptSummary
        });
      } catch (error) {
        console.error('Failed to share receipt:', error);
      }
    }
  };

  useEffect(() => {
    if (isScanning) {
      simulateQRScan();
    }
  }, [isScanning]);

  const renderScanningView = () => (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="flex flex-col items-center justify-center h-full space-y-6"
    >
      {/* Camera Viewfinder */}
      <div className="relative w-80 h-80 rounded-3xl overflow-hidden border-4 border-white/30">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/50 to-blue-900/50" />
        
        {/* Scanning Animation */}
        <motion.div
          className="absolute inset-0 border-4 border-green-400"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.8, 1, 0.8]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Corner Brackets */}
        <div className="absolute top-4 left-4 w-8 h-8 border-l-4 border-t-4 border-green-400" />
        <div className="absolute top-4 right-4 w-8 h-8 border-r-4 border-t-4 border-green-400" />
        <div className="absolute bottom-4 left-4 w-8 h-8 border-l-4 border-b-4 border-green-400" />
        <div className="absolute bottom-4 right-4 w-8 h-8 border-r-4 border-b-4 border-green-400" />
        
        {/* Scanning Line */}
        <motion.div
          className="absolute left-0 right-0 h-1 bg-green-400 shadow-lg shadow-green-400/50"
          animate={{
            y: [20, 300, 20]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-white text-center">
            <Camera className="w-12 h-12 mx-auto mb-4" />
            <p className="font-medium">Scanning QR Code...</p>
            <p className="text-sm opacity-70 mt-1">Position QR code within frame</p>
          </div>
        </div>
      </div>

      <div className="text-center">
        <p className="text-white font-medium mb-2">Ready to Scan Reservation</p>
        <p className="text-white/70 text-sm">Point camera at customer's QR code</p>
      </div>
    </motion.div>
  );

  const renderResultView = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Success Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", bounce: 0.5 }}
          className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <Check className="w-8 h-8 text-white" />
        </motion.div>
        <h2 className="text-2xl font-bold text-white mb-2">Reservation Verified</h2>
        <p className="text-white/70">Guest details processed successfully</p>
      </div>

      {/* Customer Information */}
      <Card className="bg-white/10 border-white/20 p-6">
        <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
          <User className="w-5 h-5 text-blue-400" />
          Customer Details
        </h3>
        <div className="space-y-3 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-white/70">Name:</span>
            <span className="text-white font-medium">{scannedData?.customer.name}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-white/70">Phone:</span>
            <span className="text-white font-medium">{scannedData?.customer.phone}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-white/70">Email:</span>
            <span className="text-white font-medium text-xs">{scannedData?.customer.email}</span>
          </div>
        </div>
      </Card>

      {/* Booking Information */}
      <Card className="bg-white/10 border-white/20 p-6">
        <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
          <Calendar className="w-5 h-5 text-purple-400" />
          Booking Details
        </h3>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <Clock className="w-4 h-4 text-blue-400" />
            <div>
              <p className="text-white font-medium">{scannedData?.booking.date}</p>
              <p className="text-white/60 text-sm">{scannedData?.booking.time}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Users className="w-4 h-4 text-green-400" />
            <p className="text-white font-medium">{scannedData?.booking.partySize} Guests</p>
          </div>
          {scannedData?.booking.specialRequests && (
            <div className="flex items-start gap-3">
              <FileText className="w-4 h-4 text-yellow-400 mt-0.5" />
              <div>
                <p className="text-white/70 text-sm">Special Requests:</p>
                <p className="text-white text-sm">{scannedData.booking.specialRequests}</p>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Generated Receipt */}
      {generatedReceipt && (
        <Card className="bg-green-500/10 border-green-400/30 p-6">
          <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
            <Receipt className="w-5 h-5 text-green-400" />
            Generated Receipt
          </h3>
          
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-white/70">Receipt ID:</span>
              <span className="text-white font-mono">{generatedReceipt.receiptId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Reservation ID:</span>
              <span className="text-white font-mono">{generatedReceipt.reservationId}</span>
            </div>
            
            <Separator className="bg-white/20 my-3" />
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-white/70">Deposit:</span>
                <span className="text-white">${generatedReceipt.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Tax (8.75%):</span>
                <span className="text-white">${generatedReceipt.tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-semibold text-white border-t border-white/20 pt-2">
                <span>Total Processed:</span>
                <span>${generatedReceipt.total.toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3 mt-6">
            <Button
              variant="outline"
              size="sm"
              onClick={downloadReceipt}
              className="border-green-300 text-green-600 hover:bg-green-50"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button
              variant="outline"
              size="sm" 
              onClick={shareReceipt}
              className="border-blue-300 text-blue-600 hover:bg-blue-50"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </Card>
      )}

      {/* AI Training Data Indicator */}
      <Card className="bg-purple-500/10 border-purple-400/30 p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs">AI</span>
          </div>
          <div>
            <p className="text-white font-medium text-sm">Training Data Captured</p>
            <p className="text-white/60 text-xs">Improving AI recommendations</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <Badge className="bg-purple-100 text-purple-700 justify-center">Customer Patterns</Badge>
          <Badge className="bg-blue-100 text-blue-700 justify-center">Booking Preferences</Badge>
          <Badge className="bg-green-100 text-green-700 justify-center">Venue Analytics</Badge>
          <Badge className="bg-orange-100 text-orange-700 justify-center">Payment Behavior</Badge>
        </div>
      </Card>

      <Button
        className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3"
        onClick={onClose}
      >
        Complete Check-in
      </Button>
    </motion.div>
  );

  const downloadReceipt = () => {
    if (!generatedReceipt) return;
    
    const receiptText = JSON.stringify(generatedReceipt, null, 2);
    const blob = new Blob([receiptText], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.download = `bytspot-receipt-${generatedReceipt.receiptId}.json`;
    link.href = url;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  const shareReceipt = async () => {
    if (!generatedReceipt || !scannedData) return;
    
    const receiptSummary = `
Bytspot Receipt
${scannedData.venue.name}
${scannedData.booking.date} at ${scannedData.booking.time}
Party of ${scannedData.booking.partySize}
Total: $${generatedReceipt.total.toFixed(2)}
Receipt ID: ${generatedReceipt.receiptId}
    `.trim();
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Bytspot Receipt',
          text: receiptSummary
        });
      } catch (error) {
        console.error('Failed to share receipt:', error);
      }
    }
  };

  useEffect(() => {
    if (isScanning) {
      simulateQRScan();
    }
  }, []);

  return (
    <div className="h-full bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_white_1px,_transparent_0)] bg-[size:30px_30px]" />
      </div>

      {/* Header */}
      <div className="relative z-10 p-6 pb-0">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/10 p-2"
            onClick={onClose}
          >
            <X className="w-6 h-6" />
          </Button>
          <h1 className="text-xl font-bold text-white">QR Scanner</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 px-6 pb-6 h-full">
        <AnimatePresence mode="wait">
          {isScanning ? renderScanningView() : renderResultView()}
        </AnimatePresence>
      </div>
    </div>
  );
}