import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  Sliders, 
  DollarSign, 
  Clock, 
  Users, 
  MapPin,
  Settings,
  ToggleLeft,
  ToggleRight,
  Plus,
  Edit,
  Save,
  X,
  CheckCircle,
  AlertTriangle,
  Car,
  Utensils,
  Shield,
  Coffee,
  Star,
  TrendingUp,
  Calendar,
  User,
  Phone,
  MessageSquare
} from 'lucide-react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Slider } from './ui/slider';
import { Switch } from './ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Ava<PERSON>, AvatarFallback, AvatarImage } from './ui/avatar';
import { Textarea } from './ui/textarea';

interface Service {
  id: string;
  name: string;
  type: 'valet' | 'table' | 'upgrade' | 'special';
  basePrice: number;
  currentPrice: number;
  isAvailable: boolean;
  capacity: number;
  currentBookings: number;
  icon: any;
  description: string;
  features: string[];
  dynamicPricing: boolean;
  peakMultiplier: number;
}

interface StaffAssignment {
  id: string;
  taskId: string;
  taskType: 'valet' | 'service' | 'hosting' | 'security';
  staffId: string;
  staffName: string;
  staffRole: string;
  customerName: string;
  customerTier: 'standard' | 'premium' | 'vip';
  location: string;
  startTime: string;
  estimatedDuration: number;
  status: 'assigned' | 'in-progress' | 'completed' | 'delayed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  specialInstructions?: string;
}

interface AvailableStaff {
  id: string;
  name: string;
  avatar?: string;
  role: string;
  skills: string[];
  currentLocation: string;
  status: 'available' | 'busy' | 'break';
  rating: number;
  activeAssignments: number;
  maxAssignments: number;
}

export function ManagementPanel() {
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState<StaffAssignment | null>(null);

  // Mock services data
  const [services, setServices] = useState<Service[]>([
    {
      id: '1',
      name: 'Premium Valet Service',
      type: 'valet',
      basePrice: 25,
      currentPrice: 25,
      isAvailable: true,
      capacity: 8,
      currentBookings: 3,
      icon: Car,
      description: 'Professional valet parking with premium vehicle care',
      features: ['Insured', 'Real-time tracking', 'Premium locations'],
      dynamicPricing: true,
      peakMultiplier: 1.5
    },
    {
      id: '2',
      name: 'VIP Table Reservation',
      type: 'table',
      basePrice: 75,
      currentPrice: 85,
      isAvailable: true,
      capacity: 12,
      currentBookings: 8,
      icon: Utensils,
      description: 'Premium table reservations with priority service',
      features: ['Best views', 'Priority service', 'Complimentary appetizers'],
      dynamicPricing: true,
      peakMultiplier: 1.3
    },
    {
      id: '3',
      name: 'Table Upgrade',
      type: 'upgrade',
      basePrice: 30,
      currentPrice: 35,
      isAvailable: true,
      capacity: 20,
      currentBookings: 5,
      icon: Star,
      description: 'Upgrade to premium seating locations',
      features: ['Better views', 'More space', 'Enhanced service'],
      dynamicPricing: false,
      peakMultiplier: 1.0
    },
    {
      id: '4',
      name: 'Security Escort',
      type: 'special',
      basePrice: 15,
      currentPrice: 15,
      isAvailable: false,
      capacity: 4,
      currentBookings: 0,
      icon: Shield,
      description: 'Personal security escort service',
      features: ['Trained professionals', 'Discrete service', 'Emergency response'],
      dynamicPricing: false,
      peakMultiplier: 1.0
    }
  ]);

  // Mock staff assignments
  const [assignments, setAssignments] = useState<StaffAssignment[]>([
    {
      id: '1',
      taskId: 'VAL-001',
      taskType: 'valet',
      staffId: '1',
      staffName: 'James Rodriguez',
      staffRole: 'Valet Manager',
      customerName: 'Michael Chen',
      customerTier: 'vip',
      location: 'Front Entrance',
      startTime: '8:30 PM',
      estimatedDuration: 15,
      status: 'in-progress',
      priority: 'high',
      specialInstructions: 'BMW M5, license plate VIP-123. Handle with extra care.'
    },
    {
      id: '2',
      taskId: 'SVC-002',
      taskType: 'service',
      staffId: '3',
      staffName: 'Carlos Martinez',
      staffRole: 'Server',
      customerName: 'Sarah Mitchell',
      customerTier: 'premium',
      location: 'VIP Table 12',
      startTime: '8:45 PM',
      estimatedDuration: 30,
      status: 'assigned',
      priority: 'medium',
      specialInstructions: 'Anniversary celebration, prepare special dessert'
    },
    {
      id: '3',
      taskId: 'HOST-003',
      taskType: 'hosting',
      staffId: '4',
      staffName: 'Sophie Anderson',
      staffRole: 'Hostess',
      customerName: 'David Kim',
      customerTier: 'standard',
      location: 'Reception',
      startTime: '9:00 PM',
      estimatedDuration: 10,
      status: 'completed',
      priority: 'low'
    }
  ]);

  // Mock available staff
  const availableStaff: AvailableStaff[] = [
    {
      id: '1',
      name: 'James Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      role: 'Valet Manager',
      skills: ['Valet', 'Customer Service', 'Management'],
      currentLocation: 'Valet Stand',
      status: 'busy',
      rating: 4.9,
      activeAssignments: 1,
      maxAssignments: 3
    },
    {
      id: '2',
      name: 'Lisa Park',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      role: 'Floor Manager',
      skills: ['Management', 'Customer Service', 'Problem Solving'],
      currentLocation: 'Main Floor',
      status: 'available',
      rating: 4.8,
      activeAssignments: 0,
      maxAssignments: 5
    },
    {
      id: '3',
      name: 'Carlos Martinez',
      role: 'Server',
      skills: ['Service', 'Bartending', 'Food Safety'],
      currentLocation: 'Dining Area',
      status: 'busy',
      rating: 4.7,
      activeAssignments: 2,
      maxAssignments: 4
    },
    {
      id: '4',
      name: 'Sophie Anderson',
      avatar: 'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=150&h=150&fit=crop&crop=face',
      role: 'Hostess',
      skills: ['Customer Service', 'Reservations', 'Communication'],
      currentLocation: 'Reception',
      status: 'available',
      rating: 4.9,
      activeAssignments: 0,
      maxAssignments: 3
    }
  ];

  const updateServicePrice = (serviceId: string, newPrice: number) => {
    setServices(prev => prev.map(service => 
      service.id === serviceId ? { ...service, currentPrice: newPrice } : service
    ));
  };

  const toggleServiceAvailability = (serviceId: string) => {
    setServices(prev => prev.map(service => 
      service.id === serviceId ? { ...service, isAvailable: !service.isAvailable } : service
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'in-progress': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'completed': return 'bg-green-100 text-green-800 border-green-300';
      case 'delayed': return 'bg-red-100 text-red-800 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'vip': return 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white';
      case 'premium': return 'bg-gradient-to-r from-purple-500 to-blue-500 text-white';
      case 'standard': return 'bg-gray-200 text-gray-800';
      default: return 'bg-gray-200 text-gray-800';
    }
  };

  const getStaffStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'busy': return 'bg-orange-500';
      case 'break': return 'bg-yellow-500';
      default: return 'bg-gray-400';
    }
  };

  const assignStaffToTask = (staffId: string, taskId: string) => {
    // Implementation for smart assignment logic
    console.log(`Assigning staff ${staffId} to task ${taskId}`);
    setShowAssignDialog(false);
  };

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 overflow-auto">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Management Panel</h1>
            <p className="text-gray-600">Control pricing, availability, and staff assignments</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
            <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white">
              <TrendingUp className="w-4 h-4 mr-2" />
              Optimize All
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Sliders className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{services.filter(s => s.isAvailable).length}</p>
                <p className="text-sm text-gray-600">Active Services</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{availableStaff.filter(s => s.status === 'available').length}</p>
                <p className="text-sm text-gray-600">Available Staff</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{assignments.filter(a => a.status === 'in-progress').length}</p>
                <p className="text-sm text-gray-600">Active Tasks</p>
              </div>
            </div>
          </Card>

          <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <DollarSign className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">
                  ${services.reduce((sum, s) => sum + (s.currentPrice * s.currentBookings), 0)}
                </p>
                <p className="text-sm text-gray-600">Current Revenue</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="services" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-white/20 backdrop-blur-sm">
            <TabsTrigger value="services">Services & Pricing</TabsTrigger>
            <TabsTrigger value="assignments">Staff Assignments</TabsTrigger>
            <TabsTrigger value="automation">Smart Routing</TabsTrigger>
          </TabsList>

          <TabsContent value="services" className="space-y-6">
            {/* Services Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {services.map((service) => {
                const Icon = service.icon;
                const utilizationRate = (service.currentBookings / service.capacity) * 100;
                
                return (
                  <Card key={service.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-white/30 rounded-lg">
                          <Icon className="w-6 h-6 text-gray-700" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800">{service.name}</h3>
                          <p className="text-sm text-gray-600">{service.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch 
                          checked={service.isAvailable}
                          onCheckedChange={() => toggleServiceAvailability(service.id)}
                        />
                        <Badge className={service.isAvailable ? 'bg-green-100 text-green-800 border-0' : 'bg-red-100 text-red-800 border-0'}>
                          {service.isAvailable ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {/* Pricing Controls */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-700 block mb-2">
                            Current Price
                          </label>
                          <div className="flex items-center gap-2">
                            <span className="text-2xl font-bold text-gray-800">
                              ${service.currentPrice}
                            </span>
                            {service.currentPrice !== service.basePrice && (
                              <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                                Modified
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700 block mb-2">
                            Utilization
                          </label>
                          <div className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span>{service.currentBookings}/{service.capacity}</span>
                              <span>{utilizationRate.toFixed(0)}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  utilizationRate > 80 ? 'bg-red-500' :
                                  utilizationRate > 60 ? 'bg-yellow-500' : 'bg-green-500'
                                }`}
                                style={{ width: `${utilizationRate}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Dynamic Pricing */}
                      {service.dynamicPricing && (
                        <div className="p-3 bg-blue-50/60 rounded-lg border border-blue-200/60">
                          <div className="flex items-center gap-2 mb-2">
                            <TrendingUp className="w-4 h-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-800">Dynamic Pricing Active</span>
                          </div>
                          <p className="text-xs text-blue-700">
                            Peak multiplier: {service.peakMultiplier}x (Base: ${service.basePrice})
                          </p>
                        </div>
                      )}

                      {/* Price Adjustment */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 block mb-2">
                          Adjust Price
                        </label>
                        <div className="flex items-center gap-4">
                          <Slider
                            value={[service.currentPrice]}
                            onValueChange={(value) => updateServicePrice(service.id, value[0])}
                            max={service.basePrice * 2}
                            min={Math.floor(service.basePrice * 0.5)}
                            step={5}
                            className="flex-1"
                          />
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => updateServicePrice(service.id, service.basePrice)}
                            className="bg-white/20 border-white/30 hover:bg-white/30"
                          >
                            Reset
                          </Button>
                        </div>
                      </div>

                      {/* Features */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 block mb-2">
                          Features
                        </label>
                        <div className="flex flex-wrap gap-1">
                          {service.features.map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs bg-white/30 border-white/40">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-white/30">
                        <span className="text-sm text-gray-600">
                          Revenue Today: ${(service.currentPrice * service.currentBookings).toFixed(2)}
                        </span>
                        <Button size="sm" variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
                          <Edit className="w-4 h-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="assignments" className="space-y-6">
            {/* Assignment Actions */}
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800">Active Assignments</h3>
              <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
                <DialogTrigger asChild>
                  <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                    <Plus className="w-4 h-4 mr-2" />
                    New Assignment
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Create Staff Assignment</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Task Type</label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select task type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="valet">Valet Service</SelectItem>
                            <SelectItem value="service">Customer Service</SelectItem>
                            <SelectItem value="hosting">Hosting</SelectItem>
                            <SelectItem value="security">Security</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Priority</label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="urgent">Urgent</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Customer Name</label>
                      <Input placeholder="Enter customer name" />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Location</label>
                      <Input placeholder="e.g., Front Entrance, Table 12" />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Special Instructions</label>
                      <Textarea placeholder="Any special requirements or notes..." />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
                        Cancel
                      </Button>
                      <Button onClick={() => setShowAssignDialog(false)}>
                        Create Assignment
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Assignments List */}
            <div className="space-y-4">
              {assignments.map((assignment) => (
                <Card key={assignment.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getPriorityColor(assignment.priority)}`} />
                        <Badge variant="outline" className={getStatusColor(assignment.status)}>
                          {assignment.status.replace('-', ' ')}
                        </Badge>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-gray-800">{assignment.taskId}</h3>
                          <Badge className={getTierColor(assignment.customerTier)}>
                            {assignment.customerTier.toUpperCase()}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Customer</p>
                            <p className="font-medium text-gray-800">{assignment.customerName}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Assigned To</p>
                            <p className="font-medium text-gray-800">{assignment.staffName}</p>
                            <p className="text-xs text-gray-600">{assignment.staffRole}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Location</p>
                            <p className="text-gray-800">{assignment.location}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Duration</p>
                            <p className="text-gray-800">{assignment.estimatedDuration} min</p>
                          </div>
                        </div>
                        
                        {assignment.specialInstructions && (
                          <div className="mt-3 p-2 bg-yellow-50/60 rounded border border-yellow-200/60">
                            <p className="text-sm text-yellow-800">
                              <strong>Instructions:</strong> {assignment.specialInstructions}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
                        <MessageSquare className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
                        <Phone className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="outline" className="bg-white/20 border-white/30 hover:bg-white/30">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Available Staff */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Available Staff</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {availableStaff.map((staff) => (
                  <Card key={staff.id} className="backdrop-blur-xl bg-white/20 border-white/30 p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="relative">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={staff.avatar} />
                          <AvatarFallback>{staff.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStaffStatusColor(staff.status)}`} />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-800">{staff.name}</h4>
                        <p className="text-xs text-gray-600">{staff.role}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span className="text-gray-800">{staff.currentLocation}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Rating:</span>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-yellow-400 fill-current" />
                          <span className="text-gray-800">{staff.rating}</span>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Workload:</span>
                        <span className="text-gray-800">{staff.activeAssignments}/{staff.maxAssignments}</span>
                      </div>
                    </div>
                    
                    <Button 
                      size="sm" 
                      className="w-full mt-3" 
                      disabled={staff.status !== 'available'}
                      variant={staff.status === 'available' ? 'default' : 'outline'}
                    >
                      {staff.status === 'available' ? 'Assign Task' : 'Unavailable'}
                    </Button>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="automation" className="space-y-6">
            {/* Smart Routing Configuration */}
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">Automated Request Routing</h3>
                  <p className="text-gray-600">Configure intelligent staff assignment based on expertise and availability</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="p-4 bg-white/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Users className="w-5 h-5 text-blue-600" />
                    <h4 className="font-medium text-gray-800">Skill-Based Routing</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Automatically assign tasks to staff with relevant skills and highest ratings.
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Enabled</span>
                    <Switch defaultChecked />
                  </div>
                </div>

                <div className="p-4 bg-white/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <MapPin className="w-5 h-5 text-green-600" />
                    <h4 className="font-medium text-gray-800">Location Priority</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Prioritize staff members closest to the task location to reduce response time.
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Enabled</span>
                    <Switch defaultChecked />
                  </div>
                </div>

                <div className="p-4 bg-white/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Clock className="w-5 h-5 text-purple-600" />
                    <h4 className="font-medium text-gray-800">Workload Balance</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Distribute tasks evenly across available staff to prevent overload.
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Enabled</span>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>
            </Card>

            {/* Routing Rules */}
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Routing Rules</h3>
              
              <div className="space-y-4">
                <div className="p-4 border border-white/30 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-800">VIP Customer Priority</h4>
                    <Switch defaultChecked />
                  </div>
                  <p className="text-sm text-gray-600">
                    VIP customers automatically get the highest-rated available staff member.
                  </p>
                </div>

                <div className="p-4 border border-white/30 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-800">Valet Service Auto-Assignment</h4>
                    <Switch defaultChecked />
                  </div>
                  <p className="text-sm text-gray-600">
                    Valet requests are automatically assigned to available valet staff within 2 minutes.
                  </p>
                </div>

                <div className="p-4 border border-white/30 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-800">Emergency Override</h4>
                    <Switch />
                  </div>
                  <p className="text-sm text-gray-600">
                    Security and urgent requests override all other assignments and priorities.
                  </p>
                </div>
              </div>
            </Card>

            {/* Performance Metrics */}
            <Card className="backdrop-blur-xl bg-white/20 border-white/30 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Automation Performance</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-white/30 rounded-lg">
                  <p className="text-3xl font-bold text-gray-800">2.3min</p>
                  <p className="text-gray-600">Avg Assignment Time</p>
                  <p className="text-sm text-green-600">-15% vs manual</p>
                </div>
                <div className="text-center p-4 bg-white/30 rounded-lg">
                  <p className="text-3xl font-bold text-gray-800">94%</p>
                  <p className="text-gray-600">Optimal Assignments</p>
                  <p className="text-sm text-green-600">+12% efficiency</p>
                </div>
                <div className="text-center p-4 bg-white/30 rounded-lg">
                  <p className="text-3xl font-bold text-gray-800">4.8/5</p>
                  <p className="text-gray-600">Avg Staff Rating</p>
                  <p className="text-sm text-green-600">+0.3 improvement</p>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}